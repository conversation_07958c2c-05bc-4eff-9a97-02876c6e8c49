<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>音频播放器 (隐藏窗口)</title>
    <style>
        body {
            display: none;
        }
    </style>
</head>
<body>
<audio id="audio-element" preload="auto"></audio>

<script>
  const { ipcRenderer } = require('electron')
  const audioElement = document.getElementById('audio-element')
  audioElement.volume = 0.5

  // 获取音频总时长
  audioElement.addEventListener('loadedmetadata', () => {
    const duration = audioElement.duration
    ipcRenderer.send('audio-duration', duration)
  })

  // 监听播放命令
  ipcRenderer.on('play-audio', (event, { trackUrl, currentTime = 0 }) => {
    // 如果是同一首歌，使用保存的currentTime
    if (audioElement.src === trackUrl && currentTime === 0) {
      currentTime = audioElement.currentTime
    }
    if (audioElement.src !== trackUrl) {
      audioElement.src = trackUrl
    }
    audioElement.currentTime = currentTime
    audioElement.play().catch(err => {
      console.error('播放失败:', err)
    })
  })
  // 监听暂停命令
  ipcRenderer.on('pause-audio', () => {
    const currentTime = audioElement.currentTime
    audioElement.pause()

    // 向主进程发送当前时间
    ipcRenderer.send('update-time', currentTime)
  })

  let justReset = false
  // 清空列表重置进度条
  ipcRenderer.on('reset-audio', () => {
    audioElement.pause()
    audioElement.currentTime = 0
    justReset = true
    setTimeout(() => { justReset = false }, 1200) // 1.2秒后解锁
    ipcRenderer.send('update-time', 0)
  })

  // 监听跳转到指定位置命令
  ipcRenderer.on('seek-audio', (event, currentTime) => {
    audioElement.currentTime = currentTime

    // 如果当前是暂停状态但有新的播放位置，保持暂停状态
    if (!audioElement.paused) {
      audioElement.play().catch(err => {
        console.error('播放失败:', err)
      })
    }
  })
  // 监听音频音量设置
  ipcRenderer.on('set-volume', (event, volume) => {
    audioElement.volume = volume
  })
  // 监听音频结束事件
  audioElement.addEventListener('ended', () => {
    ipcRenderer.send('ended')
  })

  // 定期发送播放时间更新
  setInterval(() => {
    if (!audioElement.paused && !justReset) {
      console.log('interval: update-time', audioElement.currentTime)
      ipcRenderer.send('update-time', audioElement.currentTime)
    }
  }, 1000)

  // 保存状态到本地存储
  window.addEventListener('beforeunload', () => {
    ipcRenderer.send('update-time', audioElement.currentTime)
  })
</script>
</body>
</html>