// 测试数据库表结构
const Database = require('better-sqlite3')
const path = require('path')
const os = require('os')
const fs = require('fs')

// 获取数据库路径
function getDatabasePath() {
  const userDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'leigod')
  return path.join(userDataPath, 'music.db')
}

// 测试数据库表结构
function testDatabaseStructure() {
  const dbPath = getDatabasePath()
  
  console.log('=== 数据库表结构测试 ===\n')
  console.log('数据库路径:', dbPath)
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ 数据库文件不存在')
    console.log('请先启动应用以创建数据库')
    return
  }
  
  try {
    const db = new Database(dbPath, { readonly: true })
    
    // 检查所有表
    console.log('\n📋 数据库中的表:')
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all()
    tables.forEach(table => {
      console.log(`  - ${table.name}`)
    })
    
    // 检查favorites表结构
    console.log('\n❤️ favorites表结构:')
    try {
      const favoritesColumns = db.prepare("PRAGMA table_info(favorites)").all()
      if (favoritesColumns.length === 0) {
        console.log('❌ favorites表不存在')
      } else {
        console.log('✅ favorites表存在，列信息:')
        favoritesColumns.forEach(col => {
          console.log(`  - ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.pk ? 'PRIMARY KEY' : ''}`)
        })
        
        // 检查是否有musicrid列
        const hasMusicrid = favoritesColumns.some(col => col.name === 'musicrid')
        if (hasMusicrid) {
          console.log('✅ musicrid列存在')
        } else {
          console.log('❌ musicrid列不存在')
        }
      }
    } catch (error) {
      console.log('❌ 检查favorites表失败:', error.message)
    }
    
    // 检查索引
    console.log('\n📊 数据库索引:')
    const indexes = db.prepare("SELECT name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'").all()
    indexes.forEach(index => {
      console.log(`  - ${index.name}`)
    })
    
    // 测试favorites表操作
    console.log('\n🧪 测试favorites表操作:')
    try {
      // 测试插入
      const insertStmt = db.prepare(`
        INSERT INTO favorites (musicrid, name, artist, album, duration, cover, source)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `)
      
      const testData = {
        musicrid: 'test_' + Date.now(),
        name: '测试歌曲',
        artist: '测试艺术家',
        album: '测试专辑',
        duration: 180,
        cover: 'test_cover.jpg',
        source: 'online'
      }
      
      const result = insertStmt.run(
        testData.musicrid,
        testData.name,
        testData.artist,
        testData.album,
        testData.duration,
        testData.cover,
        testData.source
      )
      
      console.log('✅ 插入测试数据成功, ID:', result.lastInsertRowid)
      
      // 测试查询
      const selectStmt = db.prepare('SELECT * FROM favorites WHERE musicrid = ?')
      const selected = selectStmt.get(testData.musicrid)
      
      if (selected) {
        console.log('✅ 查询测试数据成功')
        console.log('  数据:', JSON.stringify(selected, null, 2))
      } else {
        console.log('❌ 查询测试数据失败')
      }
      
      // 清理测试数据
      const deleteStmt = db.prepare('DELETE FROM favorites WHERE musicrid = ?')
      deleteStmt.run(testData.musicrid)
      console.log('✅ 清理测试数据成功')
      
    } catch (error) {
      console.log('❌ favorites表操作测试失败:', error.message)
    }
    
    db.close()
    console.log('\n✅ 数据库测试完成')
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message)
  }
}

// 运行测试
if (require.main === module) {
  testDatabaseStructure()
}

module.exports = { testDatabaseStructure }
