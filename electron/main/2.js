const express = require('express');
const axios = require('axios');
const app = express();

app.get('/download', async (req, res) => {
  const url = req.query.url;
  if (!url) {
    return res.status(400).send('Missing url parameter');
  }

  try {
    const response = await axios.get(url, {
      responseType: 'stream',
      headers: {
        'Referer': 'https://bunkr.cr/',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      }
    });

    res.setHeader('Content-Disposition', 'attachment; filename=video.mp4');
    res.setHeader('Content-Type', 'video/mp4');
    response.data.pipe(res);
  } catch (err) {
    console.error('Axios error:', err);
    if (err.response) {
      res.status(err.response.status).send('Upstream error: ' + err.response.statusText);
    } else {
      res.status(500).send('Server error: ' + err.message);
    }
  }
});

const PORT = 3000;
app.listen(PORT, () => {
  console.log(`Download proxy server running at http://localhost:${PORT}`);
});


const path = require('path')
const fs = require('fs')
const mm = require('music-metadata')
const songPath = 'd:\\leigod\\downloads\\lrc'
const songPath2= 'd:\\leigod\\downloads\\ddd'
// console.log(fs.existsSync(songPath))
const songDir = path.resolve(songPath)
const songDir2 = path.resolve(songPath2)
console.log(songDir === songDir2, songDir)
// const songName = path.basename(songPath, path.extname(songPath))
// const lrcPath = path.join('D:\\leigod\\downloads\\lrc', `${songName}.lrc`)
