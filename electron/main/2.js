// 咪咕
const url1 = `https://api.cenguigui.cn/api/mg_music/?msg=%E8%B7%B3%E6%A5%BC%E6%9C%BA&n=1&type=json`

// 酷我
const url2 = `https://api.cenguigui.cn/api/kuwo/?rid=5960811&type=json&level=hires&lrc=false`

const migu = `https://pd.musicapp.migu.cn/MIGUM2.0/v1.0/content/search_all.do?&ua=Android_migu&version=5.0.1&text=周杰伦&pageNo=1&pageSize=10&searchSwitch=`

const jiexi = `https://api.xiaotuo.net/api/jiexi/api.php?apikey=755c4c08-aa3f-ccf1-f227-75b7f0ca6e6c02825a57&url=`

const { URL } = require('url')
const querystring = require('querystring')
const axios = require('axios')

class KugouSong {
  constructor() {
    this.source = 'kugou'
    this.id = ''
    this.title = ''
    this.singer = ''
    this.duration = 0
    this.album = ''
    this.size = 0
    this.hash = ''
  }
}

async function kugouApiRequest(url, method = 'GET', data = {}) {
  try {
    let res
    if (method === 'GET') {
      res = await axios.get(url, {
        params: data, headers: {
          'Referer': 'http://m.kugou.com',
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_1_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16D57 Kugou',
        }
      })
    } else {
      res = await axios.post(url, data)
    }
    return res.data
  } catch (err) {
    console.error('请求出错:', err.message)
    return null
  }
}

/**
 * 搜索歌曲
 * @param {string} keyword 关键词
 * @param {number} number 数量，默认5
 * @returns {Promise<KugouSong[]>}
 */
async function kugouSearch(keyword, number = 5) {
  const params = {
    keyword,
    platform: 'WebFilter',
    format: 'json',
    page: 1,
    pagesize: number,
  }

  const resData = await kugouApiRequest('http://songsearch.kugou.com/song_search_v2', 'GET', params)
  if (!resData || !resData.data || !resData.data.lists) return []

  const songsList = []

  for (const item of resData.data.lists) {
    const song = new KugouSong()
    song.id = item.Scid || ''
    song.title = item.SongName || ''
    song.singer = item.SingerName || ''
    song.duration = item.Duration || 0
    song.album = item.AlbumName || ''
    song.size = Math.round((item.FileSize || 0) / 1048576 * 100) / 100
    song.hash = item.FileHash || ''

    // 优先使用高品质hash
    const keysList = ['SQFileHash', 'HQFileHash']
    for (const key of keysList) {
      const h = item[key]
      if (h && h !== '00000000000000000000000000000000') {
        song.hash = h
        break
      }
    }

    songsList.push(song)
  }

  return songsList
}

/**
 * 分页获取歌单歌曲列表
 * @param {string} url 歌单链接
 * @returns {Promise<KugouSong[]>}
 */
async function kugouPlaylist(url) {
  const songsList = []

  try {
    // 先请求歌单页面，拿参数
    const res = await axios.get(url, {
      headers: {
        'Referer': 'http://m.kugou.com',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_1_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16D57 Kugou',
      }
    })
    const parsedUrl = new URL(res.request.res.responseUrl)
    const query = Object.fromEntries(parsedUrl.searchParams.entries())

    query.page = 1
    query.pagesize = 100 // 最大100

    // 请求歌曲列表接口
    const resList = await kugouApiRequest('https://m3ws.kugou.com/zlist/list', 'GET', query)
    if (!resList || !resList.list) return []

    let resData = resList.list.info || []
    const resCount = resList.list.count || 0
    const repeatCount = Math.floor(resCount / (query.page * query.pagesize))

    // 多页循环获取
    let currentPage = 1
    while (currentPage < repeatCount) {
      currentPage++
      query.page = currentPage
      const pageRes = await kugouApiRequest('https://m3ws.kugou.com/zlist/list', 'GET', query)
      if (pageRes && pageRes.list && pageRes.list.info) {
        resData = resData.concat(pageRes.list.info)
      } else {
        break
      }
    }

    // 解析歌曲数据
    for (const item of resData) {
      const song = new KugouSong()
      song.id = item.fileid || ''
      const singerTitle = item.name ? item.name.split(' - ') : ['', '']
      song.singer = singerTitle[0] || ''
      song.title = singerTitle[1] || ''
      song.duration = item.timelen ? Math.floor(item.timelen / 1000) : 0
      song.album = item.album_id || ''
      song.size = Math.round((item.size || 0) / 1048576 * 100) / 100
      song.hash = item.hash || ''
      songsList.push(song)
    }
  } catch (err) {
    console.error('获取歌单失败:', err.message)
  }

  return songsList
}

// (async () => {
//   const songs = await kugouSearch('秋风', 5)
//   console.log(songs)
//
//   // const playlistSongs = await kugouPlaylist('歌单链接')
//   // console.log(playlistSongs)
// })()

// const filePath = 'D:\\leigod\\downloads\\周杰伦 - 不能说的秘密.mp3'
// const mm = require('music-metadata')
// mm.parseFile(filePath).then(res => {
//   console.log(res.common)
// }).catch(err => {
//   console.error(err)
// })

// 依赖：axios、crypto-js
