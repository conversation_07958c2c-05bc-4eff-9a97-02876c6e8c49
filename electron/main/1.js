// import axios from 'axios'
// import { HttpsProxyAgent } from 'https-proxy-agent'

// const proxy = 'http://38.156.74.147:10820' // 替换为你的代理地址
// const agent = new HttpsProxyAgent(proxy)
// const url = 'http://mobi.kuwo.cn/mobi.s?f=web&source=jiakong&type=convert_url_with_sign&rid=228908'
// const postData = { act: 'search', key: 'q', lang: '' }
// axios.post('https://zz123.com/ajax/', postData, {
//   httpsAgent: agent,
//   headers: {
//     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
//     'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
//     'Referer': `https://zz123.com/search/?key=${encodeURIComponent(postData.key)}`,
//   }
// }).then(response =>
//   console.log(response.data)
// )
//   .catch(err => {
//     console.error('搜索请求失败:', err)
//   })

// axios.get('https://www.kuwo.cn/search/searchMusicBykeyWord?vipver=1&client=kt&ft=music&cluster=0&strategy=2012&encoding=utf8&rformat=json&mobi=1&issubtitle=1&show_copyright_off=1&pn=0&rn=20&all=%E6%99%B4%E5%A4%A9', {
//   headers: {
//     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
//     'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
//     'Referer': `https://www.kuwo.cn/search/list?key=${encodeURIComponent('晴天')}`,
//   }
// }).then(response =>
//   console.log(response.data)
// )
//   .catch(err => {
//     console.error('搜索请求失败:', err)
//   })

const m = 'Hm_Iuvt_cdb524f42f23cer9b268564v7y735ewrq2324'
// import { wrapper } from 'axios-cookiejar-support'
// import { CookieJar } from 'tough-cookie'

const url1 = 'https://www.kuwo.cn/'
const url2 = 'https://www.kuwo.cn/api/www/playlist/playListInfo?pid=1082685104&pn=1&rn=30&httpsStatus=1&plat=web_www&from='

const jar = new CookieJar()
const client = wrapper(axios.create({ jar, withCredentials: true }))

// 请求拦截
client.interceptors.request.use(config => {
  config.headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
  config.headers['Content-Type'] = 'application/json; charset=UTF-8'
  return config
}, error => {
  return Promise.reject(error)
})

async function fetchWithFreshCookie(url, options = {}) {
  // 第一步：预请求获取 cookie
  const res = await client.get(url1, { ...options })
  const cookie = res.headers['set-cookie'][0]
  const value = cookie.match(/=(.*?);/)[1]

  function f(t, e) {
    if (null == e || e.length <= 0)
      return null
    for (var n = '', i = 0; i < e.length; i++)
      n += e.charCodeAt(i).toString()
    var o = Math.floor(n.length / 5)
      , r = parseInt(n.charAt(o) + n.charAt(2 * o) + n.charAt(3 * o) + n.charAt(4 * o) + n.charAt(5 * o))
      , c = Math.ceil(e.length / 2)
      , l = Math.pow(2, 31) - 1
    if (r < 2)
      return null
    var d = Math.round(1e9 * Math.random()) % 1e8
    for (n += d; n.length > 10;)
      n = (parseInt(n.substring(0, 10)) + parseInt(n.substring(10, n.length))).toString()
    n = (r * n + c) % l
    var f = ''
      , h = ''
    for (i = 0; i < t.length; i++)
      h += (f = parseInt(t.charCodeAt(i) ^ Math.floor(n / l * 255))) < 16 ? '0' + f.toString(16) : f.toString(16),
        n = (r * n + c) % l
    for (d = d.toString(16); d.length < 8;)
      d = '0' + d
    return h += d
  }

  // 第二步：带上新 cookie 正式请求
  const response = await client.get(url2, {
    headers: {
      'Secret': f(value, m)
    }
  })
  return response.data
}

const express = require('express')
const axios = require('axios')
const app = express()

app.get('/download', async (req, res) => {
  const url = req.query.url
  if (!url) {
    return res.status(400).send('Missing url parameter')
  }

  try {
    // 用 axios 请求视频流，设置 responseType 为 stream
    const response = await axios.get(url, {
      responseType: 'stream',
      headers: {
        'Referer': 'https://bunkr.ru/',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      }
    })

    // 设置下载响应头
    res.setHeader('Content-Disposition', 'attachment; filename=video.mp4')
    res.setHeader('Content-Type', 'video/mp4')

    // 通过管道方式转发视频流
    response.data.pipe(res)
  } catch (err) {
    res.status(500).send('Server error: ' + err.message)
  }
})

const PORT = 3000
app.listen(PORT, () => {
  console.log(`Download proxy server running at http://localhost:${PORT}`)
})
