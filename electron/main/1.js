const axios = require('axios')

async function miguSearch(keyword, number = 5) {
  const url = 'http://pd.musicapp.migu.cn/MIGUM2.0/v1.0/content/search_all.do'
  const params = {
    ua: 'Android_migu',
    version: '5.0.1',
    text: keyword,
    pageNo: 1,
    pageSize: number,
    searchSwitch: JSON.stringify({
      song: 1,
      album: 0,
      singer: 0,
      tagSong: 0,
      mvSong: 0,
      songlist: 0,
      bestShow: 1,
    }),
  }

  const headers = {
    Referer: 'http://music.migu.cn/',
    'User-Agent':
      'Mozilla/5.0 (iPhone; CPU iPhone OS 12_1_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16D57 Migu',
  }

  try {
    const res = await axios.get(url, { params, headers })
    const results = res.data?.songResultData?.result

    const songsList = []

    for (const item of results) {
      const singers = (item.singers || []).map((s) => s.name)
      const song = {
        source: 'MIGU',
        id: item.id,
        title: item.name,
        singer: singers.join('、'),
        album: (item.albums && item.albums[0]?.name),
        cover_url: (item.imgItems && item.imgItems[0]?.img),
        lyrics_url: item.lyricUrl || item.trcUrl,
        content_id: item.contentId,
        song_url: '',
        size: 0,
        ext: '',
      }

      // rateFormats按size降序排序
      const rateList = (item.rateFormats || []).sort((a, b) => {
        return (parseInt(b.size) || 0) - (parseInt(a.size) || 0)
      })

      for (const rate of rateList) {
        // 拼接播放地址
        song.song_url = `http://app.pd.nf.migu.cn/MIGUM2.0/v1.0/content/sub/listenSong.do?toneFlag=${rate.formatType || 'SQ'}&netType=00&userId=15548614588710179085055&ua=Android_migu&version=5.1&copyrightId=0&contentId=${song.content_id}&resourceType=${rate.resourceType || 'E'}&channel=0`

      //   if (song.song_url) {
      //     song.size = Math.round((parseInt(rate.size || 0) / 1048576) * 100) / 100 // 转MB，保留2位
      //     song.ext = rate.fileType || (rate.formatType === 'SQ' ? 'flac' : 'mp3')
      //     break // 找到最高质量就退出
      //   }
      }

      songsList.push(song)
    }

    return songsList
  } catch (error) {
    console.error('咪咕搜索出错:', error.message)
    return []
  }
}

(async () => {
  const songs = await miguSearch('瞬', 5)
  console.log(songs)
})()

const a = {
  id: '3790007',
  resourceType: '2',
  contentId: '600902000006889366',
  copyrightId: '60054701923',
  name: '晴天',
  highlightStr: ['晴天'],
  singers: [{ id: '112', name: '周杰伦' }],
  albums: [{ id: '8592', name: '叶惠美', type: '1' }],
  tags: [
    '运营推荐巴黎奥运2024', '快手',
    '测试6', '寂寞',
    '网络热歌', '录音室版',
    '周杰伦', '原创单曲',
    '中国台湾', '抖音',
    '流行', '爱情',
    '原创', '国语',
    '伤感', 'KTV金曲',
    '黄昏', '青春校园',
    '00年代', '抒情流行',
    '每周新增播放top10', '每日播放1w至99999',
    '累积收藏10w以上', '累积评论1000至9999',
    '累积播放1000w以上', '怀旧',
    '失恋', '下雨天',
    '回忆'
  ],
  lyricUrl: 'https://d.musicapp.migu.cn/data/oss/resource/00/2c/w4/21',
  trcUrl: '',
  imgItems: [
    {
      imgSizeType: '01',
      img: 'https://d.musicapp.migu.cn/data/oss/resource/00/4t/9y/a64e69c4d8c54f59b580b1a25ae5ceb3.webp'
    },
    {
      imgSizeType: '02',
      img: 'https://d.musicapp.migu.cn/data/oss/resource/00/4t/9y/b604231d05474ddfb7a48a094cb63e37.webp'
    },
    {
      imgSizeType: '03',
      img: 'https://d.musicapp.migu.cn/data/oss/resource/00/4t/9y/679feb591df148ddbc1e98110cd108de.webp'
    }
  ],
  televisionNames: [''],
  tones: [
    {
      id: '600902000006889364',
      copyrightId: '60054701923',
      price: '200',
      expireDate: '2025-12-31'
    }
  ],
  mvList: [
    {
      id: '600906000000388230',
      copyrightId: '600547Y0112',
      resourceType: 'D',
      price: '300',
      expireDate: '2025-12-31',
      mvPicUrl: [Array],
      playNum: '142393256',
      mvType: '0'
    }
  ],
  relatedSongs: [
    {
      resourceType: 'E',
      resourceTypeName: '无损',
      copyrightId: '60054701923',
      productId: '600902000006889366'
    },
    {
      resourceType: '1',
      resourceTypeName: '振铃',
      copyrightId: '60054701923',
      productId: '600902000006889365'
    },
    {
      resourceType: '0',
      resourceTypeName: '彩铃',
      copyrightId: '60054701923',
      productId: '600902000006889364'
    },
    {
      resourceType: '3',
      resourceTypeName: '随身听',
      copyrightId: '60054701923',
      productId: '600902000006889367'
    },
    {
      resourceType: 'D',
      resourceTypeName: '视频',
      copyrightId: '600547Y0311',
      productId: '600906000000389695'
    }
  ],
  toneControl: '111100',
  rateFormats: [
    {
      resourceType: '3',
      formatType: 'LQ',
      format: '000019',
      size: '2158842',
      fileType: 'mp3',
      price: '200'
    },
    {
      resourceType: '2',
      formatType: 'PQ',
      format: '020007',
      size: '4317311',
      fileType: 'mp3',
      price: '200',
      showTag: [Array]
    },
    {
      resourceType: '2',
      formatType: 'HQ',
      format: '020010',
      size: '10792962',
      fileType: 'mp3',
      price: '200',
      showTag: [Array]
    },
    {
      resourceType: 'E',
      formatType: 'SQ',
      format: '011002',
      size: '31529675',
      price: '200',
      androidFileType: 'flac',
      iosFileType: 'm4a',
      iosSize: '31931278',
      androidSize: '31529675',
      iosFormat: '011003',
      androidFormat: '011002',
      iosAccuracyLevel: '16bit',
      androidAccuracyLevel: '16bit',
      showTag: [Array]
    }
  ],
  newRateFormats: [
    {
      resourceType: '2',
      formatType: 'PQ',
      format: '020007',
      size: '4317311',
      fileType: 'mp3',
      price: '200',
      showTag: [Array]
    },
    {
      resourceType: '2',
      formatType: 'HQ',
      format: '020010',
      size: '10792962',
      fileType: 'mp3',
      price: '200',
      showTag: [Array]
    },
    {
      resourceType: 'E',
      formatType: 'SQ',
      format: '011002',
      size: '31529675',
      price: '200',
      androidFileType: 'flac',
      iosFileType: 'm4a',
      iosSize: '31931278',
      androidSize: '31529675',
      iosFormat: '011003',
      androidFormat: '011002',
      iosAccuracyLevel: '16bit',
      androidAccuracyLevel: '16bit',
      showTag: [Array]
    }
  ],
  z3dCode: {
    resourceType: '2',
    formatType: 'Z3D',
    format: '020026',
    size: '10584078',
    fileType: 'wav',
    price: '200',
    androidFileType: 'wav',
    iosFileType: 'm4a',
    iosSize: '11603556',
    androidSize: '71380604',
    iosFormat: '020025',
    androidFormat: '020024',
    androidFileKey: '34D1317A40FADE1C352CA45189903CC2',
    iosFileKey: '4B435C9BDE6B40F6F199830E1AE7C982',
    h5Size: '10584078',
    h5Format: '020026',
    showTag: ['vip']
  },
  songType: '01',
  isInDAlbum: '0',
  copyright: '1',
  digitalColumnId: '',
  mrcurl: 'https://d.musicapp.migu.cn/data/oss/resource/00/1x/yx/vz',
  songDescs: '',
  songAliasName: 'Sunny Day',
  translateName: '',
  invalidateDate: '2025-12-31',
  isInSalesPeriod: '0',
  dalbumId: '',
  isInSideDalbum: '0',
  vipType: '1',
  chargeAuditions: '1',
  scopeOfcopyright: '01',
  mvCopyright: '01'
}

const API = 'https://app.c.nf.migu.cn/MIGUM2.0/strategy/listen-url/v2.4'

// 你习惯的“音质名” → toneFlag 映射（自行增删别名）
function toToneFlag(q) {
  const s = String(q || '').toUpperCase()
  // 已知枚举：LQ | PQ | HQ | SQ | ZQ | Z3D | ZQ24 | ZQ32
  if (['LQ', 'PQ', 'HQ', 'SQ', 'ZQ', 'Z3D', 'ZQ24', 'ZQ32'].includes(s)) return s
  // 常见别名
  switch (s) {
    case 'HI_RES':
    case 'HIRES':
    case 'HIREZ':
      return 'ZQ'
    case 'HIRES24':
    case 'ZQ_24':
      return 'ZQ24'
    case 'HIRES32':
    case 'ZQ_32':
      return 'ZQ32'
    case 'LOSSLESS':
    case 'FLAC':
    case 'SQ':
      return 'SQ'
    case 'SUPER':
    case 'HIGH':
    case 'HQ':
      return 'HQ'
    case 'LOW':
    case 'LQ':
      return 'LQ'
    default:
      return 'PQ'
  }
}

/**
 * 取咪咕播放直链
 * @param {string} songId   咪咕歌曲 ID
 * @param {string} quality  例如 'ZQ'|'SQ'|'HQ'|'PQ'|'Z3D'|'ZQ24'|'ZQ32' 或别名
 */
async function getMiguTrackUrl(songId, quality = 'PQ') {
  const toneFlag = toToneFlag(quality)
  const url = `${API}?songId=${encodeURIComponent(songId)}&toneFlag=${toneFlag}&resourceType=2`

  const { data } = await axios.get(url, {
    headers: {
      // 按你 Java 里的头保持一致
      'User-Agent': 'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36 Chrome/124.0 Mobile Safari/537.36',
      'aversionid': '',
      'token': '',           // 若需要登录/授权可在此填入
      'channel': '0146832',
      'language': 'Chinese',
      'ua': 'Android_migu',
      'mode': 'android',
      'os': 'Android 10',
      'Accept': 'application/json',
      'Referer': 'https://m.music.migu.cn/v3' // 可选
    },
    timeout: 15000
  })

  // 成功 code 通常为 '000000'，不同环境可能是 number 0
  const code = data?.code
  if (!(code === '000000' || code === 0)) {
    throw new Error(`migu error: ${code} ${data?.info || data?.message || ''}`)
  }
  console.log(data)
  // 返回字段有环境差异，常见 data.url / data.playUrl / data.listenUrl
  const u = data?.data?.url || data?.data?.playUrl || data?.data?.listenUrl
  if (!u) throw new Error('未返回播放地址')
  return u
}

// 示例
// ;(async () => {
//   // 例：HQ（高品）/ SQ（无损）/ ZQ（Hi-Res）/ ZQ24 / ZQ32 / Z3D / PQ 等
//   const songId = '1140192289' // 替换为真实咪咕 songId
//   const url = await getMiguTrackUrl(songId, 'HQ')
//   console.log('play url:', url)
// })().catch(console.error)

// 网页里的 B[r] 映射：128k→PQ，320k→HQ，flac→SQ，flac24bit→ZQ
const TONE_MAP = { '128k': 'PQ', '320k': 'HQ', 'flac': 'SQ', 'flac24bit': 'ZQ' }

async function mgMusicUrl({ copyrightId, quality = '128k', token, aversionid, channel = '014000D' }) {
  const toneFlag = TONE_MAP[quality]
  if (!toneFlag) throw new Error('quality 仅支持 128k/320k/flac/flac24bit')

  const url = `https://app.c.nf.migu.cn/MIGUM2.0/strategy/listen-url/v2.4` +
    `?netType=01&resourceType=E&songId=${encodeURIComponent(copyrightId)}` +
    `&toneFlag=${toneFlag}`

  const { data } = await axios.get(url, {
    headers: {
      channel,
      token,        // 必填：网页里用到的 token（有时效）
      aversionid,   // 必填：网页里用到的 aversionid（有时效）
      'User-Agent': 'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36 Chrome/124.0 Mobile Safari/537.36',
      'Accept': 'application/json'
    },
    timeout: 15000,
    validateStatus: () => true
  })

  // 接口成功一般 code 为 '000000'
  if (data?.code !== '000000' && data?.code !== 0) {
    throw new Error(`migu error: ${data?.code || ''} ${data?.info || data?.message || ''}`)
  }

  let play = data?.data?.url
  if (!play) throw new Error('未返回播放地址')

  // 与网页逻辑保持一致：补 https、+ 号转义、去掉查询串
  if (play.startsWith('//')) play = 'https:' + play
  play = play.replace(/\+/g, '%2B').split('?')[0]

  return play
}

// 示例
// (async () => {
//   const url = await mgMusicUrl({
//     copyrightId: '1140192289',
//     quality: 'flac',
//     token: '848401000134020058524459344E544130526A4932517A55344D7A56434E55453240687474703A2F2F70617373706F72742E6D6967752E636E2F6E303030312F4062393662376634326336326434303935393366666433366434313939393033300300040298EAFB0400063232303032340500164D4759355A4463784D324E684E4449324F57566B4E51FF0020795263B9A333A4580E13DD7F28820A8B9788F30062F6025FA08BF10CC5A8AA04',
//     aversionid: 'DF94898993A5A28A64968A9FD0ADA0749397878BC39DD7BC68C584A1BAAFC96EC5938D8D8ED1A490949A8F9EB680997296DFD0D391D6ABBC69928AD0B57D99779CC8B88CDDECEE89628F89A1827E986F94978AD392A7A2916A928AA4878199779C'
//   })
//   console.log(url)
// })()