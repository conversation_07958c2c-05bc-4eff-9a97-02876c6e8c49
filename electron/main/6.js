const axios = require('axios')

const u = 'wk/5I5Zh8QhbTmUht/C8thICYSCbKbM694a590oOQhwaK1jnjdvdHHzjIILR5hERizbx670uZcRNLrZROkUAL8N2ZK7oAAs213Ti97veTwzo28O/7IpTQlVEW7yT65F4rBi2Gjv3be/9dNvY0ZaKihSyj+acwc+xczOCEdfZHI/tKLDSCr1fJnNRmvfPV9GDE6zyXcWk0d0voFZhxV5amKRkJOCcYHi7mG+Pwaf5Jy6sIuhDC27FKn+qHDxAPUGXER0o7mdVQDM+qPwDS9SMYmmSRmw9i8oEPnMVhoVHJ6nTnJxzpybtSVcuPSbA6GxUVaqP7/UT/VvkCA5UGF/dOt1Y5PrTHMKf6dr2dG08aBZyj0kQURwD6fEP2zcxpOMbM0rXOjoiSSBreQcQmyKOSu+sh453tnlDL9uyK7zXkGeUo+gtIrrjutNmxedfNygKsAR6sBkwDonaCXx4vpFVuZq1hQ5Nk5+N04jMhzSv0nKvdYHlcEZ103zdMXVJYXqLR3hg+Iw4Z34EfFXBUlQOz3amzt/Hoklq7dBrg7Hs6z5XpFxvHpenOBuqBV5gNBXfPf0TNkxpEhmj2vSXjHY+xVdUxoN/v9O5Nf5jJm1i2L4HTyEG3sGKjssTDlEuDwBuhN2q1xBhxmlqUAnwGFXzL/eQjciFvHf8yVrGdyYVIkeB5clxQXFY5yuG2j7EuGy5v1tuZZZhonaPCNqWfWz2uhr/ncDpJbEfrMkbMGpIAHg='

const url = 'wk/5I5Zh8QhbTmUht/C8thICYSCbKbM694a590oOQhwaK1jnjdvdHHzjIILR5hERizbx670uZcRNLrZROkUAL8N2ZK7oAAs2jGgELWfRAL3fMb8XHWpDUz8ZT2trpof41FQsjXQ7U688RRDj1YtbMJEBxG9SqEB2iz2ZBcU9BMn7bGPplZRJzWWyivh18SiEamWH3sxYhKTr9F83imsIOIr3WWPsqawvkTwJyykRwCJHY9hvb/DTcMhAUZEEDu+Fc8X6+TCwLG28/yalP0x2p/0G3JwQqjD/af16jDmXUlFhxOqHBltIfUvN1CoMsqxhPuC0eK7MgCQ7MOlrLvVTT2l3k25Jvl4IaN1BFBhO5ZAP9j+Qlqs0NgHk+owoatB9zK0ozb2PDAw1WZwZDvO7PhEpZgO19e1BRdXfNlLfKuoYeuTAqByEG994F2KGqFG9GrZSc4AWPP2x2h6Sl3ZdIBNjmzfI31lh04jMhzSv0nKvdYHlcEZ103zdMXVJYXqLR3hg+Iw4Z34EfFXBUlQOz3amzt/Hoklq7dBrg7Hs6z5XpFxvHpenOBuqBV5gNBXfPf0TNkxpEhmj2vSXjHY+xVdUxoN/v9O5Nf5jJm1i2L4HTyEG3sGKjssTDlEuDwBuhN2q1xBhxmlqUAnwGFXzL/eQjciFvHf8yVrGdyYVIkfdwVKFfMJh6k5fNL7wJOozQeJ4dnZOBMoAt0FhxUyjkMaMYkcWMUDkxAX8/MO0WoE='

// axios.get('https://m.kugou.com/app/i/getSongInfo.php?hash=cccaa0cbe3ef852eafc819c2345c2be4&cmd=playInfo', {
//   headers: {
//     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
//   }
// }).then(res => {
//   const s = res.data.album_img
//   const f = s.replace('{size}', 240)
//   console.log(f)
// })

// const url = `https://krcs.kugou.com/search?ver=1&man=no&client=pc&hash=${hash}`


// 🔹测试：用你的超长字符串


