/**
 * @name 野草🌾
 * @version 1.0.0
 */
function Z(Y,L){const K=O();return Z=function(U,H){U=U-(-0x42*0x37+-0x4*-0x21e+0x704);let S=K[U];return S;},Z(Y,L);}function O(){const n=['\x63\x6f\x70\x79\x72\x69\x67\x68\x74\x49','\x69\x6e\x69\x74\x65\x64','\x35\x53\x41\x56\x79\x6d\x5a','\x69\x63\x73\x2e\x74\x6b\x2f\x76\x31','\u670d\u52a1\u5668\u5f02\u5e38','\x63\x72\x79\x70\x74\x6f','\x53\x4f\x53\x4c\x6d','\x47\x45\x54','\x35\x32\x30\x34\x30\x32\x38\x6c\x59\x4e\x54\x76\x7a','\x6d\x75\x73\x69\x63','\x4a\x71\x48\x59\x51','\x73\x6f\x6e\x67\x6d\x69\x64','\x39\x31\x37\x31\x34\x33\x38\x55\x53\x59\x70\x77\x52','\x69\x63\x73\x2e\x74\x6b\x2f\x76\x31\x2f','\x74\x48\x42\x63\x6f','\x42\x43\x6b\x53\x46','\x70\x54\x70\x4e\x6c','\x6c\x76\x7a\x44\x71','\x62\x75\x66\x54\x6f\x53\x74\x72\x69\x6e','\x32\x32\x31\x32\x39\x33\x36\x63\x54\x74\x59\x4a\x61','\x67\x44\x69\x63\x7a','\x68\x74\x74\x70\x3a\x2f\x2f\x67\x72\x61','\x74\x55\x75\x70\x54','\x66\x72\x6f\x6d','\x65\x57\x54\x61\x70','\x73\x68\x69\x66\x74','\x75\x70\x64\x61\x74\x65\x41\x6c\x65\x72','\x2f\x75\x72\x6c\x2f','\x76\x65\x72\x73\x69\x6f\x6e','\x47\x44\x6d\x59\x70','\x73\x73\x2e\x74\x65\x6d\x70\x6d\x75\x73','\x72\x65\x71\x75\x65\x73\x74','\x32\x30\x56\x54\x67\x61\x57\x6d','\x57\x72\x58\x55\x4e','\x75\x72\x6c\x69\x6e\x66\x6f\x2f','\x72\x61\x77\x53\x63\x72\x69\x70\x74','\x74\x72\x69\x6d','\x74\x61\x67','\x73\x70\x6c\x69\x74','\x68\x65\x78','\x38\x35\x33\x32\x30\x58\x59\x41\x55\x43\x58','\x57\x71\x54\x59\x55','\x32\x36\x31\x6a\x76\x41\x43\x70\x73','\x50\x6b\x63\x5a\x4c','\x6d\x75\x73\x69\x63\x55\x72\x6c','\x68\x61\x73\x68','\x64\x61\x74\x61','\x56\x76\x47\x70\x5a','\x6d\x73\x67','\x34\x36\x32\x35\x33\x31\x46\x62\x4b\x66\x4f\x64','\x73\x74\x72\x69\x6e\x67\x69\x66\x79','\x53\x48\x76\x46\x71','\x62\x75\x66\x66\x65\x72','\x6d\x61\x74\x63\x68','\x66\x61\x69\x6c\x65\x64','\x42\x54\x4a\x75\x6f','\x62\x6f\x64\x79','\x7a\x4f\x64\x77\x68','\x6c\x78\x2d\x6d\x75\x73\x69\x63\x2f','\x4d\x59\x64\x72\x70','\x66\x6b\x6b\x6c\x54','\x31\x55\x48\x48\x71\x43\x59','\x66\x69\x61\x6c\x65\x64','\x33\x31\x30\x36\x33\x30\x32\x56\x5a\x62\x47\x73\x68','\x63\x6f\x64\x65','\x73\x6f\x75\x72\x63\x65\x73','\x6b\x77\x7c\x31\x32\x38\x6b','\x70\x6d\x79\x45\x64','\x6d\x64\x35','\x49\x46\x6b\x53\x52','\x36\x30\x32\x37\x38\x35\x34\x4d\x4b\x76\x54\x48\x63'];O=function(){return n;};return O();}const k=Z;(function(Y,L){const P={Y:0x17f,L:0x181,K:0x173,U:'\x30\x78\x31\x39\x31',H:0x18b,S:'\x30\x78\x31\x34\x65',N:'\x30\x78\x31\x38\x38',B:0x16a,v:'\x30\x78\x31\x36\x63',G:0x162,M:0x155},A=Z,K=Y();while(!![]){try{const U=-parseInt(A(P.Y))/(0x1*-0xc89+0x1*-0x74f+0x13d9)*(-parseInt(A(P.L))/(0x15f5+0x100f+0x3cd*-0xa))+parseInt(A(P.K))/(-0xe41+-0x17f5+-0x203*-0x13)+-parseInt(A(P.U))/(-0x2*-0x139+-0x21f*0x9+0x10a9)+parseInt(A(P.H))/(-0x1*0xa7b+0x1*-0x1bdd+-0x1ab*-0x17)*(parseInt(A(P.S))/(-0x766+-0x7a5+0x1d*0x85))+-parseInt(A(P.N))/(0x1095+-0xaf8*-0x2+-0x267e*0x1)+parseInt(A(P.B))/(-0x22f3+0x18e*0x9+0x14fd)*(parseInt(A(P.v))/(0x4f4*0x1+-0x1279*-0x2+-0x5fb*0x7))+parseInt(A(P.G))/(-0xcb6+-0x1*0x1bb3+-0x817*-0x5)*(-parseInt(A(P.M))/(-0x1*-0x26d+-0x1*-0x260a+-0x286c));if(U===L)break;else K['push'](K['shift']());}catch(H){K['push'](K['shift']());}}}(O,-0x1*0x188843+-0x9728b+0x30f1af));const {EVENT_NAMES:e,request:t,on:r,send:s,env:o,version:d,currentScriptInfo:i,utils:a}=globalThis['\x6c\x78'],getId=(Y,L)=>{const w={Y:'\x30\x78\x31\x37\x38',L:'\x30\x78\x31\x39\x34',K:0x16f,U:0x189,H:'\x30\x78\x31\x35\x33',S:'\x30\x78\x31\x36\x64'},I=Z,K={'\x6c\x76\x7a\x44\x71':function(U,H){return U(H);},'\x50\x6b\x63\x5a\x4c':I(w.Y)};switch(Y){case'\x74\x78':case'\x77\x79':case'\x6b\x77':return L[I(w.L)];case'\x6b\x67':return L[I(w.K)];case'\x6d\x67':return L[I(w.U)+'\x64'];}throw K[I(w.H)](Error,K[I(w.S)]);},headers={'\x55\x73\x65\x72\x2d\x41\x67\x65\x6e\x74':k('\x30\x78\x31\x37\x63')+o,'\x76\x65\x72':d,'\x73\x6f\x75\x72\x63\x65\x2d\x76\x65\x72':i[k(0x15e)]};r(e[k('\x30\x78\x31\x36\x31')],({source:Y,action:L,info:{musicInfo:K,type:U}})=>{const J={Y:0x169,L:0x157,K:0x160,U:'\x30\x78\x31\x38\x63',H:0x190,S:0x16e,N:0x180,B:0x17d,v:0x171,G:0x150,M:0x152},g={Y:'\x30\x78\x31\x35\x64',L:0x151,K:0x167,U:'\x30\x78\x31\x37\x36',H:0x154,S:0x176,N:'\x30\x78\x31\x35\x39',B:'\x30\x78\x31\x37\x34',v:0x177,G:'\x30\x78\x31\x37\x35',M:'\x30\x78\x31\x35\x36',T:'\x30\x78\x31\x36\x33',E:0x179,J:'\x30\x78\x31\x37\x65'},x=k,H={'\x42\x43\x6b\x53\x46':function(S,N,B){return S(N,B);},'\x53\x48\x76\x46\x71':x(J.Y),'\x67\x44\x69\x63\x7a':function(S,N,B,v){return S(N,B,v);},'\x57\x72\x58\x55\x4e':function(S,N){return S+N;},'\x42\x54\x4a\x75\x6f':x(J.L)+x(J.K)+x(J.U),'\x66\x6b\x6b\x6c\x54':x(J.H),'\x4d\x59\x64\x72\x70':function(S,N){return S!=N;},'\x56\x76\x47\x70\x5a':x(J.S),'\x74\x48\x42\x63\x6f':function(S,N){return S(N);},'\x70\x54\x70\x4e\x6c':x(J.N)};if(H[x(J.B)](H[x(J.v)],L))throw H[x(J.G)](Error,H[x(J.M)]);return new Promise((S,N)=>{const c=x;let B=c(g.Y)+Y+'\x2f'+H[c(g.L)](getId,Y,K)+'\x2f'+U;headers[c(g.K)]=a[c(g.U)][c(g.H)+'\x67'](a[c(g.S)][c(g.N)](JSON[c(g.B)](B[c(g.v)](/(?:\d\w)+/g),null,0x4fb+-0x44*-0x1f+-0x2*0x69b)),H[c(g.G)]),H[c(g.M)](t,H[c(g.T)](H[c(g.E)],B),{'\x6d\x65\x74\x68\x6f\x64':H[c(g.J)],'\x68\x65\x61\x64\x65\x72\x73':headers},(v,G)=>v?N(v):-0x13eb*-0x1+0x1*0x11c9+-0x25b4!==G[c('\x30\x78\x31\x37\x61')][c(0x182)]?N(Error(G[c('\x30\x78\x31\x37\x61')][c('\x30\x78\x31\x37\x32')])):void S(G[c('\x30\x78\x31\x37\x61')][c(0x170)]));});}),t(k(0x157)+k('\x30\x78\x31\x36\x30')+k('\x30\x78\x31\x34\x66')+k(0x164)+i[k(0x15e)],{'\x6d\x65\x74\x68\x6f\x64':k(0x190),'\x68\x65\x61\x64\x65\x72\x73':headers},(U,H)=>{const R={Y:0x184,L:0x18d,K:'\x30\x78\x31\x39\x32',U:'\x30\x78\x31\x36\x65',H:0x182,S:0x16b,N:'\x30\x78\x31\x37\x61',B:'\x30\x78\x31\x37\x62',v:0x17a,G:0x182,M:'\x30\x78\x31\x35\x38',T:0x18e,E:0x186,n:0x165,b:0x166,q:'\x30\x78\x31\x37\x61',X:'\x30\x78\x31\x39\x33',D:0x172,V:'\x30\x78\x31\x38\x35',h:0x166,O0:'\x30\x78\x31\x36\x38',O1:0x168,O2:0x15b,O3:'\x30\x78\x31\x35\x66',O4:0x18f,O5:0x183,O6:'\x30\x78\x31\x35\x61',O7:0x18a,O8:'\x30\x78\x31\x37\x61',O9:'\x30\x78\x31\x38\x37',OO:'\x30\x78\x31\x35\x63',OZ:0x17a},F=k,S={'\x57\x71\x54\x59\x55':F(R.Y),'\x7a\x4f\x64\x77\x68':function(M,T){return M!==T;},'\x74\x55\x75\x70\x54':function(M,T){return M!=T;},'\x4a\x71\x48\x59\x51':function(M,T){return M(T);},'\x70\x6d\x79\x45\x64':F(R.L),'\x47\x44\x6d\x59\x70':F(R.K),'\x53\x4f\x53\x4c\x6d':F(R.U),'\x65\x57\x54\x61\x70':function(M,T,E){return M(T,E);},'\x49\x46\x6b\x53\x52':function(M,T,E){return M(T,E);}},N={};N[F(R.H)]=0x0,N['\x73']=S[F(R.S)];const B={};B[F(R.N)]=N;if(U&&(H=B),S[F(R.B)](-0x19f8+0x1c51*-0x1+0x3649*0x1,H[F(R.v)][F(R.G)])||H[F(R.v)]['\x6d']&&S[F(R.M)](a[F(R.T)][F(R.E)](i[F(R.n)][F(R.b)]()),H[F(R.q)]['\x6d']))throw S[F(R.X)](Error,H[F(R.v)][F(R.D)]??S[F(R.V)]);let v={};for(let M of H[F(R.q)]['\x73'][F(R.h)]()[F(R.O0)]('\x26'))v[(M=M[F(R.O1)]('\x7c'))[F(R.O2)]()]={'\x74\x79\x70\x65':S[F(R.O3)],'\x61\x63\x74\x69\x6f\x6e\x73':[S[F(R.O4)]],'\x71\x75\x61\x6c\x69\x74\x79\x73':M};const G={};G[F(R.O5)]=v,(S[F(R.O6)](s,e[F(R.O7)],G),H[F(R.O8)]['\x75']&&S[F(R.O9)](s,e[F(R.OO)+'\x74'],{'\x6c\x6f\x67':H[F(R.OZ)]['\x75'],'\x75\x70\x64\x61\x74\x65\x55\x72\x6c':H[F(R.O8)]['\x68']}));});
