const CryptoJS = require('crypto-js')
const axios = require('axios')

const UA = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0 Safari/537.36'

var chrsz = 8
var hexcase = 0
var sign = function (a) {
  var b = CryptoJS.MD5(a)
  var c = CryptoJS.enc.Utf8.parse(b)
  var d = CryptoJS.enc.Utf8.parse('https://t.me/xmflv666')
  var e = CryptoJS.AES.encrypt(a, c, {
    iv: d,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.ZeroPadding
  })
  return e.toString()
}
function binl2hex(binarray) {
  var hex_tab = (() => {
    let _temp_ternary427
    if (hexcase) {_temp_ternary427 = '0123456789ABCDEF'} else {_temp_ternary427 = '0123456789abcdef'}
    return _temp_ternary427
  })()
  var str = ''
  for (var i = 0; i < binarray.length * 4; i++) {str += hex_tab.charAt(binarray[i >> 2] >> i % 4 * 8 + 4 & 15) + hex_tab.charAt(binarray[i >> 2] >> i % 4 * 8 & 15)}
  return str
}
function str2binl(str) {
  var bin = Array()
  var mask = (1 << chrsz) - 1
  for (var i = 0; i < str.length * chrsz; i += chrsz) bin[i >> 5] |= (str.charCodeAt(i / chrsz) & mask) << i % 32
  return bin
}
function hex_md5(s) {return binl2hex(core_md5(str2binl(s), s.length * chrsz))}
function md5_cmn(q, a, b, x, s, t) {return safe_add(bit_rol(safe_add(safe_add(a, q), safe_add(x, t)), s), b)}
function safe_add(x, y) {
  var lsw = (x & 65535) + (y & 65535)
  var msw = (x >> 16) + (y >> 16) + (lsw >> 16)
  return msw << 16 | lsw & 65535
}
function md5_ff(a, b, c, d, x, s, t) {return md5_cmn(b & c | ~b & d, a, b, x, s, t)}
function bit_rol(num, cnt) {return num << cnt | num >>> 32 - cnt}
function md5_gg(a, b, c, d, x, s, t) {return md5_cmn(b & d | c & ~d, a, b, x, s, t)}
function md5_hh(a, b, c, d, x, s, t) {return md5_cmn(b ^ c ^ d, a, b, x, s, t)}
function md5_ii(a, b, c, d, x, s, t) {return md5_cmn(c ^ (b | ~d), a, b, x, s, t)}
function core_md5(x, len) {
  x[len >> 5] |= 128 << len % 32
  x[(len + 64 >>> 9 << 4) + 14] = len
  var a = 1732584193
  var b = -271733879
  var c = -1732584194
  var d = 271733878
  for (var i = 0; i < x.length; i += 16) {
    var olda = a
    var oldb = b
    var oldc = c
    var oldd = d
    a = md5_ff(a, b, c, d, x[i + 0], 7, -680876936)
    d = md5_ff(d, a, b, c, x[i + 1], 12, -389564586)
    c = md5_ff(c, d, a, b, x[i + 2], 17, 606105819)
    b = md5_ff(b, c, d, a, x[i + 3], 22, -1044525330)
    a = md5_ff(a, b, c, d, x[i + 4], 7, -176418897)
    d = md5_ff(d, a, b, c, x[i + 5], 12, 1200080426)
    c = md5_ff(c, d, a, b, x[i + 6], 17, -1473231341)
    b = md5_ff(b, c, d, a, x[i + 7], 22, -45705983)
    a = md5_ff(a, b, c, d, x[i + 8], 7, 1770035416)
    d = md5_ff(d, a, b, c, x[i + 9], 12, -1958414417)
    c = md5_ff(c, d, a, b, x[i + 10], 17, -42063)
    b = md5_ff(b, c, d, a, x[i + 11], 22, -1990404162)
    a = md5_ff(a, b, c, d, x[i + 12], 7, 1804603682)
    d = md5_ff(d, a, b, c, x[i + 13], 12, -40341101)
    c = md5_ff(c, d, a, b, x[i + 14], 17, -1502002290)
    b = md5_ff(b, c, d, a, x[i + 15], 22, 1236535329)
    a = md5_gg(a, b, c, d, x[i + 1], 5, -165796510)
    d = md5_gg(d, a, b, c, x[i + 6], 9, -1069501632)
    c = md5_gg(c, d, a, b, x[i + 11], 14, 643717713)
    b = md5_gg(b, c, d, a, x[i + 0], 20, -373897302)
    a = md5_gg(a, b, c, d, x[i + 5], 5, -701558691)
    d = md5_gg(d, a, b, c, x[i + 10], 9, 38016083)
    c = md5_gg(c, d, a, b, x[i + 15], 14, -660478335)
    b = md5_gg(b, c, d, a, x[i + 4], 20, -405537848)
    a = md5_gg(a, b, c, d, x[i + 9], 5, 568446438)
    d = md5_gg(d, a, b, c, x[i + 14], 9, -1019803690)
    c = md5_gg(c, d, a, b, x[i + 3], 14, -187363961)
    b = md5_gg(b, c, d, a, x[i + 8], 20, 1163531501)
    a = md5_gg(a, b, c, d, x[i + 13], 5, -1444681467)
    d = md5_gg(d, a, b, c, x[i + 2], 9, -51403784)
    c = md5_gg(c, d, a, b, x[i + 7], 14, 1735328473)
    b = md5_gg(b, c, d, a, x[i + 12], 20, -1926607734)
    a = md5_hh(a, b, c, d, x[i + 5], 4, -378558)
    d = md5_hh(d, a, b, c, x[i + 8], 11, -2022574463)
    c = md5_hh(c, d, a, b, x[i + 11], 16, 1839030562)
    b = md5_hh(b, c, d, a, x[i + 14], 23, -35309556)
    a = md5_hh(a, b, c, d, x[i + 1], 4, -1530992060)
    d = md5_hh(d, a, b, c, x[i + 4], 11, 1272893353)
    c = md5_hh(c, d, a, b, x[i + 7], 16, -155497632)
    b = md5_hh(b, c, d, a, x[i + 10], 23, -1094730640)
    a = md5_hh(a, b, c, d, x[i + 13], 4, 681279174)
    d = md5_hh(d, a, b, c, x[i + 0], 11, -358537222)
    c = md5_hh(c, d, a, b, x[i + 3], 16, -722521979)
    b = md5_hh(b, c, d, a, x[i + 6], 23, 76029189)
    a = md5_hh(a, b, c, d, x[i + 9], 4, -640364487)
    d = md5_hh(d, a, b, c, x[i + 12], 11, -421815835)
    c = md5_hh(c, d, a, b, x[i + 15], 16, 530742520)
    b = md5_hh(b, c, d, a, x[i + 2], 23, -995338651)
    a = md5_ii(a, b, c, d, x[i + 0], 6, -198630844)
    d = md5_ii(d, a, b, c, x[i + 7], 10, 1126891415)
    c = md5_ii(c, d, a, b, x[i + 14], 15, -1416354905)
    b = md5_ii(b, c, d, a, x[i + 5], 21, -57434055)
    a = md5_ii(a, b, c, d, x[i + 12], 6, 1700485571)
    d = md5_ii(d, a, b, c, x[i + 3], 10, -1894986606)
    c = md5_ii(c, d, a, b, x[i + 10], 15, -1051523)
    b = md5_ii(b, c, d, a, x[i + 1], 21, -2054922799)
    a = md5_ii(a, b, c, d, x[i + 8], 6, 1873313359)
    d = md5_ii(d, a, b, c, x[i + 15], 10, -30611744)
    c = md5_ii(c, d, a, b, x[i + 6], 15, -1560198380)
    b = md5_ii(b, c, d, a, x[i + 13], 21, 1309151649)
    a = md5_ii(a, b, c, d, x[i + 4], 6, -145523070)
    d = md5_ii(d, a, b, c, x[i + 11], 10, -1120210379)
    c = md5_ii(c, d, a, b, x[i + 2], 15, 718787259)
    b = md5_ii(b, c, d, a, x[i + 9], 21, -343485551)
    a = safe_add(a, olda)
    b = safe_add(b, oldb)
    c = safe_add(c, oldc)
    d = safe_add(d, oldd)
  }
  return Array(a, b, c, d)
}
function aesDecryptBase64(cipherBase64, keyUtf8, ivUtf8) {
  if (!cipherBase64) return ''
  const bytes = CryptoJS.AES.decrypt(cipherBase64, CryptoJS.enc.Utf8.parse(keyUtf8), {
    iv: CryptoJS.enc.Utf8.parse(ivUtf8),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  return CryptoJS.enc.Utf8.stringify(bytes)
}
function detectWapByUA(ua = '') {
  return /iPad|iPhone|Android|Linux|iPod/i.test(ua) ? 1 : 0
}

/**
 * 仅使用外部提供的 sign 与 hex_md5
 * @param {Object} opts
 * @param {string} opts.pageUrl - 待解析页面URL（可带@前缀，如 @https://www.iqiyi.com/v_xxx.html）
 * @param {Function} opts.sign - 你的 sign 函数
 * @param {Function} opts.hex_md5 - 你的 hex_md5 函数
 * @param {string} [opts.userAgent]
 */
async function getIqiyiDirectUrl({ pageUrl, sign, hex_md5, userAgent = UA }) {
  const sourceUrl = String(pageUrl || '').replace(/^@/, '')
  const wap = detectWapByUA(userAgent)

  // 1) 获取 time/area
  const iqHeaders = {
    'User-Agent': userAgent,
    'Origin': 'https://jx.xmflv.com',
    'Accept': 'application/json, text/javascript, */*; q=0.01',
    'X-Requested-With': 'XMLHttpRequest',
    'Accept-Language': 'zh-CN,zh;q=0.9'
  }
  const { data: parms } = await axios.get('https://data.video.iqiyi.com/v.f4v', {
    headers: iqHeaders,
    timeout: 6000
  })
  const area = parms?.t
  const time = parms?.time
  if (!area || !time) throw new Error('获取 iqiyi time/area 失败')

  // 2) 计算 key（严格使用原始URL参与签名）
  const key = sign(hex_md5(String(time) + sourceUrl))

  // 3) 表单体（让 URLSearchParams 自动编码，不手动 encodeURIComponent）
  const form = new URLSearchParams()
  form.set('wap', String(wap))
  form.set('url', sourceUrl)
  form.set('time', String(time))
  form.set('key', key)
  form.set('area', String(area))
  const body = form.toString()

  const makeHeaders = (origin) => ({
    'User-Agent': userAgent,
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'Accept': 'application/json, text/javascript, */*; q=0.01',
    'X-Requested-With': 'XMLHttpRequest',
    'Origin': 'https://jx.xmflv.com',
    'Accept-Language': 'zh-CN,zh;q=0.9'
  })

  // 4) 主域 + 备用域回退
  let resp
  try {
    resp = await post('https://*************/Api.js', body, {
      headers: makeHeaders('https://*************'),
      timeout: 6000
    })
  } catch {
    resp = await axios.post('https://cache.hls.one/xmflv.js', body, {
      headers: makeHeaders('https://cache.hls.one'),
      timeout: 6000
    })
  }

  const data = resp?.data
  console.log(data)
  if (!data || data.code !== 200) throw new Error(data?.msg || '接口请求失败')

  // 5) 解密直链
  const aesKey = data.aes_key
  const aesIv  = data.aes_iv
  const vurl   = aesDecryptBase64(data.url,  aesKey, aesIv)
  const next   = aesDecryptBase64(data.next, aesKey, aesIv)
  const html   = aesDecryptBase64(data.html, aesKey, aesIv)
  const type   = data.type

  if (!vurl) throw new Error('解密直链失败')
  return { vurl, next, type, html, aesKey, aesIv, area, time }
}

// 使用示例：
(async () => {
  const ret = await getIqiyiDirectUrl({
    pageUrl: 'https://www.iqiyi.com/v_28b3s5tlqlw.html',
    sign, hex_md5
  })
  console.log(ret.vurl, ret.type)
})()