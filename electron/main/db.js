const path = require('path')
const fs = require('fs')
const Database = require('better-sqlite3')
const { app } = require('electron')

const DB_DIR = path.join((app && app.getPath && app.getPath('userData')) || process.cwd(), 'data')
const DB_PATH = path.join(DB_DIR, 'leigod.db')

function ensureDir(p) {
  if (!fs.existsSync(p)) fs.mkdirSync(p, { recursive: true })
}

let db = null
let stmts = {} // 预编译语句缓存

function initDB() {
  ensureDir(DB_DIR)
  db = new Database(DB_PATH)
  db.pragma('journal_mode = WAL')
  db.pragma('synchronous = NORMAL')

  db.exec(`
      CREATE TABLE IF NOT EXISTS songs
      (
          id         INTEGER PRIMARY KEY AUTOINCREMENT,
          musicrid   TEXT UNIQUE,
          hash       TEXT UNIQUE,
          path       TEXT UNIQUE,
          name       TEXT,
          artist     TEXT,
          album      TEXT,
          duration   REAL                                                                  DEFAULT 0,
          cover      TEXT,
          localCover TEXT,
          filename   TEXT,
          source     TEXT CHECK (source IN ('downloaded', 'imported', 'local', 'scanned')) DEFAULT 'local',
          created_at INTEGER                                                               DEFAULT (CAST(strftime('%s', 'now') AS INTEGER) * 1000),
          updated_at INTEGER                                                               DEFAULT (CAST(strftime('%s', 'now') AS INTEGER) * 1000)
      );

      CREATE TABLE IF NOT EXISTS failed_cover_hashes
      (
          id         INTEGER PRIMARY KEY AUTOINCREMENT,
          hash       TEXT UNIQUE,
          reason     TEXT,
          created_at INTEGER DEFAULT (CAST(strftime('%s', 'now') AS INTEGER) * 1000)
      );

      CREATE TABLE IF NOT EXISTS failed_lyric_hashes
      (
          id         INTEGER PRIMARY KEY AUTOINCREMENT,
          hash       TEXT UNIQUE,
          reason     TEXT,
          created_at INTEGER DEFAULT (CAST(strftime('%s', 'now') AS INTEGER) * 1000)
      );

      CREATE TABLE IF NOT EXISTS playlists
      (
          id         INTEGER PRIMARY KEY AUTOINCREMENT,
          name       TEXT UNIQUE NOT NULL,
          created_at DATETIME DEFAULT (CAST(strftime('%s', 'now') AS INTEGER) * 1000)
      );

      CREATE TABLE IF NOT EXISTS playlist_items
      (
          id          INTEGER PRIMARY KEY AUTOINCREMENT,
          playlist_id INTEGER NOT NULL,
          song_id     INTEGER NOT NULL,
          position    INTEGER NOT NULL,
          created_at  DATETIME DEFAULT (CAST(strftime('%s', 'now') AS INTEGER) * 1000),
          UNIQUE (playlist_id, song_id),
          FOREIGN KEY (playlist_id) REFERENCES playlists (id) ON DELETE CASCADE,
          FOREIGN KEY (song_id) REFERENCES songs (id) ON DELETE CASCADE
      );

      CREATE TABLE IF NOT EXISTS favorites
      (
          id         INTEGER PRIMARY KEY AUTOINCREMENT,
          musicrid   TEXT NOT NULL,
          name       TEXT,
          artist     TEXT,
          album      TEXT,
          duration   REAL DEFAULT 0,
          cover      TEXT,
          source     TEXT CHECK (source IN ('online', 'local')) DEFAULT 'online',
          created_at INTEGER DEFAULT (CAST(strftime('%s', 'now') AS INTEGER) * 1000),
          UNIQUE (musicrid)
      );

      CREATE TABLE IF NOT EXISTS lyrics
      (
          id         INTEGER PRIMARY KEY AUTOINCREMENT,
          song_id    INTEGER UNIQUE NOT NULL,
          lrc        TEXT,
          updated_at DATETIME DEFAULT (CAST(strftime('%s', 'now') AS INTEGER) * 1000),
          FOREIGN KEY (song_id) REFERENCES songs (id) ON DELETE CASCADE
      );

      -- 创建索引
      CREATE INDEX IF NOT EXISTS idx_songs_hash ON songs (hash);
      CREATE INDEX IF NOT EXISTS idx_songs_path ON songs (path);
      CREATE INDEX IF NOT EXISTS idx_failed_cover_hash ON failed_cover_hashes (hash);
      CREATE INDEX IF NOT EXISTS idx_failed_lyric_hash ON failed_lyric_hashes (hash);
      CREATE INDEX IF NOT EXISTS idx_favorites_musicrid ON favorites (musicrid);
      CREATE INDEX IF NOT EXISTS idx_favorites_created_at ON favorites (created_at);
  `)

  // 迁移：补充 localCover 列（老库无该列时添加）
  try {
    const cols = db.prepare('PRAGMA table_info(songs)').all()
    if (!cols.some(c => c.name === 'localCover')) {
      db.exec('ALTER TABLE songs ADD COLUMN localCover TEXT;')
    }
  } catch {}

  // 预编译语句
  stmts.upsert_song_hash = db.prepare(`
      INSERT INTO songs (hash, path, name, artist, album, duration, cover, localCover, filename, musicrid, source,
                         created_at, updated_at)
      VALUES (@hash, @path, @NAME, @ARTIST, @ALBUM, COALESCE(@DURATION, 0), @cover, @localCover, @filename, @MUSICRID,
              COALESCE(@source, 'local'), CAST(strftime('%s', 'now') AS INTEGER) * 1000,
              CAST(strftime('%s', 'now') AS INTEGER) * 1000)
      ON CONFLICT(hash) DO UPDATE SET path=COALESCE(excluded.path, songs.path),
                                      name=COALESCE(excluded.name, songs.name),
                                      artist=COALESCE(excluded.artist, songs.artist),
                                      album=COALESCE(excluded.album, songs.album),
                                      duration=COALESCE(excluded.duration, songs.duration),
                                      cover=COALESCE(excluded.cover, songs.cover),
                                      localCover=COALESCE(excluded.localCover, songs.localCover),
                                      filename=COALESCE(excluded.filename, songs.filename),
                                      musicrid=COALESCE(excluded.musicrid, songs.musicrid),
                                      source=COALESCE(excluded.source, songs.source),
                                      updated_at=CAST(strftime('%s', 'now') AS INTEGER) * 1000;
  `)

  stmts.upsert_song_path = db.prepare(`
      INSERT INTO songs (hash, path, name, artist, album, duration, cover, localCover, filename, musicrid, source,
                         created_at, updated_at)
      VALUES (NULL, @path, @NAME, @ARTIST, @ALBUM, COALESCE(@DURATION, 0), @cover, @localCover, @filename, @MUSICRID,
              COALESCE(@source, 'local'), CAST(strftime('%s', 'now') AS INTEGER) * 1000,
              CAST(strftime('%s', 'now') AS INTEGER) * 1000)
      ON CONFLICT(path) DO UPDATE SET name=COALESCE(excluded.name, songs.name),
                                      artist=COALESCE(excluded.artist, songs.artist),
                                      album=COALESCE(excluded.album, songs.album),
                                      duration=COALESCE(excluded.duration, songs.duration),
                                      cover=COALESCE(excluded.cover, songs.cover),
                                      localCover=COALESCE(excluded.localCover, songs.localCover),
                                      filename=COALESCE(excluded.filename, songs.filename),
                                      musicrid=COALESCE(excluded.musicrid, songs.musicrid),
                                      source=COALESCE(excluded.source, songs.source),
                                      updated_at=CAST(strftime('%s', 'now') AS INTEGER) * 1000;
  `)

  // 返回所有歌曲记录（包括可能没有本地 path 的条目），由调用方决定是否可播放
  stmts.list_songs = db.prepare(`
      SELECT id,
             hash,
             path,
             name     as NAME,
             artist   as ARTIST,
             album    as ALBUM,
             duration as DURATION,
             cover,
             localCover,
             filename,
             source,
             musicrid as MUSICRID,
             created_at
      FROM songs
      ORDER BY created_at DESC
  `)

  stmts.lyrics_upsert = db.prepare(`
      INSERT INTO lyrics (song_id, lrc, updated_at)
      VALUES (?, ?, datetime('now'))
      ON CONFLICT(song_id) DO UPDATE SET lrc=excluded.lrc,
                                         updated_at=CAST(strftime('%s', 'now') AS INTEGER) * 1000
  `)
  stmts.lyrics_get = db.prepare(`SELECT lrc
                                 FROM lyrics
                                 WHERE song_id = ?`)

  // 收藏相关操作
  stmts.add_favorite = db.prepare(`
      INSERT INTO favorites (musicrid, name, artist, album, duration, cover, source, created_at)
      VALUES (@musicrid, @name, @artist, @album, @duration, @cover, @source, CAST(strftime('%s', 'now') AS INTEGER) * 1000)
      ON CONFLICT(musicrid) DO NOTHING
  `)

  stmts.remove_favorite = db.prepare(`
      DELETE FROM favorites WHERE musicrid = ?
  `)

  stmts.list_favorites = db.prepare(`
      SELECT musicrid, name, artist, album, duration, cover, source, created_at
      FROM favorites
      ORDER BY created_at DESC
  `)

  stmts.check_favorite = db.prepare(`
      SELECT 1 FROM favorites WHERE musicrid = ?
  `)
  // 删除歌曲（按路径）
  stmts.delete_song_by_path = db.prepare(`DELETE
                                          FROM songs
                                          WHERE path = ?`)

  // 按哈希更新歌曲路径（可将 path 设为 NULL 表示丢失）
  stmts.update_song_path_by_hash = db.prepare(`
      UPDATE songs
      SET path       = ?,
          filename   = COALESCE(?, filename),
          updated_at = CAST(strftime('%s', 'now') AS INTEGER) * 1000
      WHERE hash = ?
  `)

  // 将指定 path 对应的记录的 path 置为 NULL（保留记录，不删除）
  stmts.nullify_path_by_path = db.prepare(`
      UPDATE songs
      SET path       = NULL,
          filename   = NULL,
          updated_at = CAST(strftime('%s', 'now') AS INTEGER) * 1000
      WHERE path = ?
  `)

  // 记录没有图片的hash
  stmts.record_failed_cover_hash = db.prepare(`
      INSERT OR
      REPLACE INTO failed_cover_hashes (hash, reason, created_at)
      VALUES (?, ?, CAST(strftime('%s', 'now') AS INTEGER) * 1000)
  `)

  // 查询没有图片的hash
  stmts.get_failed_cover_hash = db.prepare(`
      SELECT *
      FROM failed_cover_hashes
      WHERE hash = ?
  `)

  // 清理过期的记录（比如30天前的）
  stmts.cleanup_failed_cover_hashes = db.prepare(`
      DELETE
      FROM failed_cover_hashes
      WHERE created_at < CAST(strftime('%s', 'now') AS INTEGER) * 1000 - 30 * 24 * 60 * 60 * 1000
  `)

  // 记录没有歌词的hash
  stmts.record_failed_lyric_hash = db.prepare(`
      INSERT OR
      REPLACE INTO failed_lyric_hashes (hash, reason, created_at)
      VALUES (?, ?, CAST(strftime('%s', 'now') AS INTEGER) * 1000)
  `)

  // 查询没有歌词的hash
  stmts.get_failed_lyric_hash = db.prepare(`
      SELECT *
      FROM failed_lyric_hashes
      WHERE hash = ?
  `)

  // 清理过期的歌词失败记录（比如30天前的）
  stmts.cleanup_failed_lyric_hashes = db.prepare(`
      DELETE
      FROM failed_lyric_hashes
      WHERE created_at < CAST(strftime('%s', 'now') AS INTEGER) * 1000 - 30 * 24 * 60 * 60 * 1000
  `)

  return db
}

function getDB() {
  if (!db) initDB()
  return db
}

function normalizeSong(song) {
  return {
    // identifiers
    hash: song?.hash ?? null,
    path: song?.path ?? null,
    MUSICRID: song?.MUSICRID ?? null,
    // metadata
    NAME: song?.NAME ?? null,
    ARTIST: song?.ARTIST ?? '',
    ALBUM: song?.ALBUM ?? '',
    DURATION: Number(song?.DURATION ?? 0),
    cover: song?.cover ?? null,
    localCover: song?.localCover ?? null,
    filename: song?.filename ?? (song?.path ? path.basename(song.path) : null),
    source: song?.source ?? 'local'
  }
}

function upsertSong(song) {
  getDB()
  const row = normalizeSong(song)
  if (row.hash) return stmts.upsert_song_hash.run(row)
  return stmts.upsert_song_path.run(row)
}

function bulkUpsertSongs(songs = []) {
  const d = getDB()
  const trx = d.transaction((rows) => {
    for (const s of rows) {
      const row = normalizeSong(s)
      if (row.hash) stmts.upsert_song_hash.run(row)
      else stmts.upsert_song_path.run(row)
    }
  })
  trx(songs)
}

function listSongs() {
  getDB()
  return stmts.list_songs.all()
}

function deleteSongsByPaths(paths = []) {
  const d = getDB()
  const trx = d.transaction(arr => {
    for (const p of arr) {
      if (p) stmts.delete_song_by_path.run(p)
    }
  })
  trx(paths)
}

function upsertLyricsBySongId(songId, lrc) {
  getDB()
  return stmts.lyrics_upsert.run(songId, lrc)
}

function getLyricsBySongId(songId) {
  getDB()
  return stmts.lyrics_get.get(songId)
}

// 收藏相关函数
function addFavorite(songInfo) {
  getDB()
  const data = {
    musicrid: songInfo.MUSICRID,
    name: songInfo.NAME || songInfo.SONGNAME,
    artist: songInfo.ARTIST || '',
    album: songInfo.ALBUM || '',
    duration: songInfo.DURATION || 0,
    cover: songInfo.cover || '',
    source: songInfo.source || 'online'
  }
  return stmts.add_favorite.run(data)
}

function removeFavorite(musicrid) {
  getDB()
  return stmts.remove_favorite.run(musicrid)
}

function listFavorites() {
  getDB()
  return stmts.list_favorites.all()
}

function isFavorite(musicrid) {
  getDB()
  const result = stmts.check_favorite.get(musicrid)
  return !!result
}

// 根据哈希查询歌曲
function getSongByHash(hash) {
  getDB()
  try {
    const stmt = db.prepare(`
        SELECT id,
               hash,
               path,
               name     as NAME,
               artist   as ARTIST,
               album    as ALBUM,
               duration as DURATION,
               cover,
               filename,
               source,
               musicrid as MUSICRID,
               updated_at
        FROM songs
        WHERE hash = ?
        LIMIT 1
    `)
    return stmt.get(hash) || null
  } catch {
    return null
  }
}

// 仅按哈希更新封面路径
function updateSongCoverByHash(hash, coverPath) {
  getDB()
  const stmt = db.prepare(`
      UPDATE songs
      SET cover      = ?,
          updated_at = CAST(strftime('%s', 'now') AS INTEGER) * 1000
      WHERE hash = ?
  `)
  return stmt.run(coverPath, hash)
}

// 仅按哈希更新本地封面路径
function updateSongLocalCoverByHash(hash, localPath) {
  getDB()
  const stmt = db.prepare(`
      UPDATE songs
      SET localCover = ?,
          updated_at = CAST(strftime('%s', 'now') AS INTEGER) * 1000
      WHERE hash = ?
  `)
  return stmt.run(localPath, hash)
}

// 注意：stmts.update_song_path_by_hash 在 initDB 中已创建，这里不应重复赋值以避免在 require 时访问 null

function updateSongPathByHash(hash, newPath, filename) {
  getDB()
  return stmts.update_song_path_by_hash.run(newPath, filename || null, hash)
}

// 注意：stmts.nullify_path_by_path 在 initDB 中已创建，这里不应重复赋值以避免在 require 时访问 null

function nullifySongPathByPath(targetPath) {
  getDB()
  return stmts.nullify_path_by_path.run(targetPath)
}

// 移除失败记录相关的方法和导出
function recordFailedCoverHash(hash, reason) {
  getDB()
  return stmts.record_failed_cover_hash.run(hash, reason)
}

function getFailedCoverHash(hash) {
  getDB()
  return stmts.get_failed_cover_hash.get(hash)
}

function cleanupFailedCoverHashes() {
  getDB()
  return stmts.cleanup_failed_cover_hashes.run()
}

// 歌词相关的数据库方法
function recordFailedLyricHash(hash, reason) {
  getDB()
  return stmts.record_failed_lyric_hash.run(hash, reason)
}

function getFailedLyricHash(hash) {
  getDB()
  return stmts.get_failed_lyric_hash.get(hash)
}

function cleanupFailedLyricHashes() {
  getDB()
  return stmts.cleanup_failed_lyric_hashes.run()
}

// 根据hash更新歌曲歌词
function updateSongLyricsByHash(hash, lrc) {
  getDB()
  try {
    // 先获取歌曲ID
    const song = getSongByHash(hash)
    if (!song) return null

    // 更新或插入歌词
    return upsertLyricsBySongId(song.id, lrc)
  } catch (e) {
    console.error('[update song lyrics by hash err]', e)
    return null
  }
}

module.exports = {
  initDB,
  upsertSong,
  bulkUpsertSongs,
  listSongs,
  deleteSongsByPaths,
  upsertLyricsBySongId,
  getLyricsBySongId,
  getSongByHash,
  updateSongCoverByHash,
  updateSongLocalCoverByHash,
  recordFailedCoverHash,
  getFailedCoverHash,
  cleanupFailedCoverHashes,
  recordFailedLyricHash,
  getFailedLyricHash,
  cleanupFailedLyricHashes,
  updateSongLyricsByHash,
  updateSongPathByHash,
  nullifySongPathByPath,
  // 收藏相关
  addFavorite,
  removeFavorite,
  listFavorites,
  isFavorite
}