import axios from 'axios'
import Store from 'electron-store'

import { proxyRequest } from '../../src/utils/server.js'

const { app, BrowserWindow, dialog, ipcMain, Menu, Tray } = require('electron')
const path = require('path')
const express = require('express')
const fs = require('fs')
const https = require('https')
const http = require('http')
const mm = require('music-metadata')

const store = new Store()

let mainWindow = null
let audioWindow = null
let downloadWindow = null
let tray = null
let audioState = {
  isPlaying: false, // 是否正在播放
  currentTime: 0, // 当前播放时间
  currentTrack: null, // 当前播放歌曲URL
  duration: 0, // 歌曲时长
  volume: 0.5,
  ended: false, // 是否播放结束
  songInfo: null, // 当前播放歌曲信息
  playList: [], // 播放列表
  currentMusicId: null, // 当前播放歌曲ID
}
const isDev = process.env.NODE_ENV === 'development'
Menu.setApplicationMenu(null)
const createWindow = () => {
  mainWindow = new BrowserWindow({
    width: 1600,
    height: 789,
    // width: 1116,
    // height: 789,
    backgroundColor: '#fff',
    webPreferences: {
      nodeIntegration: true,
      preload: path.join(__dirname, '../preload/index.js'),
      //此参数禁用当前窗口的同源策略
      webSecurity: false,
    },
    show: false,
    resizable: true,
    // transparent: true,
    minWidth: 1116,
    minHeight: 789
  })
  //接收来自渲染进程的消息
  let searchValue = ''
  ipcMain.on('save-search', (event, value) => {
    searchValue = value
  })
  ipcMain.handle('get-search', () => {
    return searchValue
  })

  const startUrl = isDev ? 'http://localhost:5173' : `file://${__dirname}/index.html`
  mainWindow.loadURL(startUrl)
  if (isDev) {
    mainWindow.webContents.openDevTools()
  }
  // // mainWindow.webContents.openDevTools()
  // mainWindow.on('closed', () => {
  //     mainWindow = null
  // })
  // mainWindow.on('close', event => {
  //     // 阻止退出程序
  //     event.preventDefault()
  //     // 取消任务栏显示
  //     //mainWindow.setSkipTaskbar(true)
  //     // 隐藏主程序窗口
  //     //mainWindow.hide()
  // })
  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })
  // 拦截主窗口关闭：若有其他窗口，隐藏主窗口
  mainWindow.on('close', (event) => {
    // 检查是否存在未销毁的其他窗口（如 audioWindow）
    const hasOtherWindows = BrowserWindow.getAllWindows().some(
      win => win !== mainWindow && !win.isDestroyed()
    )
    if (hasOtherWindows) {
      event.preventDefault() // 阻止关闭
      mainWindow.hide()      // 隐藏主窗口
    }
  })
  // 主窗口刷新后恢复音频状态
  mainWindow.webContents.on('did-finish-load', () => {
    mainWindow.webContents.send('audio-state', audioState)
  })
  // const menu = Menu.buildFromTemplate(menuBar)
  // Menu.setApplicationMenu(null)
}

const createTray = () => {
  function toggle() {
    mainWindow.isVisible() ? mainWindow.hide() : mainWindow.show()
    mainWindow.isVisible() ? mainWindow.setSkipTaskbar(false) : mainWindow.setSkipTaskbar(true)
  }

  tray = new Tray(path.resolve(__dirname, '../../public/imgs/img.png'))
  const contextMenu = Menu.buildFromTemplate([
    {
      label: '显示/隐藏雷噶得',
      click() {
        toggle()
      }
    },
    {
      label: '退出',
      click() {
        tray.destroy()
        app.exit()
      }
    }
  ])
  tray.setToolTip('雷噶得')
  tray.setContextMenu(contextMenu)
  tray.on('click', () => {
    toggle()
  })
}

// 创建音频窗口
function createAudioWindow() {
  audioWindow = new BrowserWindow({
    width: 0,
    height: 0,
    show: false,
    frame: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  })
  audioWindow.loadURL(path.join(__dirname, '../../music.html'),)

  // 防止音频窗口意外显示
  audioWindow.on('ready-to-show', () => {
    if (audioWindow) audioWindow.hide()
  })

  // 监听音频窗口关闭
  audioWindow.on('closed', () => {
    audioWindow = null
  })
}

const createDownloadWindow = () => {
  if (downloadWindow && !downloadWindow.isDestroyed()) {
    downloadWindow.show()
    downloadWindow.focus()
    return
  }
  downloadWindow = new BrowserWindow({
    width: 400,
    height: 600,
    minWidth: 400,
    minHeight: 600,
    backgroundColor: '#fff',
    webPreferences: {
      preload: path.join(__dirname, '../preload/index.js'),
      webSecurity: false
    }
  })
  const startUrl = isDev ? 'http://localhost:5173/?type=sub' : `file://${__dirname}/index.html?type=sub`
  downloadWindow.loadURL(startUrl)
  if (isDev) {
    downloadWindow.webContents.openDevTools()
  }

  downloadWindow.on('ready-to-show', () => {
    downloadWindow.show()
  })

  downloadWindow.on('close', () => {
    downloadWindow.hide()
  })

  downloadWindow.on('closed', () => {
    downloadWindow = null
  })
}

ipcMain.on('open-download-window', () => {
  createDownloadWindow()
})

// 防止多开
const gotTheLock = app.requestSingleInstanceLock()
if (!gotTheLock) {
  app.exit()
}

app.on('ready', () => {
  createWindow()
  createAudioWindow()
  createTray()
})

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

function isValidWindow(window) {
  return window && !window.isDestroyed() && window.webContents
}

ipcMain.on('play-audio', (event, { trackUrl, currentTime = 0, songInfo, musicId }) => {
  audioState = {
    ...audioState,
    isPlaying: true,
    currentTime,
    currentTrack: trackUrl,
    ended: false,
    songInfo,
    currentMusicId: musicId || (songInfo && songInfo.MUSICRID) || null // 新增
  }
  if (audioWindow) {
    audioWindow.webContents.send('play-audio', { trackUrl, currentTime })
  }
})

// 新增：设置播放列表
ipcMain.on('set-playlist', (event, playList) => {
  audioState.playList = playList || []
})
// 新增：设置当前播放歌曲ID
ipcMain.on('set-current-music-id', (event, musicId) => {
  audioState.currentMusicId = musicId
})

ipcMain.on('pause-audio', () => {
  // 在暂停时获取当前时间（从音频窗口获取）
  const currentTime = audioState.currentTime
  audioState = {
    ...audioState,
    isPlaying: false,
    currentTime
  }

  if (audioWindow) {
    audioWindow.webContents.send('pause-audio')
  }
})
ipcMain.on('set-volume', (event, volume) => {
  audioState = {
    ...audioState,
    volume
  }
  // 通知音频窗口更新音量
  if (audioWindow) {
    audioWindow.webContents.send('set-volume', volume)
  }
  // 通知主窗口更新UI
  if (mainWindow) {
    mainWindow.webContents.send('audio-state', audioState)
  }
})

ipcMain.on('audio-duration', (event, duration) => {
  audioState = {
    ...audioState,
    duration
  }
  audioWindow.webContents.send('audio-duration', duration)
})

ipcMain.on('seek-audio', (event, currentTime) => {
  audioState = {
    ...audioState,
    currentTime
  }
  // 通知音频窗口更新播放位置
  if (audioWindow) {
    audioWindow.webContents.send('seek-audio', currentTime)
  }
  // 通知主窗口更新UI
  if (mainWindow) {
    mainWindow.webContents.send('audio-state', audioState)
  }
})

ipcMain.on('update-time', (event, currentTime) => {
  audioState = {
    ...audioState,
    currentTime
  }
  // 通知主窗口更新UI
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('audio-state', audioState)
  }
})

ipcMain.on('ended', () => {
  audioState.ended = true
  audioState.isPlaying = false
  // 通知主窗口更新UI
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('audio-state', audioState)
  }
})

// 监听音频错误信息
ipcMain.on('audio-error', (event, errorInfo) => {
  console.error('audio-error:', errorInfo)
  // 可以在这里添加错误处理逻辑，比如显示通知给用户
})

// 监听音频日志信息
// ipcMain.on('audio-log', (event, logInfo) => {
//   console.log('音频播放日志:', logInfo)
// })

// 获取完整audioState
ipcMain.handle('get-audio-state', () => {
  return audioState
})

// 获取当前应用目录
const appDir = app.getAppPath()
// 拼接 download 目录
const defaultDownloadDir = path.join(appDir, 'downloads')
// 启动时确保 download 目录存在
if (!fs.existsSync(defaultDownloadDir)) {
  fs.mkdirSync(defaultDownloadDir, { recursive: true })
}
// 用这个目录作为下载目录
let userDownloadDir = defaultDownloadDir
// 设置歌词下载目录
let LrcDownloadDir = path.join(userDownloadDir, 'lrc')
// 选择下载目录
ipcMain.handle('select-download-dir', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openDirectory']
  })
  if (!result.canceled && result.filePaths.length > 0) {
    userDownloadDir = result.filePaths[0]
    return userDownloadDir
  }
  return null
})

// 返回下载目录给前端
ipcMain.handle('get-download-dir', async () => {
  return userDownloadDir
})

// 获取已保存的歌曲列表
ipcMain.handle('get-songs', () => {
  // 从 'songs' 这个键读取数据，如果不存在则返回空数组
  return store.get('songs', [])
})

// 保存/更新歌曲列表
ipcMain.handle('set-songs', (event, songs) => {
  store.set('songs', songs)
})

// 获取本地歌曲
ipcMain.handle('get-local-songs', async () => {
  if (!fs.existsSync(defaultDownloadDir)) {
    return []
  }
  // 1. 从 store 加载已缓存的歌曲数据
  const cachedSongs = store.get('songs', [])
  // 2. 创建一个以文件路径为键的 Map，用于快速查找 O(1)
  const songCacheMap = new Map(cachedSongs.map(song => [song.path, song]))

  const files = fs.readdirSync(defaultDownloadDir).filter(f => {
    const ext = path.extname(f).toLowerCase()
    return ['.mp3', '.flac', '.m4a', '.wav', '.aac', '.ogg'].includes(ext)
  })

  const songPromises = files.map(async (file) => {
    // 从文件名中提取歌手和歌曲名
    const nameWithoutExt = path.basename(file, path.extname(file))
    const parts = nameWithoutExt.split(' - ')
    const artist = parts.length > 1 ? parts[0] : '未知艺术家'
    const title = parts.length > 1 ? parts.slice(1).join(' - ') : nameWithoutExt

    const fullPath = path.join(defaultDownloadDir, file)
    // 3. 检查缓存
    if (songCacheMap.has(fullPath)) {
      // 3.1 如果命中缓存，直接返回 store 中的数据
      return songCacheMap.get(fullPath)
    } else {
      // 3.2 如果未命中缓存，才使用 music-metadata 解析
      try {
        const metadata = await mm.parseFile(fullPath)
        const { common } = metadata
        return {
          NAME: common.title || title,
          ARTIST: common.artist || artist,
          ALBUM: common.album,
          DURATION: metadata.format.duration || 0,
          path: fullPath,
          source: 'local', // 标记为本地来源
          MUSICRID: `local_${fullPath}`, // 创建唯一ID
          filename: file
        }
      } catch (error) {
        console.error(`解析文件失败: ${fullPath}`, error)
        // 解析失败，也返回一个基础对象，保证程序不中断
        return {
          NAME: path.basename(fullPath, path.extname(fullPath)),
          ARTIST: '解析失败',
          path: fullPath,
          source: 'local',
          MUSICRID: `local_${fullPath}`
        }
      }
    }
  })

  return await Promise.all(songPromises)
})

// 读取本地歌词
ipcMain.handle('get-local-lyrics', async (event, songPath) => {
  let songName = null
  try {
    if (fs.existsSync(songPath.replace('file://', ''))) {
      // 本地歌曲
      songName = path.basename(songPath, path.extname(songPath))
    } else {
      // 在线歌曲
      songName = songPath
    }
    // 查找同名的 .lrc 文件
    const lrcPath = path.join(LrcDownloadDir, `${songName}.lrc`)
    if (fs.existsSync(lrcPath)) {
      const lyricsContent = fs.readFileSync(lrcPath, 'utf-8')
      return {
        success: true,
        lyrics: lyricsContent,
        source: 'local'
      }
    }
    return {
      success: false,
      message: '未找到本地歌词文件'
    }
  } catch (error) {
    console.error('读取本地歌词失败:', error)
    return {
      success: false,
      message: error.message
    }
  }
})

// 保存歌词到本地
ipcMain.handle('save-lyrics', async (event, { name, lrc }) => {
  try {
    const lyricsFileName = `${name}.lrc`
    const lyricsPath = path.join(LrcDownloadDir, lyricsFileName)
    // 保存歌词文件
    fs.writeFileSync(lyricsPath, lrc, 'utf-8')
  } catch (err) {
    console.log('保存失败: ', err)
  }
})

// 下载歌曲
ipcMain.on('download-song', (event, { url, filename, key, songInfo }) => {
  if (!mainWindow) {
    console.error('主窗口不存在，无法下载')
    return
  }

  const savePath = path.join(userDownloadDir, filename)

  // 使用Node.js直接下载，避免浏览器弹窗
  const protocol = url.startsWith('https:') ? https : http
  const options = {
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Referer': 'https://www.kuwo.cn'
    }
  }
  const request = protocol.get(url, options, (response) => {
    if (response.statusCode !== 200) {
      console.error('下载失败，状态码:', response.statusCode)
      mainWindow.webContents.send('download-done', {
        key,
        filename,
        state: 'interrupted',
        path: savePath
      })
      return
    }

    const total = parseInt(response.headers['content-length'], 10) || 0
    let received = 0

    const fileStream = fs.createWriteStream(savePath)

    response.on('data', (chunk) => {
      received += chunk.length
      if (total > 0) {
        const progress = Math.round((received / total) * 100)
        // 格式化已下载大小
        const formatFileSize = (bytes) => {
          if (bytes === 0) return '0B'
          const k = 1024
          const sizes = ['B', 'KB', 'MB', 'GB']
          const i = Math.floor(Math.log(bytes) / Math.log(k))
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i]
        }

        const progressData = {
          key,
          filename,
          progress,
          received,
          total,
          downloadedSize: formatFileSize(received)
        }
        mainWindow.webContents.send('download-progress', progressData)
      }
    })

    fileStream.on('finish', async () => {
      fileStream.close()
      // 1. 准备要保存的歌曲对象
      const downloadedSong = {
        ...songInfo, // 包含 NAME, ARTIST, MUSICRID 等
        path: savePath, // 保存文件的绝对路径
        source: 'downloaded' // 标记来源
      }
      // 2 添加新歌，并做简单去重
      const songs = store.get('songs', [])
      const newSongs = [...songs.filter(s => s.MUSICRID !== downloadedSong.MUSICRID), downloadedSong]
      // 3.更新store
      store.set('songs', newSongs)
      mainWindow.webContents.send('download-done', {
        key,
        filename,
        state: 'completed',
        path: savePath
      })
      if (!fs.existsSync(LrcDownloadDir)) {
        fs.mkdirSync(LrcDownloadDir, { recursive: true })
      }
      // 下载歌曲完成后，自动下载歌词
      if (songInfo && songInfo.musicId) {
        const { musicId, NAME, ARTIST } = songInfo
        try {
          // 构建歌词文件名
          const lyricsFileName = `${ARTIST} - ${NAME}.lrc`
          const lyricsPath = path.join(LrcDownloadDir, lyricsFileName)
          // 如果有同名文件则直接退出
          if (fs.existsSync(lyricsPath)) return
          // 获取歌词URL
          const lyricsUrl = `https://www.kuwo.cn/openapi/v1/www/lyric/getlyric?musicId=${musicId.split('_')[1]}&httpsStatus=1&plat=web_www&from=`
          const response = await axios.get(lyricsUrl, {
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              'Referer': `https://www.kuwo.cn/play_detail/${musicId.split('_')[1]}`
            }
          })
          if (response.data && response.data.code === 200 && response.data.data && response.data.data.lrclist) {
            // 将歌词数组转换为LRC格式
            const lrcContent = response.data.data.lrclist.map(item => {
              const time = parseFloat(item.time)
              const minutes = Math.floor(time / 60)
              const seconds = Math.floor(time % 60)
              const milliseconds = Math.floor((time % 1) * 1000)
              return `[${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}]${item.lineLyric}`
            }).join('\n')
            // 保存歌词文件
            fs.writeFileSync(lyricsPath, lrcContent, 'utf-8')
          } else {
            console.log('未找到歌词数据')
          }
        } catch (error) {
          console.error('download err:', error)
        }
      }

    })

    fileStream.on('error', (error) => {
      mainWindow.webContents.send('download-done', {
        key,
        filename,
        state: 'interrupted',
        path: savePath
      })
    })

    response.pipe(fileStream)
  })

  request.on('error', (error) => {
    console.error('下载请求错误:', error)
    mainWindow.webContents.send('download-done', {
      key,
      filename,
      state: 'interrupted',
      path: savePath
    })
  })

  request.setTimeout(30000, () => {
    console.error('下载超时')
    request.destroy()
    mainWindow.webContents.send('download-done', {
      key,
      filename,
      state: 'interrupted',
      path: savePath
    })
  })
})

const server = express()
server.use(express.static(__dirname + '/public'))
server.listen(9966)

// server.get('/search', (req, res) => {
//   const { name } = req.query
//   searchRequest('search', name).then(data => {
//     res.send(data)
//   })
// })
// server.get('/home', (req, res) => {
//   const { page } = req.query
//   searchRequest('home', page).then(data => {
//     res.send(data)
//   })
// })

// server.get('/proxy', async (req, res) => {
//   const { url } = req.query
//   proxyRequest(url).then(data => {
//     res.send(data.data)
//   })
// })

server.get('/proxy', async (req, res) => {
  const args = req.query
  proxyRequest(args).then(result => {
    res.send(result.data)
  })
})
