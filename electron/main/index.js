import axios from 'axios'
import Store from 'electron-store'

import { proxyRequest } from '../../src/utils/server.js'
import { getRecDetailProxy } from './recProxy'
import { userAgentMobile, userAgentList } from '../../src/utils/userAgent.js'
import { decodeLyric, parseLyric } from '../../src/utils/getLrc.js'
import { replaceSize } from '../../src/utils/constants.js'

const { app, BrowserWindow, dialog, ipcMain, Menu, Tray } = require('electron')
const path = require('path')
const express = require('express')
const fs = require('fs')
const https = require('https')
const http = require('http')
const axiosRaw = require('axios')
const mm = require('music-metadata')
const crypto = require('crypto')

const store = new Store()

// 兼容多环境加载 db 模块
let db = null
const tryPaths = [
  // asar 内部：out/main/ → ../../electron/main/db.js
  path.join(__dirname, '..', '..', 'electron', 'main', 'db.js'),
  // asar 根：app.asar + electron/main/db.js
  path.join(app.getAppPath(), 'electron', 'main', 'db.js'),
  // resources 旁：app.asar.unpacked 场景（保守回退）
  path.join(process.resourcesPath || '', 'app.asar.unpacked', 'electron', 'main', 'db.js'),
  // 开发环境
  path.join(process.cwd(), 'electron', 'main', 'db.js'),
  path.join(__dirname, 'db.js'),
  path.join(__dirname, 'db')
]

for (const p of tryPaths) {
  try {
    db = require(p)
    break
  } catch (err) {
    // 输出每个加载路径的具体错误，便于定位问题
    console.error('[db] require failed for path:', p, err && err.message)
  }
}
if (!db) {
  console.error('[db] 加载失败，尝试路径：', tryPaths)
  throw new Error('加载 db 模块失败，请检查 electron/main/db.js 是否被打包进 asar')
}

let mainWindow = null
let audioWindow = null
let downloadWindow = null
let tray = null
let audioState = {
  isPlaying: false, // 是否正在播放
  currentTime: 0, // 当前播放时间
  currentTrack: null, // 当前播放歌曲URL
  duration: 0, // 歌曲时长
  volume: store.get('volume', 0.5),
  ended: false, // 是否播放结束
  songInfo: null, // 当前播放歌曲信息
  playList: [], // 播放列表
  currentMusicId: null, // 当前播放歌曲ID
}

// 缓存：正在进行的封面/歌词请求，防止并发重复请求（in-flight dedupe）
const inFlightLyricRequests = new Map() // key -> Promise
const inFlightCoverRequests = new Map() // hash -> Promise

const isDev = process.env.NODE_ENV === 'development'
Menu.setApplicationMenu(null)
const createWindow = () => {
  mainWindow = new BrowserWindow({
    width: isDev ? 1600 : 1100,
    height: 789,
    // width: 1116,
    // height: 789,
    backgroundColor: '#fff',
    webPreferences: {
      nodeIntegration: true,
      preload: path.join(__dirname, '../preload/index.js'),
      //此参数禁用当前窗口的同源策略
      webSecurity: false,
    },
    show: false,
    resizable: true,
    // transparent: true,
    minWidth: 1116,
    minHeight: 789
  })
  //接收来自渲染进程的消息
  let searchValue = ''
  ipcMain.on('save-search', (event, value) => {
    searchValue = value
  })
  ipcMain.handle('get-search', () => {
    return searchValue
  })

  const startUrl = isDev
    ? (process.env.ELECTRON_RENDERER_URL || 'http://localhost:5173')
    : `file://${path.join(__dirname, '../renderer/index.html')}`
  mainWindow.loadURL(startUrl)
  if (isDev) {
    mainWindow.webContents.openDevTools()
  }
  // // mainWindow.webContents.openDevTools()
  // mainWindow.on('closed', () => {
  //     mainWindow = null
  // })
  // mainWindow.on('close', event => {
  //     // 阻止退出程序
  //     event.preventDefault()
  //     // 取消任务栏显示
  //     //mainWindow.setSkipTaskbar(true)
  //     // 隐藏主程序窗口
  //     //mainWindow.hide()
  // })
  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })
  // 拦截主窗口关闭：若有其他窗口，隐藏主窗口
  mainWindow.on('close', (event) => {
    // 检查是否存在未销毁的其他窗口（如 audioWindow）
    const hasOtherWindows = BrowserWindow.getAllWindows().some(
      win => win !== mainWindow && !win.isDestroyed()
    )
    if (hasOtherWindows) {
      event.preventDefault() // 阻止关闭
      mainWindow.hide()      // 隐藏主窗口
    }
  })
  // 主窗口刷新后恢复音频状态
  mainWindow.webContents.on('did-finish-load', () => {
    mainWindow.webContents.send('audio-state', audioState)
  })
  // const menu = Menu.buildFromTemplate(menuBar)
  // Menu.setApplicationMenu(null)
}

const createTray = () => {
  function toggle() {
    mainWindow.isVisible() ? mainWindow.hide() : mainWindow.show()
    mainWindow.isVisible() ? mainWindow.setSkipTaskbar(false) : mainWindow.setSkipTaskbar(true)
  }

  const iconPath = app.isPackaged
    ? path.join(process.resourcesPath, 'public', 'imgs', 'img.png')
    : path.join(__dirname, '../../public/imgs/img.png')
  tray = new Tray(iconPath)
  const contextMenu = Menu.buildFromTemplate([
    {
      label: '显示/隐藏雷噶得',
      click() {
        toggle()
      }
    },
    {
      label: '退出',
      click() {
        tray.destroy()
        app.exit()
      }
    }
  ])
  tray.setToolTip('雷噶得')
  tray.setContextMenu(contextMenu)
  tray.on('click', () => {
    toggle()
  })
}

// 创建音频窗口
function createAudioWindow() {
  audioWindow = new BrowserWindow({
    width: 0,
    height: 0,
    show: false,
    frame: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  })
  // 修改加载路径
  let audioUrl
  if (isDev) {
    audioUrl = path.join(__dirname, '../../music.html')
  } else {
    audioUrl = path.join(__dirname, '../renderer/music.html')
  }
  // audioWindow.loadURL(`file://${audioUrl}`)
  // audioWindow.loadURL(path.join(__dirname, '../../music.html'),)

  audioWindow.loadURL(`file://${audioUrl}`)
  // 防止音频窗口意外显示
  audioWindow.on('ready-to-show', () => {
    if (audioWindow) audioWindow.hide()
  })

  // 监听音频窗口关闭
  audioWindow.on('closed', () => {
    audioWindow = null
  })
}

const createDownloadWindow = () => {
  if (downloadWindow && !downloadWindow.isDestroyed()) {
    downloadWindow.show()
    downloadWindow.focus()
    return
  }
  downloadWindow = new BrowserWindow({
    width: 400,
    height: 600,
    minWidth: 400,
    minHeight: 600,
    backgroundColor: '#fff',
    webPreferences: {
      preload: path.join(__dirname, '../preload/index.js'),
      webSecurity: false
    }
  })
  const startUrl = isDev ? 'http://localhost:5173/?type=sub' : `file://${__dirname}/index.html?type=sub`
  downloadWindow.loadURL(startUrl)
  if (isDev) {
    downloadWindow.webContents.openDevTools()
  }

  downloadWindow.on('ready-to-show', () => {
    downloadWindow.show()
  })

  downloadWindow.on('close', () => {
    downloadWindow.hide()
  })

  downloadWindow.on('closed', () => {
    downloadWindow = null
  })
}

ipcMain.on('open-download-window', () => {
  createDownloadWindow()
})

// 计算文件 MD5（流式）
function computeFileMd5(filePath) {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('md5')
    const stream = fs.createReadStream(filePath)
    stream.on('error', reject)
    stream.on('data', chunk => hash.update(chunk))
    stream.on('end', () => resolve(hash.digest('hex')))
  })
}

// 轻量清理：仅维护仍使用的 store 项（下载目录/扫描目录）
function cleanStoreSongs() {
  try {
    // 1) 确保下载目录存在（若被手动删除则自动重建）
    const currentDownloadDir = store.get('downloadDir') || defaultDownloadDir
    if (!fs.existsSync(currentDownloadDir)) {
      fs.mkdirSync(currentDownloadDir, { recursive: true })
    }
    // 同步到运行时变量
    userDownloadDir = currentDownloadDir
    LrcDownloadDir = path.join(userDownloadDir, 'lrc')
    if (!fs.existsSync(LrcDownloadDir)) {
      fs.mkdirSync(LrcDownloadDir, { recursive: true })
    }

    // 2) 清理 scanDirs 中已不存在的路径
    const dirs = store.get(SCAN_DIRS_KEY, [])
    const validDirs = dirs.filter(p => p && fs.existsSync(p))
    if (validDirs.length !== dirs.length) {
      store.set(SCAN_DIRS_KEY, validDirs)
    }
  } catch {}
}

// 防止多开
const gotTheLock = app.requestSingleInstanceLock()
if (!gotTheLock) {
  app.exit()
}

app.on('ready', () => {
  createWindow()
  createAudioWindow()
  createTray()
  db.initDB()
})

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

function isValidWindow(window) {
  return window && !window.isDestroyed() && window.webContents
}

const server = express()
server.use(express.static(__dirname + '/public'))
server.listen(9966)

server.get('/proxy', async (req, res) => {
  const args = req.query
  proxyRequest(args).then(result => {
    res.send(result.data)
  })
})

server.get('/recProxy', async (req, res) => {
  const args = req.query
  getRecDetailProxy(args).then(result => {
    res.send(result.data)
  })
})

// 设置播放列表
ipcMain.on('set-playlist', (event, playList) => {
  audioState = { ...audioState, playList }
  if (!playList || playList.length === 0) {
    handlePauseAudio()   // 调用已有的暂停逻辑
    audioState = {
      ...audioState,
      isPlaying: false,
      ended: true,
      currentTime: 0,
      duration: 0,
      currentTrack: null,
      songInfo: null,
      currentMusicId: null
    }

    if (audioWindow) {
      audioWindow.webContents.send('reset-audio')
    }
    if (mainWindow) mainWindow.webContents.send('audio-state', audioState)
  }
})

// 设置当前音乐ID
ipcMain.on('set-current-music-id', (event, musicId) => {
  audioState = {
    ...audioState,
    currentMusicId: musicId
  }
})

// 播放音频函数
async function handlePlayAudio(trackUrl, currentTime = 0, songInfo, musicId, playList = null) {
  try {
    // 如果传入了播放列表，则更新播放列表
    if (playList) {
      audioState.playList = playList
    }

    // 验证本地文件是否存在
    if (trackUrl.startsWith('file://')) {
      const filePath = trackUrl.replace('file://', '')
      if (!fs.existsSync(filePath)) {
        // 文件不存在，从播放列表中移除
        if (audioState.playList && songInfo?.MUSICRID) {
          const currentIndex = audioState.playList.findIndex(item => item.MUSICRID === songInfo.MUSICRID)
          if (currentIndex !== -1) {
            audioState.playList.splice(currentIndex, 1)
            // 通知主窗口更新播放列表
            if (mainWindow) {
              mainWindow.webContents.send('audio-state', audioState)
            }
            return { success: false, message: '歌曲文件已失效，已从播放列表移除' }
          }
        }
        return { success: false, message: '歌曲文件不存在' }
      }
    }

    console.log(`play:  id: ${musicId}  url: ${trackUrl}`)
    // 如果是在线音乐且没有URL，需要获取播放链接
    if (musicId && !trackUrl.startsWith('file://') && !trackUrl.startsWith('http')) {
      const response = await proxyRequest({
        type: 'musicId',
        musicId: musicId
      })

      if (response.data.code === 200) {
        trackUrl = response.data.data.url
        if (!songInfo.duration) {
          songInfo.duration = response.data.data.duration || 0
        }
      } else {
        return { success: false, message: '获取播放链接失败' }
      }
    }

    audioState = {
      ...audioState,
      isPlaying: true,
      currentTime,
      currentTrack: trackUrl,
      ended: false,
      songInfo,
      currentMusicId: musicId || (songInfo && songInfo.MUSICRID) || null
    }

    if (audioWindow) {
      audioWindow.webContents.send('play-audio', { trackUrl, currentTime })
    }

    if (mainWindow) {
      mainWindow.webContents.send('audio-state', audioState)
    }

    return { success: true, songInfo }
  } catch (error) {
    console.error('播放失败:', error)
    return { success: false, message: '播放失败: ' + error.message }
  }
}

ipcMain.handle('play-audio', async (event, { trackUrl, currentTime = 0, songInfo, musicId, playList }) => {
  return await handlePlayAudio(trackUrl, currentTime, songInfo, musicId, playList)
})

// 暂停音频函数
function handlePauseAudio() {
  const currentTime = audioState.currentTime
  audioState = {
    ...audioState,
    isPlaying: false,
    currentTime
  }

  if (audioWindow) {
    audioWindow.webContents.send('pause-audio')
  }

  if (mainWindow) {
    mainWindow.webContents.send('audio-state', audioState)
  }

  return { success: true }
}

ipcMain.on('pause-audio', () => {
  handlePauseAudio()
})

ipcMain.handle('pause-audio', () => {
  return handlePauseAudio()
})

// 上一首歌曲
ipcMain.handle('prev-audio', async () => {
  const { playList, currentMusicId } = audioState
  if (!playList || !currentMusicId) return { success: false, message: '播放列表为空' }

  const idx = playList.findIndex(item => item.MUSICRID === currentMusicId)
  if (idx === -1) return { success: false, message: '当前歌曲不在播放列表中' }

  const prevIdx = (idx - 1 + playList.length) % playList.length
  const prevSongInfo = playList[prevIdx]

  // 根据"目标上一首"的类型决定播放分支
  if (prevSongInfo.isLocalPlay) {
    // 使用 handlePlayAudio 统一处理，包括文件验证
    return await handlePlayAudio(`file://${prevSongInfo.path}`, 0, prevSongInfo, prevSongInfo.MUSICRID)
  }

  // 在线分支
  try {
    const response = await proxyRequest({ type: 'musicId', musicId: prevSongInfo.MUSICRID })
    if (response.data.code === 200) {
      const { url, duration } = response.data.data
      const songInfo = {
        NAME: prevSongInfo.NAME || prevSongInfo.SONGNAME,
        ARTIST: prevSongInfo.ARTIST,
        ALBUM: prevSongInfo.ALBUM || '',
        cover: prevSongInfo.cover,
        duration: duration || 0,
        MUSICRID: prevSongInfo.MUSICRID
      }
      // 使用 handlePlayAudio 统一处理
      return await handlePlayAudio(url, 0, songInfo, prevSongInfo.MUSICRID)
    }
  } catch (error) {
    console.error('获取上一首歌曲失败:', error)
    return { success: false, message: '获取歌曲链接失败' }
  }
})

// 将next-audio处理函数提取出来
async function handleNextAudio() {
  const { playList, currentMusicId } = audioState
  if (!playList || !currentMusicId) return { success: false, message: '播放列表为空' }

  // 定位当前歌曲索引
  const idx = playList.findIndex(item => item.MUSICRID === currentMusicId)
  if (idx === -1) return { success: false, message: '当前歌曲不在播放列表中' }

  // 计算下一首
  const nextIdx = (idx + 1) % playList.length
  const nextSongInfo = playList[nextIdx]

  // 如果目标下一首是本地歌曲，走本地播放分支
  if (nextSongInfo.isLocalPlay) {
    // 使用 handlePlayAudio 统一处理，包括文件验证
    return await handlePlayAudio(`file://${nextSongInfo.path}`, 0, nextSongInfo, nextSongInfo.MUSICRID)
  }

  // 在线播放分支
  try {
    const response = await proxyRequest({
      type: 'musicId',
      musicId: nextSongInfo.MUSICRID
    })
    if (response.data.code === 200) {
      const { url, duration } = response.data.data
      const songInfo = {
        NAME: nextSongInfo.NAME || nextSongInfo.SONGNAME,
        ARTIST: nextSongInfo.ARTIST,
        ALBUM: nextSongInfo.ALBUM || '',
        cover: nextSongInfo.cover,
        duration: duration || 0,
        MUSICRID: nextSongInfo.MUSICRID
      }
      // 使用 handlePlayAudio 统一处理
      return await handlePlayAudio(url, 0, songInfo, nextSongInfo.MUSICRID)
    } else {
      return { success: false, message: '获取歌曲链接失败' }
    }
  } catch (error) {
    console.error('获取下一首歌曲失败:', error)
    return { success: false, message: '获取歌曲链接失败' }
  }
}

ipcMain.handle('next-audio', handleNextAudio)

let isAutoSwitching = false
ipcMain.on('ended', async () => {
  if (isAutoSwitching) return

  isAutoSwitching = true
  audioState.ended = true
  audioState.isPlaying = false

  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('audio-state', audioState)
  }

  setTimeout(async () => {
    try {
      await handleNextAudio() // 直接调用函数，不是IPC处理器
    } catch (error) {
      console.error('自动切换下一首失败:', error)
    } finally {
      isAutoSwitching = false
    }
  }, 500)
})

ipcMain.on('set-volume', (event, volume) => {
  audioState = {
    ...audioState,
    volume
  }
  // 通知音频窗口更新音量
  if (audioWindow) {
    audioWindow.webContents.send('set-volume', volume)
  }
  // 通知主窗口更新UI
  if (mainWindow) {
    mainWindow.webContents.send('audio-state', audioState)
  }
  store.set('volume', volume)
})

ipcMain.on('audio-duration', (event, duration) => {
  audioState = {
    ...audioState,
    duration
  }
  audioWindow.webContents.send('audio-duration', duration)
})

ipcMain.on('seek-audio', (event, currentTime) => {
  audioState = {
    ...audioState,
    currentTime
  }
  // 通知音频窗口更新播放位置
  if (audioWindow) {
    audioWindow.webContents.send('seek-audio', currentTime)
  }
  // 通知主窗口更新UI
  if (mainWindow) {
    mainWindow.webContents.send('audio-state', audioState)
  }
})

ipcMain.on('update-time', (event, currentTime) => {
  // 如果当前没有播放、没有曲目、或播放列表为空，直接忽略进度上报
  if (!audioState.isPlaying || !audioState.currentTrack || !audioState.playList?.length) {
    return // 直接返回，不处理这个update-time
  }

  audioState = {
    ...audioState,
    currentTime
  }
  // 通知主窗口更新UI
  if (mainWindow && !mainWindow.isDestroyed()) {
    // mainWindow.webContents.send('audio-state', audioState)
    // 仅下发进度信息，避免频繁覆盖完整 audioState 导致播放列表/封面回退
    mainWindow.webContents.send('audio-progress', {
      currentTime: audioState.currentTime,
      duration: audioState.duration,
      isPlaying: audioState.isPlaying
    })
  }
})

// 监听音频错误信息
ipcMain.on('audio-error', (event, errorInfo) => {
  console.error('audio-error:', errorInfo)
  // 可以在这里添加错误处理逻辑，比如显示通知给用户
})

// 监听音频日志信息
// ipcMain.on('audio-log', (event, logInfo) => {
//   console.log('音频播放日志:', logInfo)
// })

// 获取完整audioState
ipcMain.handle('get-audio-state', () => {
  return audioState
})

// 收藏相关IPC处理
ipcMain.handle('add-favorite', async (event, songInfo) => {
  try {
    const result = db.addFavorite(songInfo)
    return { success: true, result }
  } catch (error) {
    console.error('添加收藏失败:', error)
    return { success: false, error: error.message }
  }
})

ipcMain.handle('remove-favorite', async (event, musicrid) => {
  try {
    const result = db.removeFavorite(musicrid)
    return { success: true, result }
  } catch (error) {
    console.error('取消收藏失败:', error)
    return { success: false, error: error.message }
  }
})

ipcMain.handle('get-favorites', async () => {
  try {
    const favorites = db.listFavorites()
    return { success: true, data: favorites }
  } catch (error) {
    console.error('获取收藏列表失败:', error)
    return { success: false, error: error.message }
  }
})

ipcMain.handle('check-favorite', async (event, musicrid) => {
  try {
    const isFav = db.isFavorite(musicrid)
    return { success: true, isFavorite: isFav }
  } catch (error) {
    console.error('检查收藏状态失败:', error)
    return { success: false, error: error.message }
  }
})

// 计算默认下载根目录：
// - 打包后：resources 的同级目录
// - 开发时：应用根目录（与之前行为一致）
const baseDownloadRoot = app.isPackaged ? path.dirname(process.resourcesPath) : app.getAppPath()
// 拼接 download 目录
let defaultDownloadDir = path.join(baseDownloadRoot, 'downloads')
if (!store.get('downloadDir')) {
  store.set('downloadDir', defaultDownloadDir)
}
// 启动时确保 download 目录存在
if (!fs.existsSync(defaultDownloadDir)) {
  try {
    fs.mkdirSync(defaultDownloadDir, { recursive: true })
  } catch (error) {
    console.error('创建下载目录失败:', error)
    // 如果创建失败，使用桌面目录作为备选
    const desktopPath = app.getPath('desktop')
    defaultDownloadDir = path.join(desktopPath, '雷噶得下载')
    if (!fs.existsSync(defaultDownloadDir)) {
      fs.mkdirSync(defaultDownloadDir, { recursive: true })
      store.set('downloadDir', defaultDownloadDir)
    }
  }
}

// 用这个目录作为下载目录
let userDownloadDir = defaultDownloadDir
// 设置歌词下载目录（位于下载目录下）
let LrcDownloadDir = path.join(userDownloadDir, 'lrc')
// 选择下载目录
ipcMain.handle('select-download-dir', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openDirectory']
  })
  if (!result.canceled && result.filePaths.length > 0) {
    userDownloadDir = result.filePaths[0]
    store.set('downloadDir', userDownloadDir)
    return userDownloadDir
  }
  return null
})

// 获取下载目录
ipcMain.handle('get-download-dir', async () => {
  return store.get('downloadDir')
})

// 获取本地歌曲
ipcMain.handle('get-local-songs', async () => {
  try {
    const rows = db.listSongs()
    // 过滤掉本地 path 不存在的记录
    const valid = rows.filter(s => s.path && fs.existsSync(s.path))
    return valid.map(s => {
      const localCoverExists = !!s.localCover && fs.existsSync(s.localCover)
      return {
        NAME: s.NAME,
        ARTIST: s.ARTIST,
        ALBUM: s.ALBUM,
        DURATION: s.DURATION || 0,
        cover: s.cover,
        localCover: localCoverExists ? s.localCover : null,
        path: s.path,
        filename: s.filename,
        MUSICRID: s.MUSICRID,
        hash: s.hash,
        source: s.source,
        created: s.created_at,
      }
    })
  } catch (e) {
    console.error('[get-local-songs] err', e)
    return []
  }
})

// 统一路径规范化，避免大小写/真实路径差异导致的未命中
const toKey = (p) => {
  try {
    return fs.realpathSync.native ? fs.realpathSync.native(p) : fs.realpathSync(p)
  } catch {
    return path.resolve(p)
  }
}

// 从文件名尝试提取 艺术家-歌名
function extractArtistTitleFromFilename(filePath) {
  try {
    const base = path.basename(filePath, path.extname(filePath)).trim()
    if (!base) return { artist: '', title: '' }
    const candidates = [' - ', ' — ', '–', '—', '-', '_']
    for (const sep of candidates) {
      const idx = base.indexOf(sep)
      if (idx > 0 && idx < base.length - sep.length) {
        const artist = base.slice(0, idx).trim()
        const title = base.slice(idx + sep.length).trim()
        if (artist && title) return { artist, title }
      }
    }
    return { artist: '', title: base }
  } catch {
    return { artist: '', title: '' }
  }
}

// 读取本地歌词 没有则在线获取
ipcMain.handle('get-local-lyrics', async (event, song) => {
  try {
    const dir = LrcDownloadDir
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
    let hash = song.hash

    // 1) 优先按 hash.lrc
    if (hash) {
      const target = path.join(dir, `${hash}.lrc`)
      if (fs.existsSync(target)) {
        const lyricsContent = fs.readFileSync(target, 'utf-8')
        console.log('local lrc by hash: ', hash)
        return { success: true, lyrics: lyricsContent, source: 'local', file: `${hash}.lrc` }
      }
    }

    // 2) 回退按"歌手 - 歌名.lrc"
    const sanitize = (s) => (typeof s === 'string' ? s : '').replace(/[\/:*?"<>|]/g, '_').trim()
    let artistName = sanitize(song.ARTIST)
    let titleName = sanitize(song.NAME)
    const fileName = `${artistName} - ${titleName}.lrc`
    const tryPath = path.join(dir, fileName)
    if (fs.existsSync(tryPath)) {
      console.log('local lrc by name: ', fileName)
      const lyricsContent = fs.readFileSync(tryPath, 'utf-8')
      return { success: true, lyrics: lyricsContent, source: 'local', file: fileName }
    }

    // 3) 本地没有歌词，尝试通过hash获取（加入 in-flight 去重）
    if (hash) {
      // 查看失败记录
      try {
        const failedRecord = db.getFailedLyricHash?.(hash)
        if (failedRecord) {
          console.log(`Hash ${hash} no lyric before: ${failedRecord.reason}`)
          return { success: false, message: '本地没有歌词' }
        }
      } catch {}

      // 若已有正在进行的请求，直接复用 Promise
      if (inFlightLyricRequests.has(hash)) {
        try {
          return await inFlightLyricRequests.get(hash)
        } catch (e) {
          return { success: false, message: '未找到本地歌词文件' }
        }
      }

      const p = (async () => {
        try {
          // 第一步：搜索歌词
          const searchUrl = `https://krcs.kugou.com/search?ver=1&man=no&client=pc&hash=${hash}`
          const searchResponse = await axiosRaw.get(searchUrl, {
            headers: {
              'User-Agent': userAgentList[Math.floor(Math.random() * userAgentList.length)],
              'Referer': 'https://www.kugou.com/',
            },
            timeout: 10000
          })

          if (!(searchResponse.data && searchResponse.data.status === 200)) {
            // 记录无数据
            try { db.recordFailedLyricHash?.(hash, 'no_lyric_data') } catch {}
            return { success: false, message: '本地没有歌词' }
          }

          const firstLyric = searchResponse.data?.candidates?.[0]
          const lyricId = firstLyric?.id
          const accessKey = firstLyric?.accesskey
          if (!lyricId || !accessKey) {
            try { db.recordFailedLyricHash?.(hash, 'no_lyric_data') } catch {}
            return { success: false, message: '本地没有歌词' }
          }

          // 第二步：下载歌词
          const downloadUrl = `https://lyrics.kugou.com/download?ver=1&client=pc&id=${lyricId}&accesskey=${accessKey}&fmt=krc&charset=utf8`
          const downloadRes = await axiosRaw.get(downloadUrl, {
            headers: {
              'User-Agent': userAgentList[Math.floor(Math.random() * userAgentList.length)],
              'Referer': 'https://www.kugou.com/',
            },
            timeout: 10000
          })

          if (!(downloadRes.data && downloadRes.data.status === 200 && downloadRes.data.content)) {
            try { db.recordFailedLyricHash?.(hash, 'no_lyric_data') } catch {}
            return { success: false, message: '本地没有歌词' }
          }

          // 解码并解析
          const decodedLyric = await decodeLyric(downloadRes.data.content)
          const parsedLyric = parseLyric(decodedLyric)

          // 原子写入：先写到临时文件再重命名
          const lyricsFileName = `${hash}.lrc`
          const tmpPath = path.join(dir, `${lyricsFileName}.tmp`)
          const finalPath = path.join(dir, lyricsFileName)
          fs.writeFileSync(tmpPath, parsedLyric.lyric, 'utf-8')
          fs.renameSync(tmpPath, finalPath)

          // 更新数据库中的歌词信息
          try { db.updateSongLyricsByHash?.(hash, parsedLyric.lyric) } catch (e) { console.error('[update song lyrics err]', e) }

          console.log(`lrc saved: ${finalPath}`)
          return { success: true, lyrics: parsedLyric.lyric, source: 'online', file: lyricsFileName }
        } catch (parseError) {
          console.error('get lrc failed:', parseError)
          try { db.recordFailedLyricHash?.(hash, 'fetch_failed') } catch {}
          return { success: false, message: '未找到本地歌词文件' }
        }
      })()

      inFlightLyricRequests.set(hash, p)
      try {
        return await p
      } finally {
        inFlightLyricRequests.delete(hash)
      }
    }

    return { success: false, message: '未找到本地歌词文件' }
  } catch (error) {
    console.error('read lrc failed:', error)
    return { success: false, message: error.message }
  }
})

// 保存歌词到本地
ipcMain.handle('save-lyrics', async (event, payload) => {
  try {
    // 兼容旧参数 { name, lrc }，以及新参数 { hash, lrc }
    const hash = payload?.hash
    const name = payload?.name
    const lrc = payload?.lrc || ''
    console.log('save, payload', payload)
    let lyricsFileName = ''
    if (hash && /^[0-9a-f]{32}$/i.test(hash)) {
      lyricsFileName = `${hash}.lrc`
    } else if (name) {
      lyricsFileName = `${name}.lrc`
    } else {
      return { success: false, message: '缺少 hash 或 name' }
    }

    const lyricsPath = path.join(LrcDownloadDir, lyricsFileName)
    // 保存歌词文件
    fs.writeFileSync(lyricsPath, lrc, 'utf-8')
    return { success: true, path: lyricsPath }
  } catch (err) {
    console.log('保存失败: ', err)
    return { success: false, message: err?.message || '保存失败' }
  }
})

// 存储待下载的任务
const downloadQueue = new Map()

// 下载歌曲
ipcMain.on('download-song', (event, { url, filename, key, songInfo }) => {
  if (!mainWindow) {
    console.error('主窗口不存在，无法下载')
    return
  }

  // 将下载信息存储到队列中
  downloadQueue.set(key, { url, filename, songInfo })

  const savePath = path.join(userDownloadDir, filename)

  // 使用Node.js直接下载，避免浏览器弹窗
  const protocol = url.startsWith('https:') ? https : http
  const options = {
    headers: {
      'User-Agent': 'Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
      'Referer': 'https://www.kuwo.cn'
    }
  }
  const request = protocol.get(url, options, (response) => {
    if (response.statusCode !== 200) {
      console.error('下载失败，状态码:', response.statusCode)
      // 清理队列
      downloadQueue.delete(key)
      mainWindow.webContents.send('download-done', {
        key,
        filename,
        state: 'interrupted',
        path: savePath
      })
      return
    }

    const total = parseInt(response.headers['content-length'], 10) || 0
    let received = 0

    const fileStream = fs.createWriteStream(savePath)

    response.on('data', (chunk) => {
      received += chunk.length
      if (total > 0) {
        const progress = Math.round((received / total) * 100)
        // 格式化已下载大小
        const formatFileSize = (bytes) => {
          if (bytes === 0) return '0B'
          const k = 1024
          const sizes = ['B', 'KB', 'MB', 'GB']
          const i = Math.floor(Math.log(bytes) / Math.log(k))
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i]
        }

        const progressData = {
          key,
          filename,
          progress,
          received,
          total,
          downloadedSize: formatFileSize(received)
        }
        mainWindow.webContents.send('download-progress', progressData)
      }
    })

    fileStream.on('finish', async () => {
      fileStream.close()
      // 清理队列
      downloadQueue.delete(key)

      // 先计算 MD5
      let md5 = null
      try {
        md5 = await computeFileMd5(savePath)
      } catch {}

      // 1. 准备要保存的歌曲对象
      const downloadedSong = {
        MUSICRID: songInfo.MUSICRID,
        NAME: songInfo.NAME,
        ALBUM: songInfo.ALBUM,
        ARTIST: songInfo.ARTIST,
        DURATION: songInfo.DURATION,
        cover: songInfo.cover,
        path: savePath, // 保存文件的绝对路径
        source: 'downloaded', // 标记来源
        hash: md5,
        filename
      }
      // 写入 SQLite（哈希优先、包含 MUSICRID/filename）
      try { db.upsertSong(downloadedSong) } catch (e) { console.error('[download upsert err]', e) }

      // 回传完成事件时也带上 MD5
      // 尝试同步下载封面（若有 cover 字段）并更新 localCover
      try {
        // 如果没有 md5，则按策略跳过封面下载
        if (!md5) {
          console.log('no md5 for downloaded song, skip cover download')
        } else {
          const coversDir = path.join(defaultDownloadDir, 'covers')
          if (!fs.existsSync(coversDir)) fs.mkdirSync(coversDir, { recursive: true })
          const coverUrl = songInfo?.cover || downloadedSong.cover
          // 如果没有图片链接则跳过
          if (!coverUrl) {
            console.log('no cover url for downloaded song, skip')
          } else {
            try {
              // 使用 replaceSize 替换图片尺寸
              const sizedUrl = replaceSize(coverUrl, 500)
              const u = new URL(sizedUrl)
              const ext = path.extname(u.pathname) || '.jpg'
              // 保存文件名始终为 md5，确保一致性
              const finalKey = md5
              const coverSavePath = path.join(coversDir, `${finalKey}${ext}`)
              // 如果已存在则跳过下载
              if (!fs.existsSync(coverSavePath)) {
                const imgResp = await axiosRaw.get(sizedUrl, {
                  headers: {
                    'User-Agent': userAgentMobile[Math.floor(Math.random() * userAgentMobile.length)]
                  },
                  responseType: 'arraybuffer',
                  timeout: 15000
                })
                // 若返回不是图片则跳过（简单检测 content-type）
                const ct = imgResp.headers && imgResp.headers['content-type']
                if (!ct || !ct.startsWith('image/')) {
                  console.log('cover response not image, skip')
                } else {
                  fs.writeFileSync(coverSavePath, Buffer.from(imgResp.data))
                  // 更新数据库的 localCover 字段（按 md5 更新）
                  try { db.updateSongLocalCoverByHash?.(md5, coverSavePath) } catch (e) { console.error('[update localCover after download err]', e) }
                  // 把 localCover 放到 downloadedSong 以便前端使用
                  downloadedSong.localCover = coverSavePath
                }
              } else {
                // 已存在，仍更新 db 字段以防丢失
                try { db.updateSongLocalCoverByHash?.(md5, coverSavePath) } catch (e) { console.error('[update localCover after download err]', e) }
                downloadedSong.localCover = coverSavePath
              }
            } catch (e) {
              console.error('download cover failed:', e?.message)
            }
          }
        }
      } catch (e) {
        console.error('cover save error:', e)
      }

      mainWindow.webContents.send('download-done', {
        key,
        filename,
        state: 'completed',
        path: savePath,
        md5,
        localCover: downloadedSong.localCover || null
      })

      if (!fs.existsSync(LrcDownloadDir)) {
        fs.mkdirSync(LrcDownloadDir, { recursive: true })
      }
      // 下载歌曲完成后，自动下载歌词（文件名附带 hash 以避免冲突）
      if (songInfo && songInfo.MUSICRID) {
        const { MUSICRID, NAME, ARTIST } = songInfo
        try {
          // 构建歌词文件名
          const safeArtist = (ARTIST || '未知艺术家').replace(/[\\/:*?"<>|]/g, '_')
          const safeName = (NAME || '未知歌曲').replace(/[\\/:*?"<>|]/g, '_')
          const lyricsFileName = md5 ? `${md5}.lrc` : `${safeArtist} - ${safeName}.lrc`
          const lyricsPath = path.join(LrcDownloadDir, lyricsFileName)
          // 如果有同名文件则直接退出
          if (fs.existsSync(lyricsPath)) return
          // 获取歌词URL
          const lyricsUrl = `https://www.kuwo.cn/openapi/v1/www/lyric/getlyric?musicId=${MUSICRID.split('_')[1]}&httpsStatus=1&plat=web_www&from=`
          const response = await axios.get(lyricsUrl, {
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              'Referer': `https://www.kuwo.cn/play_detail/${MUSICRID.split('_')[1]}`
            }
          })
          if (response.data && response.data.code === 200 && response.data.data && response.data.data.lrclist) {
            // 将歌词数组转换为LRC格式
            const lrcContent = response.data.data.lrclist.map(item => {
              const time = parseFloat(item.time)
              const minutes = Math.floor(time / 60)
              const seconds = Math.floor(time % 60)
              const milliseconds = Math.floor((time % 1) * 1000)
              return `[${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}]${item.lineLyric}`
            }).join('\n')
            // 保存歌词文件
            fs.writeFileSync(lyricsPath, lrcContent, 'utf-8')
          } else {
            console.log('未找到歌词数据')
          }
        } catch (error) {
          console.error('download err:', error)
        }
      }

    })

    fileStream.on('error', (error) => {
      // 清理队列
      downloadQueue.delete(key)
      mainWindow.webContents.send('download-done', {
        key,
        filename,
        state: 'interrupted',
        path: savePath
      })
    })

    response.pipe(fileStream)
  })

  request.on('error', (error) => {
    console.error('下载请求错误:', error)
    // 清理队列
    downloadQueue.delete(key)
    mainWindow.webContents.send('download-done', {
      key,
      filename,
      state: 'interrupted',
      path: savePath
    })
  })

  request.setTimeout(30000, () => {
    console.error('下载超时')
    // 清理队列
    downloadQueue.delete(key)
    request.destroy()
    mainWindow.webContents.send('download-done', {
      key,
      filename,
      state: 'interrupted',
      path: savePath
    })
  })
})

// 要扫描的目录
const SCAN_DIRS_KEY = 'scanDirs'

/**
 * 从 store 读取扫描目录（默认目录）
 * 返回：[{ path, name }]
 */
ipcMain.handle('get-default-music-dirs', async () => {
  const arr = store.get(SCAN_DIRS_KEY, [])
  return arr.map(p => ({ path: p, name: path.basename(p) || p }))
})

/**
 * 选择要扫描的文件夹（多选）
 * 逻辑：
 *  - 用户选择后合并到 store（去重、仅保留存在的路径）
 *  - 返回更新后的目录列表
 */
ipcMain.handle('select-scan-folders', async () => {
  const res = await dialog.showOpenDialog({
    properties: ['openDirectory', 'multiSelections']
  })
  if (res.canceled) return { success: false, message: '取消选择' }

  const cur = new Set(store.get(SCAN_DIRS_KEY, []))
  for (const p of res.filePaths) {
    try { if (p && fs.existsSync(p)) cur.add(p) } catch {}
  }
  const merged = Array.from(cur)
  store.set(SCAN_DIRS_KEY, merged)

  return { success: true, dirs: merged.map(p => ({ path: p, name: path.basename(p) || p })) }
})

/**
 * 从 store 删除一个目录
 * 入参：delPath: string
 * 返回：{ success, dirs }
 */
ipcMain.handle('remove-scan-dir', async (e, delPath) => {
  const next = (store.get(SCAN_DIRS_KEY, []) || []).filter(p => p !== delPath)
  store.set(SCAN_DIRS_KEY, next)
  return { success: true, dirs: next.map(p => ({ path: p, name: path.basename(p) || p })) }
})

/**
 * 手动选择音频文件并导入到 importedSongs
 * - 使用规范化绝对路径作为 Key 去重
 * - 若文件已存在于 store.songs（下载缓存）或 importedSongs，则跳过，不重复添加
 * - 仅对“未命中缓存”的文件解析 metadata；命中下载缓存的文件直接跳过（读取时会从下载目录展示）
 */
ipcMain.handle('select-audio-files', async () => {
  try {
    const result = await dialog.showOpenDialog({
      properties: ['openFile', 'multiSelections'],
      filters: [{ name: '音频文件', extensions: ['mp3', 'flac', 'm4a', 'wav', 'aac', 'ogg', 'ape'] }]
    })
    if (result.canceled) return { success: false, message: '取消选择' }

    cleanStoreSongs()

    // 使用数据库作为唯一事实来源（除 scanDirs 仍走 store）
    const existingSongs = db.listSongs()
    const existedPathKeys = new Set(existingSongs.filter(s => s.path).map(s => toKey(s.path)))
    const existedHashes = new Set(existingSongs.map(s => s.hash).filter(Boolean))

    const added = []
    let skipped = 0

    // 遍历所选文件（先按路径判重，路径未命中再按 hash）
    for (const filePath of result.filePaths) {
      const key = toKey(filePath)

      // 优先按路径去重，避免对已有文件重复计算 MD5
      if (existedPathKeys.has(key)) {
        skipped++
        continue
      }

      // 若路径未命中，再计算 MD5 并按 hash 去重/修复（可能是移动文件）
      let md5 = null
      try { md5 = await computeFileMd5(filePath) } catch {}

      if (md5 && existedHashes.has(md5)) {
        try {
          const existing = db.getSongByHash?.(md5)
          if (existing && existing.path !== filePath) {
            try {
              db.updateSongPathByHash?.(md5, filePath, path.basename(filePath))
              existedPathKeys.add(key)
              console.log(`[select-audio] updated song path for hash ${md5} -> ${filePath}`)
            } catch (e) { console.error('[select-audio] update path by hash failed', e) }
          }
        } catch (e) {}
        skipped++
        continue
      }

      try {
        const metadata = await mm.parseFile(filePath).catch(() => null)
        const common = metadata?.common || {}
        const duration = metadata?.format?.duration || 0
        const ft = extractArtistTitleFromFilename(filePath)
        added.push({
          NAME: common.title || ft.title || path.basename(filePath, path.extname(filePath)),
          ARTIST: common.artist || ft.artist || '',
          ALBUM: common.album || '',
          DURATION: duration,
          path: filePath,
          source: 'imported',
          MUSICRID: md5 ? `imported_${md5}` : `imported_${path.basename(filePath)}`,
          filename: path.basename(filePath),
          hash: md5 // 记录哈希
        })
      } catch {
        // 解析失败跳过
      }
    }
    // 批量写入数据库（哈希优先、路径兜底由 UPSERT 保证）
    if (added.length) {
      try { db.bulkUpsertSongs(added) } catch {}
    }

    return { success: true, songs: added, skipped }
  } catch (error) {
    return { success: false, message: error.message }
  }
})

/**
 * 扫描目录中的音频文件并写入 importedSongs（仅扫描顶层文件，不递归）
 * - 目录来源：store('scanDirs')
 * - 已下载(songs) 或已导入(importedSongs) 的文件将“跳过”，统计到 skipped
 * - 命中 songs 时不解析 metadata，直接使用缓存原信息；未命中才解析
 * - 返回新增数量与跳过数量
 */
ipcMain.handle('scan-audio-in-folders', async () => {
  try {
    const folders = store.get('scanDirs', [])
    if (!folders.length) return { success: false, message: '未配置扫描目录' }
    // 扫描前先清理
    cleanStoreSongs()

    function walkDir(root, list = []) {
      if (!fs.existsSync(root)) return list
      const ents = fs.readdirSync(root, { withFileTypes: true })
      for (const ent of ents) {
        const full = path.join(root, ent.name)
        if (ent.isDirectory()) walkDir(full, list)
        else if (ent.isFile()) list.push(full)
      }
      return list
    }

    const AUDIO_EXT = new Set(['.mp3', '.flac', '.m4a', '.wav', '.aac', '.ogg', '.ape'])
    let skipped = 0
    const candidates = []
    // 从数据库构建已存在集合（路径/哈希）
    const existingSongs = db.listSongs()
    const existedPathKeys = new Set(existingSongs.filter(s => s.path).map(s => toKey(s.path)))
    const existedHashes = new Set(existingSongs.map(s => s.hash).filter(Boolean))
    for (const dir of folders) {
      const files = walkDir(dir)
      for (const full of files) {
        const ext = path.extname(full).toLowerCase()
        if (!AUDIO_EXT.has(ext)) continue
        const key = toKey(full)
        if (existedPathKeys.has(key)) {
          skipped++
          continue
        }
        candidates.push({ full, key })
      }
    }

    // 构建新增歌曲（并发限制 + 哈希优先去重）
    const newSongs = []
    const LIMIT = 6 // 建议 4~8 之间
    let idx = 0
    // 新增会话级哈希集合，避免本次扫描内重复
    const sessionHashSet = new Set()

    async function worker() {
      while (idx < candidates.length) {
        const cur = candidates[idx++]
        if (!cur) break
        const { full, key } = cur
        try {
          // 路径再校验（稳妥）
          if (existedPathKeys.has(key)) {
            skipped++
            continue
          }

          // 计算 MD5，用哈希优先去重
          let md5 = null
          try { md5 = await computeFileMd5(full) } catch {}
          if (md5) {
            // 如果数据库已有该 hash，尝试更新其 path（文件被移动的场景）
            if (existedHashes.has(md5)) {
              try {
                const existing = db.getSongByHash?.(md5)
                // 如果数据库中存在但路径不同，则更新为当前路径
                if (existing && existing.path !== full) {
                  try {
                    // 使用专门的更新方法，避免无意中改变其他字段
                    if (db.updateSongPathByHash) {
                      db.updateSongPathByHash(md5, full, path.basename(full))
                    } else {
                      db.upsertSong({ hash: md5, path: full, filename: path.basename(full) })
                    }
                    // 更新内存集合，避免本次扫描再次处理
                    existedPathKeys.add(key)
                    console.log(`[scan] updated song path for hash ${md5} -> ${full}`)
                  } catch (e) {
                    console.error('[scan] update path by hash failed', e)
                  }
                }
              } catch (e) {
                // ignore
              }
              skipped++
              continue
            }
            if (sessionHashSet.has(md5)) {
              skipped++
              continue
            }
            sessionHashSet.add(md5)
          }

          // 解析元数据（失败不影响入库）
          const md = await mm.parseFile(full).catch(() => null)
          const common = md?.common || {}
          const duration = md?.format?.duration || 0
          // 回退：文件名拆分出 artist/title
          const ft = extractArtistTitleFromFilename(full)

          newSongs.push({
            NAME: common.title || ft.title || path.basename(full, path.extname(full)),
            ARTIST: common.artist || ft.artist || '',
            ALBUM: common.album || '',
            DURATION: duration,
            path: full,
            source: 'imported',
            MUSICRID: md5 ? `imported_${md5}` : `imported_${path.basename(full)}`,
            filename: path.basename(full),
            hash: md5
          })
        } catch {
          // 单个文件异常忽略
        }
      }
    }

    await Promise.all(Array.from({ length: Math.min(LIMIT, candidates.length) }, () => worker()))

    // 批量写入数据库
    if (newSongs.length) {
      try { db.bulkUpsertSongs(newSongs) } catch (e) { console.error('[scan upsert err]', e) }
    }
    console.log('[scan] candidates:', candidates.length, 'new:', newSongs.length)
    console.log('[scan] db total after:', db.listSongs().length)
    return { success: true, added: newSongs.length, skipped, songs: newSongs }
  } catch (err) {
    console.log('[scan] error:', err)
    return { success: false, message: err?.message || '扫描失败' }
  }
})

// 通过哈希补全封面：请求接口 -> 保存到本地 -> 更新数据库
ipcMain.handle('fetch-and-update-cover-by-hash', async (event, { hash }) => {
  try {
    if (!hash) return null
    // 若已有进行中的请求则复用（尽早去重，避免重复日志与多次处理）
    if (inFlightCoverRequests.has(hash)) {
      try {
        return await inFlightCoverRequests.get(hash)
      } catch {
        return null
      }
    }

    // 先查 DB：优先返回 localCover，其次 cover
    try {
      const hit = db.getSongByHash?.(hash)
      if (hit) {
        if (hit.localCover && fs.existsSync(hit.localCover)) {
          console.log('local cover: ', hit.localCover)
          return hit.localCover
        }
        if (hit.cover?.length) {
          console.log('online cover: ', hit.cover)
          return hit.cover
        }
      }
    } catch {}

    // 检查是否之前请求过但没有图片
    try {
      const failedRecord = db.getFailedCoverHash?.(hash)
      if (failedRecord) {
        console.log(`${hash} get cover failed: ${failedRecord.reason}`)
        return null
      }
    } catch {}

    // 请求封面接口（in-flight 去重）
    if (inFlightCoverRequests.has(hash)) {
      try {
        return await inFlightCoverRequests.get(hash)
      } catch {
        return null
      }
    }

    const p = (async () => {
      try {
        const COVER_API = `https://m.kugou.com/app/i/getSongInfo.php?hash=${hash}&cmd=playInfo`
        const { data } = await axiosRaw.get(COVER_API, {
          headers: {
            'User-Agent': userAgentMobile[Math.floor(Math.random() * userAgentMobile.length)]
          },
          timeout: 10000
        })
        let coverUrl = data?.album_img || data?.imgUrl
        console.log('get cover success: ', coverUrl)

        if (!coverUrl) {
          try { db.recordFailedCoverHash?.(hash, 'no_cover_url') } catch (e) { console.error('[record no cover hash err]', e) }
          return null
        }

        coverUrl = coverUrl.replace('{size}', 500)

        // 保存到本地 covers 目录
        const coversDir = path.join(defaultDownloadDir, 'covers')
        if (!fs.existsSync(coversDir)) fs.mkdirSync(coversDir, { recursive: true })
        const u = new URL(coverUrl)
        const ext = path.extname(u.pathname) || '.jpg'
        const savePath = path.join(coversDir, `${hash}${ext}`)

        const imgResp = await axiosRaw.get(coverUrl, {
          headers: {
            'User-Agent': userAgentMobile[Math.floor(Math.random() * userAgentMobile.length)]
          },
          responseType: 'arraybuffer',
          timeout: 15000
        })
        fs.writeFileSync(savePath, Buffer.from(imgResp.data))

        // 写回数据库：优先记录 localCover（本地文件），保留 cover 作为在线封面备用
        try { db.updateSongLocalCoverByHash?.(hash, savePath) } catch (e) { console.error('[update localCover err]', e) }

        // 在内存 audioState.playList 中同步 localCover，避免后续下发的 audioState 覆盖渲染层的本地封面
        try {
          if (Array.isArray(audioState.playList)) {
            audioState.playList = audioState.playList.map(it => {
              if (it.hash && it.hash === hash) {
                return { ...it, localCover: savePath }
              }
              return it
            })
          }
        } catch (e) {
          console.error('[sync audioState playList localCover err]', e)
        }
        if (mainWindow && !mainWindow.isDestroyed()) {
          // 仅通知渲染进程封面已更新，渲染端负责局部合并 localCover
          try { mainWindow.webContents.send('cover-updated', { hash, localCover: savePath }) } catch (e) {}
        }
        return savePath
      } catch (downloadError) {
        console.error(`Hash ${hash} download cover failed:`, downloadError?.message)
        return null
      }
    })()

    inFlightCoverRequests.set(hash, p)
    try {
      return await p
    } finally {
      inFlightCoverRequests.delete(hash)
    }

  } catch (err) {
    // 请求失败不记录，只记录日志
    console.error('fetch-and-update-cover-by-hash failed:', err?.message)
    return null
  }
})

