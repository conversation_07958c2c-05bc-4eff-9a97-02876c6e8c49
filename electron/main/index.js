const { app, BrowserWindow, dialog, ipc<PERSON>ain, Menu, Tray } = require('electron')
const path = require('path')
const express = require('express')
const fs = require('fs')
const https = require('https')
const http = require('http')
import { proxyRequest } from '../../src/utils/server.js'

const mm = require('music-metadata') // 引入music-metadata库

let mainWindow = null
let audioWindow = null
let downloadWindow = null
let tray = null
let audioState = {
  isPlaying: false,
  currentTime: 0,
  currentTrack: null,
  duration: 0,
  volume: 0.5,
  ended: false,
  songInfo: null,
  playList: [], // 新增
  currentMusicId: null // 新增
}
const isDev = process.env.NODE_ENV === 'development'
Menu.setApplicationMenu(null)
const createWindow = () => {
  mainWindow = new BrowserWindow({
    width: 1600,
    height: 789,
    // width: 1116,
    // height: 789,
    backgroundColor: '#fff',
    webPreferences: {
      nodeIntegration: true,
      preload: path.join(__dirname, '../preload/index.js'),
      //此参数禁用当前窗口的同源策略
      webSecurity: false,
    },
    show: false,
    resizable: true,
    // transparent: true,
    minWidth: 1116,
    minHeight: 789
  })
  //接收来自渲染进程的消息
  let searchValue = ''
  ipcMain.on('save-search', (event, value) => {
    searchValue = value
  })
  ipcMain.handle('get-search', () => {
    return searchValue
  })

  const startUrl = isDev ? 'http://localhost:5173' : `file://${__dirname}/index.html`
  mainWindow.loadURL(startUrl)
  if (isDev) {
    mainWindow.webContents.openDevTools()
  }
  // // mainWindow.webContents.openDevTools()
  // mainWindow.on('closed', () => {
  //     mainWindow = null
  // })
  // mainWindow.on('close', event => {
  //     // 阻止退出程序
  //     event.preventDefault()
  //     // 取消任务栏显示
  //     //mainWindow.setSkipTaskbar(true)
  //     // 隐藏主程序窗口
  //     //mainWindow.hide()
  // })
  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })
  // 拦截主窗口关闭：若有其他窗口，隐藏主窗口
  mainWindow.on('close', (event) => {
    // 检查是否存在未销毁的其他窗口（如 audioWindow）
    const hasOtherWindows = BrowserWindow.getAllWindows().some(
      win => win !== mainWindow && !win.isDestroyed()
    )
    if (hasOtherWindows) {
      event.preventDefault() // 阻止关闭
      mainWindow.hide()      // 隐藏主窗口
    }
  })
  // 主窗口刷新后恢复音频状态
  mainWindow.webContents.on('did-finish-load', () => {
    mainWindow.webContents.send('audio-state', audioState)
  })
  // const menu = Menu.buildFromTemplate(menuBar)
  // Menu.setApplicationMenu(null)
}

const createTray = () => {
  function toggle() {
    mainWindow.isVisible() ? mainWindow.hide() : mainWindow.show()
    mainWindow.isVisible() ? mainWindow.setSkipTaskbar(false) : mainWindow.setSkipTaskbar(true)
  }

  tray = new Tray(path.resolve(__dirname, '../../public/imgs/img.png'))
  const contextMenu = Menu.buildFromTemplate([
    {
      label: '显示/隐藏雷噶得',
      click() {
        toggle()
      }
    },
    {
      label: '退出',
      click() {
        tray.destroy()
        app.exit()
      }
    }
  ])
  tray.setToolTip('雷噶得')
  tray.setContextMenu(contextMenu)
  tray.on('click', () => {
    toggle()
  })
}

// 创建音频窗口
function createAudioWindow() {
  audioWindow = new BrowserWindow({
    width: 0,
    height: 0,
    show: false,
    frame: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  })
  audioWindow.loadURL(path.join(__dirname, '../../music.html'),)

  // 防止音频窗口意外显示
  audioWindow.on('ready-to-show', () => {
    if (audioWindow) audioWindow.hide()
  })

  // 监听音频窗口关闭
  audioWindow.on('closed', () => {
    audioWindow = null
  })
}

const createDownloadWindow = () => {
  if (downloadWindow && !downloadWindow.isDestroyed()) {
    downloadWindow.show()
    downloadWindow.focus()
    return
  }
  downloadWindow = new BrowserWindow({
    width: 400,
    height: 600,
    minWidth: 400,
    minHeight: 600,
    backgroundColor: '#fff',
    webPreferences: {
      preload: path.join(__dirname, '../preload/index.js'),
      webSecurity: false
    }
  })
  const startUrl = isDev ? 'http://localhost:5173/?type=sub' : `file://${__dirname}/index.html?type=sub`
  downloadWindow.loadURL(startUrl)
  if (isDev) {
    downloadWindow.webContents.openDevTools()
  }

  downloadWindow.on('ready-to-show', () => {
    downloadWindow.show()
  })

  downloadWindow.on('close', () => {
    downloadWindow.hide()
  })

  downloadWindow.on('closed', () => {
    downloadWindow = null
  })
}

ipcMain.on('open-download-window', () => {
  createDownloadWindow()
})

// 防止多开
const gotTheLock = app.requestSingleInstanceLock()
if (!gotTheLock) {
  app.exit()
}

app.on('ready', () => {
  createWindow()
  createAudioWindow()
  createTray()
})

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

function isValidWindow(window) {
  return window && !window.isDestroyed() && window.webContents
}

ipcMain.on('play-audio', (event, { trackUrl, currentTime = 0, songInfo, musicId }) => {
  audioState = {
    ...audioState,
    isPlaying: true,
    currentTime,
    currentTrack: trackUrl,
    ended: false,
    songInfo,
    currentMusicId: musicId || (songInfo && songInfo.MUSICRID) || null // 新增
  }
  if (audioWindow) {
    audioWindow.webContents.send('play-audio', { trackUrl, currentTime })
  }
})

// 新增：设置播放列表
ipcMain.on('set-playlist', (event, playList) => {
  audioState.playList = playList || []
})
// 新增：设置当前播放歌曲ID
ipcMain.on('set-current-music-id', (event, musicId) => {
  audioState.currentMusicId = musicId
})

ipcMain.on('pause-audio', () => {
  // 在暂停时获取当前时间（从音频窗口获取）
  const currentTime = audioState.currentTime
  audioState = {
    ...audioState,
    isPlaying: false,
    currentTime
  }

  if (audioWindow) {
    audioWindow.webContents.send('pause-audio')
  }
})
ipcMain.on('set-volume', (event, volume) => {
  audioState = {
    ...audioState,
    volume
  }
  // 通知音频窗口更新音量
  if (audioWindow) {
    audioWindow.webContents.send('set-volume', volume)
  }
  // 通知主窗口更新UI
  if (mainWindow) {
    mainWindow.webContents.send('audio-state', audioState)
  }
})

ipcMain.on('audio-duration', (event, duration) => {
  audioState = {
    ...audioState,
    duration
  }
  audioWindow.webContents.send('audio-duration', duration)
})

ipcMain.on('seek-audio', (event, currentTime) => {
  audioState = {
    ...audioState,
    currentTime
  }
  // 通知音频窗口更新播放位置
  if (audioWindow) {
    audioWindow.webContents.send('seek-audio', currentTime)
  }
  // 通知主窗口更新UI
  if (mainWindow) {
    mainWindow.webContents.send('audio-state', audioState)
  }
})

ipcMain.on('update-time', (event, currentTime) => {
  audioState = {
    ...audioState,
    currentTime
  }
  // 通知主窗口更新UI
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('audio-state', audioState)
  }
})

ipcMain.on('ended', () => {
  audioState.ended = true
  audioState.isPlaying = false
  // 通知主窗口更新UI
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('audio-state', audioState)
  }
})

// 监听音频错误信息
ipcMain.on('audio-error', (event, errorInfo) => {
  console.error('音频播放错误:', errorInfo)
  // 可以在这里添加错误处理逻辑，比如显示通知给用户
})

// 监听音频日志信息
ipcMain.on('audio-log', (event, logInfo) => {
  console.log('音频播放日志:', logInfo)
})

// 获取完整audioState
ipcMain.handle('get-audio-state', () => {
  return audioState
})

// 获取当前应用目录
const appDir = app.getAppPath()
// 拼接 download 目录
const defaultDownloadDir = path.join(appDir, 'downloads')
// 启动时确保 download 目录存在
if (!fs.existsSync(defaultDownloadDir)) {
  fs.mkdirSync(defaultDownloadDir, { recursive: true })
}
// 用这个目录作为下载目录
let userDownloadDir = defaultDownloadDir
// 选择下载目录
ipcMain.handle('select-download-dir', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openDirectory']
  })
  if (!result.canceled && result.filePaths.length > 0) {
    userDownloadDir = result.filePaths[0]
    return userDownloadDir
  }
  return null
})

// 返回下载目录给前端
ipcMain.handle('get-download-dir', async () => {
  return userDownloadDir
})

// 2. 将此函数修改为异步函数
ipcMain.handle('get-local-songs', async () => {
  if (!fs.existsSync(userDownloadDir)) {
    return []
  }
  const files = fs.readdirSync(userDownloadDir).filter(f => {
    const ext = path.extname(f).toLowerCase()
    return ['.mp3', '.flac', '.m4a', '.wav', '.aac', '.ogg', 'mgg'].includes(ext)
  })

    // 3. 使用music-metadata解析音频时长
  return await Promise.all(
    files.map(async (file) => {
      const filePath = path.join(userDownloadDir, file)
      
      // 从文件名中提取歌手和歌曲名
      const nameWithoutExt = path.basename(file, path.extname(file))
      const parts = nameWithoutExt.split(' - ')
      const artist = parts.length > 1 ? parts[0] : '未知艺术家'
      const title = parts.length > 1 ? parts.slice(1).join(' - ') : nameWithoutExt
      
      // 获取文件大小
      const stats = fs.statSync(filePath)
      const fileSize = stats.size
      
      // 使用music-metadata解析音频时长
      let duration = 0
      let bitrate = 0
      let sampleRate = 0
      
      try {
        const metadata = await mm.parseFile(filePath)
        if (metadata && metadata.format && metadata.format.duration) {
          duration = Math.floor(metadata.format.duration)
        }
        
        if (metadata && metadata.format && metadata.format.bitrate) {
          bitrate = metadata.format.bitrate
        }
        
        if (metadata && metadata.format && metadata.format.sampleRate) {
          sampleRate = metadata.format.sampleRate
        }
        
      } catch (error) {
        console.log(`解析 ${file} 失败:`, error.message)
        // 解析失败时，时长保持为0
      }
      
      return {
        filename: file,
        path: filePath,
        duration: duration,
        artist: artist,
        title: title,
        album: '未知专辑',
        bitrate: bitrate,
        sampleRate: sampleRate,
        format: path.extname(file).substring(1).toUpperCase(),
        fileSize: fileSize
      }
    })
  )
})

// 下载
ipcMain.on('download-song', (event, { url, filename, key }) => {
  console.log('主进程收到下载请求:', { url, filename, key })
  
  if (!mainWindow) {
    console.error('主窗口不存在，无法下载')
    return
  }

  const savePath = path.join(userDownloadDir, filename)

  // 使用Node.js直接下载，避免浏览器弹窗
  const protocol = url.startsWith('https:') ? https : http

  const request = protocol.get(url, (response) => {
    if (response.statusCode !== 200) {
      console.error('下载失败，状态码:', response.statusCode)
      mainWindow.webContents.send('download-done', {
        key,
        filename,
        state: 'interrupted',
        path: savePath
      })
      return
    }

    const total = parseInt(response.headers['content-length'], 10) || 0
    let received = 0

    const fileStream = fs.createWriteStream(savePath)

    response.on('data', (chunk) => {
      received += chunk.length
      if (total > 0) {
        const progress = Math.round((received / total) * 100)
        // 格式化已下载大小
        const formatFileSize = (bytes) => {
          if (bytes === 0) return '0B'
          const k = 1024
          const sizes = ['B', 'KB', 'MB', 'GB']
          const i = Math.floor(Math.log(bytes) / Math.log(k))
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i]
        }

        const progressData = {
          key,
          filename,
          progress,
          received,
          total,
          downloadedSize: formatFileSize(received)
        }
        mainWindow.webContents.send('download-progress', progressData)
      }
    })

    fileStream.on('finish', () => {
      fileStream.close()
      mainWindow.webContents.send('download-done', {
        key,
        filename,
        state: 'completed',
        path: savePath
      })
    })

    fileStream.on('error', (error) => {
      mainWindow.webContents.send('download-done', {
        key,
        filename,
        state: 'interrupted',
        path: savePath
      })
    })

    response.pipe(fileStream)
  })

  request.on('error', (error) => {
    console.error('下载请求错误:', error)
    mainWindow.webContents.send('download-done', {
      key,
      filename,
      state: 'interrupted',
      path: savePath
    })
  })

  request.setTimeout(30000, () => {
    console.error('下载超时')
    request.destroy()
    mainWindow.webContents.send('download-done', {
      key,
      filename,
      state: 'interrupted',
      path: savePath
    })
  })
})

const server = express()
server.use(express.static(__dirname + '/public'))
server.listen(9966)

// server.get('/search', (req, res) => {
//   const { name } = req.query
//   searchRequest('search', name).then(data => {
//     res.send(data)
//   })
// })
// server.get('/home', (req, res) => {
//   const { page } = req.query
//   searchRequest('home', page).then(data => {
//     res.send(data)
//   })
// })

// server.get('/proxy', async (req, res) => {
//   const { url } = req.query
//   proxyRequest(url).then(data => {
//     res.send(data.data)
//   })
// })

server.get('/proxy', async (req, res) => {
  const args = req.query
  proxyRequest(args).then(result => {
    res.send(result.data)
  })
})
