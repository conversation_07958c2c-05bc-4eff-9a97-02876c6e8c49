import { wrapper } from 'axios-cookiejar-support'
import { <PERSON><PERSON><PERSON>ar } from 'tough-cookie'
import axios from 'axios'
import { userAgentList } from '../../src/utils/userAgent.js'

const jar = new CookieJar()
const client = wrapper(axios.create({ jar, withCredentials: true }))
const m = 'Hm_Iuvt_cdb524f42f23cer9b268564v7y735ewrq2324'

function f(t, e) {
  if (null == e || e.length <= 0)
    return null
  for (var n = '', i = 0; i < e.length; i++)
    n += e.charCodeAt(i).toString()
  const o = Math.floor(n.length / 5)
    , r = parseInt(n.charAt(o) + n.charAt(2 * o) + n.charAt(3 * o) + n.charAt(4 * o) + n.charAt(5 * o))
    , c = Math.ceil(e.length / 2)
    , l = Math.pow(2, 31) - 1
  if (r < 2)
    return null
  let d = Math.round(1e9 * Math.random()) % 1e8
  for (n += d; n.length > 10;)
    n = (parseInt(n.substring(0, 10)) + parseInt(n.substring(10, n.length))).toString()
  n = (r * n + c) % l
  let f = ''
    , h = ''
  for (i = 0; i < t.length; i++)
    h += (f = parseInt(t.charCodeAt(i) ^ Math.floor(n / l * 255))) < 16 ? '0' + f.toString(16) : f.toString(16),
      n = (r * n + c) % l
  for (d = d.toString(16); d.length < 8;)
    d = '0' + d
  return h + d
}

async function getRecDetailProxy({ id, page }) {
  const url1 = 'https://www.kuwo.cn/'
  const url2 = `https://www.kuwo.cn/api/www/playlist/playListInfo?pid=${id}&pn=${page}&rn=20&httpsStatus=1&plat=web_www&from=`
  const userAgent = userAgentList[Math.floor(Math.random() * userAgentList.length)]
  try {
    // 第一步：预请求获取 cookie
    const res = await client.get(url1, {
      headers: {
        'User-Agent': userAgent
      }
    })
    const cookie = res.headers['set-cookie'][0]
    const value = cookie.match(/=(.*?);/)[1]
    // 第二步：带上新 cookie 正式请求
    const response = await client.get(url2, {
      headers: {
        'Secret': f(value, m),
        'User-Agent': userAgent
      }
    })
    return response.data
  } catch (err) {
    return { status: 500, message: '请求失败' }
  }
}

export { getRecDetailProxy }