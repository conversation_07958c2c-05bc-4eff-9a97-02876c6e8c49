const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron')

contextBridge.exposeInMainWorld('electronAPI', {
    //接收发送过来的搜索内容
    sendSearch: value => ipcRenderer.send('save-search', value),
    // 监听主进程返回的搜索结果
    searchInitial: cb => ipcRenderer.invoke('get-search').then(cb),
    openDownloadWindow: () => ipcRenderer.send('open-download-window')
  }
)
// 音乐控制API
contextBridge.exposeInMainWorld('audioAPI', {
  play: (trackUrl, currentTime, songInfo, musicId) => ipcRenderer.send('play-audio', {
    trackUrl,
    currentTime,
    songInfo,
    musicId
  }),
  pause: () => ipcRenderer.send('pause-audio'),
  seek: (currentTime) => ipcRenderer.send('seek-audio', currentTime),
  setVolume: (volume) => ipcRenderer.send('set-volume', volume),
  getState: () => ipcRenderer.invoke('get-audio-state'),
  setPlayList: playList => ipcRenderer.send('set-playlist', playList),
  setCurrentMusicId: musicId => ipcRenderer.send('set-current-music-id', musicId),
  onStateUpdate: (callback) => ipcRenderer.on('audio-state', callback),
  removeStateUpdate: (callback) => ipcRenderer.removeListener('audio-state', callback),
  getLocalSongs: () => ipcRenderer.invoke('get-local-songs'),
  downloadSong: (options) => ipcRenderer.send('download-song', options),
  onDownloadProgress: (callback) => {
    const handler = (event, progress) => callback(progress)
    ipcRenderer.on('download-progress', handler)
    // 【关键】返回一个清理函数
    return () => ipcRenderer.removeListener('download-progress', handler)
  },
  onDownloadDone: (callback) => {
    const handler = (event, info) => callback(info)
    ipcRenderer.on('download-done', handler)
    // 【关键】返回一个清理函数
    return () => ipcRenderer.removeListener('download-done', handler)
  },
  getDownloadDir: () => ipcRenderer.invoke('get-download-dir'),
  selectDownloadDir: () => ipcRenderer.invoke('select-download-dir'),
  saveLyrics: (lrc) =>
    ipcRenderer.invoke('save-lyrics', lrc),
  // 获取本地歌词
  getLocalLyrics: (songPath) => ipcRenderer.invoke('get-local-lyrics', songPath),
  // store中的歌曲列表
  getSongs: () => ipcRenderer.invoke('get-songs'),
  setSongs: (songs) => ipcRenderer.invoke('set-songs', songs)
})