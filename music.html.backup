<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>音频播放器 (隐藏窗口)</title>
    <style>
        body {
            display: none;
        }
    </style>
</head>
<body>
<audio id="audio-element" preload="auto"></audio>

<script>
  const { ipc<PERSON><PERSON><PERSON> } = require('electron')
  const audioElement = document.getElementById('audio-element')
  let audioContext = null
  let audioSource = null
  let isFlacFile = false
  let currentFlacStartTime = 0

  // 初始化 Web Audio API
  function initAudioContext() {
    if (!audioContext) {
      audioContext = new (window.AudioContext || window.webkitAudioContext)()
    }
  }

  // 检查是否为FLAC文件
  function isFlacFormat(trackUrl) {
    return trackUrl.toLowerCase().includes('.flac') || trackUrl.toLowerCase().includes('format=flac')
  }

  // 获取音频总时长
  audioElement.addEventListener('loadedmetadata', () => {
    const duration = audioElement.duration
    ipcRenderer.send('audio-duration', duration)
  })

  // 处理标准音频播放
  function handleStandardPlayback(trackUrl, currentTime) {
    // 如果是同一首歌，使用保存的currentTime
    if (audioElement.src === trackUrl && currentTime === 0) {
      currentTime = audioElement.currentTime
    }
    
    if (audioElement.src !== trackUrl) {
      console.log('设置新的音频源:', trackUrl)
      audioElement.src = trackUrl
    }
    
    audioElement.currentTime = currentTime
    
    // 添加错误处理
    audioElement.addEventListener('error', (e) => {
      console.error('音频加载错误:', e)
      console.error('错误详情:', audioElement.error)
      console.error('音频源:', trackUrl)
      
      // 发送错误信息到主窗口
      ipcRenderer.send('audio-error', {
        error: audioElement.error ? {
          code: audioElement.error.code,
          message: audioElement.error.message
        } : 'Unknown error',
        trackUrl: trackUrl,
        timestamp: new Date().toISOString()
      })
    })
    
    audioElement.addEventListener('loadstart', () => {
      console.log('开始加载音频:', trackUrl)
      ipcRenderer.send('audio-log', { type: 'loadstart', trackUrl })
    })
    
    audioElement.addEventListener('canplay', () => {
      console.log('音频可以播放:', trackUrl)
      ipcRenderer.send('audio-log', { type: 'canplay', trackUrl })
    })
    
    audioElement.addEventListener('canplaythrough', () => {
      console.log('音频可以完整播放:', trackUrl)
      ipcRenderer.send('audio-log', { type: 'canplaythrough', trackUrl })
    })
    
    audioElement.play().then(() => {
      console.log('播放成功:', trackUrl)
      ipcRenderer.send('audio-log', { type: 'play-success', trackUrl })
    }).catch(err => {
      console.error('播放失败:', err)
      console.error('音频源:', trackUrl)
      console.error('错误类型:', err.name)
      console.error('错误消息:', err.message)
      
      // 发送播放失败信息到主窗口
      ipcRenderer.send('audio-error', {
        error: {
          name: err.name,
          message: err.message
        },
        trackUrl: trackUrl,
        timestamp: new Date().toISOString()
      })
    })
  }

  // 处理FLAC文件播放
  async function handleFlacPlayback(trackUrl, currentTime) {
    try {
      console.log('处理FLAC文件播放:', trackUrl)
      ipcRenderer.send('audio-log', { type: 'flac-loadstart', trackUrl })
      
      // 初始化音频上下文
      initAudioContext()
      
      // 暂停当前播放
      if (audioSource) {
        audioSource.stop()
        audioSource = null
      }
      
      // 尝试使用fetch获取FLAC文件
      const response = await fetch(trackUrl)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const arrayBuffer = await response.arrayBuffer()
      console.log('FLAC文件加载成功，大小:', arrayBuffer.byteLength)
      
      // 尝试解码FLAC
      try {
        // 使用Web Audio API解码
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
        console.log('FLAC解码成功')
        
        // 创建音频源
        audioSource = audioContext.createBufferSource()
        audioSource.buffer = audioBuffer
        audioSource.connect(audioContext.destination)
        
        // 设置播放位置
        currentFlacStartTime = audioContext.currentTime
        if (currentTime > 0) {
          audioSource.start(0, currentTime)
        } else {
          audioSource.start(0)
        }
        
        // 监听播放结束
        audioSource.onended = () => {
          ipcRenderer.send('ended')
        }
        
        console.log('FLAC播放成功')
        ipcRenderer.send('audio-log', { type: 'flac-play-success', trackUrl })
        
      } catch (decodeError) {
        console.error('FLAC解码失败:', decodeError)
        ipcRenderer.send('audio-error', {
          error: {
            name: 'DecodeError',
            message: 'FLAC文件解码失败，请检查文件是否损坏'
          },
          trackUrl: trackUrl,
          timestamp: new Date().toISOString()
        })
      }
      
    } catch (error) {
      console.error('FLAC播放失败:', error)
      ipcRenderer.send('audio-error', {
        error: {
          name: error.name,
          message: error.message
        },
        trackUrl: trackUrl,
        timestamp: new Date().toISOString()
      })
    }
  }

  // 监听播放命令
  ipcRenderer.on('play-audio', (event, { trackUrl, currentTime = 0 }) => {
    console.log('收到播放命令:', { trackUrl, currentTime })
    
    try {
      isFlacFile = isFlacFormat(trackUrl)
      
      if (isFlacFile) {
        // FLAC文件使用特殊处理
        handleFlacPlayback(trackUrl, currentTime)
      } else {
        // 普通文件使用标准audio元素
        handleStandardPlayback(trackUrl, currentTime)
      }
    } catch (error) {
      console.error('播放命令处理错误:', error)
      ipcRenderer.send('audio-error', {
        error: {
          name: error.name,
          message: error.message
        },
        trackUrl: trackUrl,
        timestamp: new Date().toISOString()
      })
    }
  })
      
      // 添加错误处理
      audioElement.addEventListener('error', (e) => {
        console.error('音频加载错误:', e)
        console.error('错误详情:', audioElement.error)
        console.error('音频源:', trackUrl)
        
        // 发送错误信息到主窗口
        ipcRenderer.send('audio-error', {
          error: audioElement.error ? {
            code: audioElement.error.code,
            message: audioElement.error.message
          } : 'Unknown error',
          trackUrl: trackUrl,
          timestamp: new Date().toISOString()
        })
      })
      
      audioElement.addEventListener('loadstart', () => {
        console.log('开始加载音频:', trackUrl)
        ipcRenderer.send('audio-log', { type: 'loadstart', trackUrl })
      })
      
      audioElement.addEventListener('canplay', () => {
        console.log('音频可以播放:', trackUrl)
        ipcRenderer.send('audio-log', { type: 'canplay', trackUrl })
      })
      
      audioElement.addEventListener('canplaythrough', () => {
        console.log('音频可以完整播放:', trackUrl)
        ipcRenderer.send('audio-log', { type: 'canplaythrough', trackUrl })
      })
      
      audioElement.play().then(() => {
        console.log('播放成功:', trackUrl)
        ipcRenderer.send('audio-log', { type: 'play-success', trackUrl })
      }).catch(err => {
        console.error('播放失败:', err)
        console.error('音频源:', trackUrl)
        console.error('错误类型:', err.name)
        console.error('错误消息:', err.message)
        
        // 发送播放失败信息到主窗口
        ipcRenderer.send('audio-error', {
          error: {
            name: err.name,
            message: err.message
          },
          trackUrl: trackUrl,
          timestamp: new Date().toISOString()
        })
      })
    } catch (error) {
      console.error('播放命令处理错误:', error)
      ipcRenderer.send('audio-error', {
        error: {
          name: error.name,
          message: error.message
        },
        trackUrl: trackUrl,
        timestamp: new Date().toISOString()
      })
    }
  })
  // 监听暂停命令
  ipcRenderer.on('pause-audio', () => {
    const currentTime = audioElement.currentTime
    audioElement.pause()

    // 向主进程发送当前时间
    ipcRenderer.send('update-time', currentTime)
  })

  // 监听跳转到指定位置命令
  ipcRenderer.on('seek-audio', (event, currentTime) => {
    audioElement.currentTime = currentTime

    // 如果当前是暂停状态但有新的播放位置，保持暂停状态
    if (!audioElement.paused) {
      audioElement.play().catch(err => {
        console.error('播放失败:', err)
      })
    }
  })
  // 监听音频音量设置
  ipcRenderer.on('set-volume', (event, volume) => {
    audioElement.volume = volume
  })
  // 监听音频结束事件
  audioElement.addEventListener('ended', () => {
    ipcRenderer.send('ended')
  })

  // 定期发送播放时间更新
  setInterval(() => {
    if (!audioElement.paused) {
      ipcRenderer.send('update-time', audioElement.currentTime)
    }
  }, 1000)

  // 保存状态到本地存储
  window.addEventListener('beforeunload', () => {
    ipcRenderer.send('update-time', audioElement.currentTime)
  })
</script>
</body>
</html>