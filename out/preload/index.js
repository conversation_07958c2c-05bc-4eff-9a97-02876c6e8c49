"use strict";
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require("electron");
const inFlight = /* @__PURE__ */ new Map();
const recentResults = /* @__PURE__ */ new Map();
const RECENT_TTL = 30 * 1e3;
function dedupeInvoke(key, channel, ...args) {
  if (!key) return ipcRenderer.invoke(channel, ...args);
  const cached = recentResults.get(key);
  if (cached && Date.now() - cached.ts < RECENT_TTL) {
    return Promise.resolve(cached.value);
  }
  if (inFlight.has(key)) return inFlight.get(key);
  const p = ipcRenderer.invoke(channel, ...args).then((res) => {
    try {
      recentResults.set(key, { ts: Date.now(), value: res });
    } catch (e) {
    }
    return res;
  }).finally(() => inFlight.delete(key));
  inFlight.set(key, p);
  return p;
}
contextBridge.exposeInMainWorld(
  "electronAPI",
  {
    //接收发送过来的搜索内容
    sendSearch: (value) => ipcRenderer.send("save-search", value),
    // 监听主进程返回的搜索结果
    searchInitial: (cb) => ipcRenderer.invoke("get-search").then(cb),
    openDownloadWindow: () => ipcRenderer.send("open-download-window")
  }
);
contextBridge.exposeInMainWorld("audioAPI", {
  play: (trackUrl, currentTime, songInfo, musicId, playList) => ipcRenderer.invoke("play-audio", {
    trackUrl,
    currentTime,
    songInfo,
    musicId,
    playList
  }),
  pause: () => ipcRenderer.invoke("pause-audio"),
  nextAudio: () => ipcRenderer.invoke("next-audio"),
  prevAudio: () => ipcRenderer.invoke("prev-audio"),
  seek: (currentTime) => ipcRenderer.send("seek-audio", currentTime),
  setVolume: (volume) => ipcRenderer.send("set-volume", volume),
  getState: () => ipcRenderer.invoke("get-audio-state"),
  setPlayList: (playList) => ipcRenderer.send("set-playlist", playList),
  setCurrentMusicId: (musicId) => ipcRenderer.send("set-current-music-id", musicId),
  onStateUpdate: (callback) => ipcRenderer.on("audio-state", callback),
  removeStateUpdate: (callback) => ipcRenderer.removeListener("audio-state", callback),
  getLocalSongs: () => ipcRenderer.invoke("get-local-songs"),
  downloadSong: (options) => ipcRenderer.send("download-song", options),
  onDownloadProgress: (callback) => {
    const handler = (event, progress) => callback(progress);
    ipcRenderer.on("download-progress", handler);
    return () => ipcRenderer.removeListener("download-progress", handler);
  },
  onDownloadDone: (callback) => {
    const handler = (event, info) => callback(info);
    ipcRenderer.on("download-done", handler);
    return () => ipcRenderer.removeListener("download-done", handler);
  },
  getDownloadDir: () => ipcRenderer.invoke("get-download-dir"),
  selectDownloadDir: () => ipcRenderer.invoke("select-download-dir"),
  // 收藏相关API
  addFavorite: (songInfo) => ipcRenderer.invoke("add-favorite", songInfo),
  removeFavorite: (musicrid) => ipcRenderer.invoke("remove-favorite", musicrid),
  getFavorites: () => ipcRenderer.invoke("get-favorites"),
  checkFavorite: (musicrid) => ipcRenderer.invoke("check-favorite", musicrid),
  saveLyrics: (lrc) => ipcRenderer.invoke("save-lyrics", lrc),
  // 获取本地歌词（去重）
  getLocalLyrics: (song) => {
    const key = song?.hash || song?.MUSICRID || JSON.stringify(song);
    return dedupeInvoke(`lyric_${key}`, "get-local-lyrics", song);
  },
  // 手动添加歌曲
  selectAudioFiles: () => ipcRenderer.invoke("select-audio-files"),
  // 从 store 读取扫描目录
  getDefaultMusicDirs: () => ipcRenderer.invoke("get-default-music-dirs"),
  // 选择目录并写入 store，返回最新列表
  selectScanFolders: () => ipcRenderer.invoke("select-scan-folders"),
  // 删除目录并写回 store，返回最新列表
  removeScanDir: (p) => ipcRenderer.invoke("remove-scan-dir", p),
  // 从 store 读取目录并执行扫描
  scanAudioInFolders: () => ipcRenderer.invoke("scan-audio-in-folders"),
  // 通过哈希补全封面并更新数据库（去重）
  fetchAndUpdateCoverByHash: (hash) => dedupeInvoke(`cover_${hash}`, "fetch-and-update-cover-by-hash", { hash })
});
const coverListeners = /* @__PURE__ */ new Map();
const progressListeners = /* @__PURE__ */ new Map();
contextBridge.exposeInMainWorld("coverAPI", {
  onCoverUpdated: (cb) => {
    if (coverListeners.has(cb)) return;
    const handler = (event, payload) => cb(payload);
    coverListeners.set(cb, handler);
    ipcRenderer.on("cover-updated", handler);
  },
  removeCoverUpdated: (cb) => {
    const handler = coverListeners.get(cb);
    if (handler) {
      ipcRenderer.removeListener("cover-updated", handler);
      coverListeners.delete(cb);
    }
  },
  onAudioProgress: (cb) => {
    if (progressListeners.has(cb)) return;
    const handler = (event, payload) => cb(payload);
    progressListeners.set(cb, handler);
    ipcRenderer.on("audio-progress", handler);
  },
  removeAudioProgress: (cb) => {
    const handler = progressListeners.get(cb);
    if (handler) {
      ipcRenderer.removeListener("audio-progress", handler);
      progressListeners.delete(cb);
    }
  }
});
