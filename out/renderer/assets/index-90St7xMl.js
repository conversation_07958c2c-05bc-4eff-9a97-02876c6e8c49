import { u as useLocation, r as reactExports, R as React, M as MusicList, c as axios, L as LOCALHOST } from "./index-jq6SDkto.js";
/* empty css               */
/* empty css               */
import "./index-BcGbK0Cw.js";
function RecDetail() {
  const location = useLocation();
  const [data, setData] = reactExports.useState([]);
  const [currentMusicId, setCurrentMusicId] = reactExports.useState("");
  const getRecDetail = async () => {
    if (!location.state) return;
    try {
      const cachedData = localStorage.getItem(`recId-${location.state}`);
      if (cachedData) {
        try {
          const parsedData = JSON.parse(cachedData);
          setData(parsedData);
          return;
        } catch (parseError) {
          console.error("解析缓存数据失败:", parseError);
        }
      }
      const res = await axios.get(LOCALHOST + "/recProxy", {
        params: {
          id: location.state,
          page: 1
        }
      });
      console.log(res.data);
      if (res.data) {
        const list = res.data?.musicList.map((item) => {
          return {
            NAME: item.name,
            ARTIST: item.artist,
            ALBUM: item.album,
            cover: item.pic,
            pic: item.pic,
            DURATION: item.duration,
            MUSICRID: item.musicrid
          };
        });
        setData(list);
        localStorage.setItem(`recId-${location.state}`, JSON.stringify(list));
      }
    } catch (error) {
      console.error("获取推荐详情失败:", error);
    }
  };
  reactExports.useEffect(() => {
    getRecDetail();
  }, []);
  return /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement(
    MusicList,
    {
      data,
      currentMusicId,
      onPlay: (item) => {
      },
      isShowToolbar: false
    }
  ));
}
export {
  RecDetail as default
};
