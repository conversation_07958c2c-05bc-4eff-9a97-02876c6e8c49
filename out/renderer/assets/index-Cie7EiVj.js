import { r as reactExports, R as React, T as Tabs, M as MusicList } from "./index-jq6SDkto.js";
const items = [
  {
    key: "1",
    label: "单曲"
  },
  {
    key: "2",
    label: "歌单"
  },
  {
    key: "3",
    label: "专辑"
  }
];
const onChange = (key) => {
};
function Favorite() {
  const [favoriteData, setFavoriteData] = reactExports.useState([]);
  const [hasMore, setHasMore] = reactExports.useState(false);
  const [loading, setLoading] = reactExports.useState(false);
  const handlePlay = (item) => {
    console.log("播放:", item);
  };
  return /* @__PURE__ */ React.createElement("div", null, /* @__PURE__ */ React.createElement(Tabs, { defaultActiveKey: "1", items, type: "card", onChange }), /* @__PURE__ */ React.createElement(
    MusicList,
    {
      data: favoriteData,
      hasMore,
      loading,
      onPlay: handlePlay,
      isShowToolbar: false
    }
  ));
}
export {
  Favorite as default
};
