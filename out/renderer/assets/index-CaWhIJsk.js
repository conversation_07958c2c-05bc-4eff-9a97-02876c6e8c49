import { a as useNavigate, r as reactExports, c as axios, L as LOCALHOST, s as staticMethods, R as React, F as Flex, S as Spin, d as RefIcon, b as Row, C as Col, O as Outlet } from "./index-jq6SDkto.js";
/* empty css               */
import { C as Card } from "./index-ClSkETsv.js";
const reb = "" + new URL("rgb-DTB6mgPJ.jpg", import.meta.url).href;
const xgb = "" + new URL("xgb-nNdz0dNe.jpg", import.meta.url).href;
const bsb = "" + new URL("bsb-DAC1S7ME.jpg", import.meta.url).href;
const om = "data:image/jpeg;base64,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";
const rhb = "data:image/jpeg;base64,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";
const initialData = [
  { img: reb, title: "热歌榜", content: [] },
  { img: xgb, title: "新歌榜", content: [] },
  { img: bsb, title: "飙升榜", content: [] },
  { img: om, title: "欧美榜", content: [] },
  { img: rhb, title: "日韩版", content: [] }
];
function canRequestToday(key = "lastRequestDate") {
  const today = (/* @__PURE__ */ new Date()).toISOString().slice(0, 10);
  const lastDate = localStorage.getItem(key);
  if (lastDate === today) {
    return false;
  }
  localStorage.setItem(key, today);
  return true;
}
function Bang() {
  const navigate = useNavigate();
  const [data, setData] = reactExports.useState(initialData);
  const [loading, setLoading] = reactExports.useState(false);
  const bangClick = ({ id }) => {
    navigate("/home/<USER>/" + id);
  };
  const liClick = (li) => {
    console.log(li);
  };
  reactExports.useEffect(() => {
    if (canRequestToday("lastRequestDate")) {
      setLoading(true);
      axios.get(LOCALHOST + "/proxy", { params: { type: "bang" } }).then((res) => {
        if (res.status === 200) {
          const doc = new DOMParser().parseFromString(res.data, "text/html");
          const scripts = doc.querySelectorAll("script");
          let newData = [...initialData];
          for (let script of scripts) {
            if (!script.src) {
              const result = eval(script.innerHTML);
              const bang = result?.data[0]?.bang;
              bang.forEach((bangItem, idx) => {
                newData[idx].id = bangItem.id;
                newData[idx].content = bangItem.musicList.slice(0, 6).map((m) => ({
                  artist: m.artist,
                  song_name: m.name,
                  rid: m.rid,
                  pic: m.pic,
                  pic120: m.pic120
                }));
              });
            }
          }
          localStorage.setItem("arrData", JSON.stringify(newData));
          localStorage.setItem("lastRequestDate", (/* @__PURE__ */ new Date()).toISOString().slice(0, 10));
          setData(newData);
        } else {
          staticMethods.open({
            type: "error",
            content: "寄了"
          });
        }
      }).finally(() => {
        setLoading(false);
      });
    } else {
      const localArr = localStorage.getItem("arrData");
      if (localArr) {
        setData(JSON.parse(localArr));
      }
    }
  }, []);
  return /* @__PURE__ */ React.createElement("div", null, loading ? /* @__PURE__ */ React.createElement(Flex, { align: "center", gap: "middle" }, /* @__PURE__ */ React.createElement(
    Spin,
    {
      indicator: /* @__PURE__ */ React.createElement(RefIcon, { spin: true }),
      size: "large",
      style: { position: "absolute", top: "50%", left: "50%" }
    }
  )) : /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement(Row, { gutter: 16, style: { margin: 0 } }, data.map((item, index) => /* @__PURE__ */ React.createElement(Col, { span: 24 / data.length, key: index, style: { minWidth: 0, flex: "1 1 0%" } }, /* @__PURE__ */ React.createElement(
    Card,
    {
      hoverable: true,
      styles: {
        body: {
          padding: 0
        }
      },
      cover: /* @__PURE__ */ React.createElement("div", { className: "cover-container" }, /* @__PURE__ */ React.createElement(
        "img",
        {
          onClick: () => bangClick(item),
          alt: item.title,
          src: item.img,
          style: { width: "100%" }
        }
      ), /* @__PURE__ */ React.createElement("div", { className: "play-icon" }, /* @__PURE__ */ React.createElement("svg", { width: "40", height: "40", viewBox: "0 0 40 40", fill: "none" }, /* @__PURE__ */ React.createElement("circle", { cx: "20", cy: "20", r: "20", fill: "rgba(0,0,0,0.4)" }), /* @__PURE__ */ React.createElement("polygon", { points: "16,13 28,20 16,27", fill: "#fff" }))))
    },
    /* @__PURE__ */ React.createElement("ul", null, item.content.map((i, index2) => /* @__PURE__ */ React.createElement("li", { key: index2, onClick: () => liClick(i) }, /* @__PURE__ */ React.createElement("div", { className: `${index2 > 2 && "index"} top-img-${index2 + 1}` }, index2 > 2 && index2 + 1), /* @__PURE__ */ React.createElement("div", { className: "info" }, /* @__PURE__ */ React.createElement("p", { className: "song-name hover-effect", title: i.song_name }, i.song_name), /* @__PURE__ */ React.createElement("p", { className: "artist", title: i.artist }, i.artist)))))
  )))), /* @__PURE__ */ React.createElement(Outlet, null)));
}
export {
  Bang as default
};
