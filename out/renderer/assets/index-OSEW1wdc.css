body, #root {
    padding: 0;
    margin: 0;
    user-select: none;
    overflow: hidden;
}
main {
  padding: 0!important;
}
.download-icon-animation {
  animation: download-bounce 0.8s ease-in-out infinite;
}
@keyframes download-bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}
.music-player {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 85px;
  background-color: #181818;
  z-index: 1001;
  color: #fff;
}
.music-player .player-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 6px;
  color: white;
  width: 100%;
}
.music-player .player-main .song-info {
  display: flex;
  min-width: 0;
  max-width: 280px;
  flex-shrink: 0;
  width: 280px;
  /* 固定宽度，防止影响后续布局 */
}
.music-player .player-main .song-info .song-avatar {
  margin-top: -6px;
  margin-left: 16px;
}
.music-player .player-main .song-info .song-details {
  display: flex;
  flex-direction: column;
  padding-left: 5px;
  min-width: 0;
  max-width: 180px;
}
.music-player .player-main .song-info .song-details .song-title {
  font-size: 16px;
  margin-bottom: 4px;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
  /* 限制最大宽度，防止样式垮掉 */
}
.music-player .player-main .song-info .song-details .song-artist {
  text-align: left;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}
.music-player .player-main .player-controls {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding-right: 10%;
}
.music-player .player-main .player-controls span {
  padding: 6px;
}
.music-player .player-main .player-controls .music-time {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.4);
  display: flex;
  align-items: center;
  margin-right: 18px;
  text-align: left;
  min-width: 70px;
  max-width: 82px;
  flex-shrink: 0;
}
.music-player .player-main .player-controls .music-actions {
  display: inline-flex;
  align-items: center;
  gap: 20px;
}
.music-player .player-main .music-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 25px;
  margin-right: 20px;
  cursor: pointer;
  vertical-align: middle;
}
.music-player .player-main .prev-icon,
.music-player .player-main .next-icon,
.music-player .player-main .play-icon,
.music-player .player-main .pause-icon {
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  width: 12px;
  height: 12px;
  display: inline-block;
  vertical-align: middle;
}
.music-player .player-main .prev-icon {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEoAAABKCAMAAAArDjJDAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAI1QTFRF////////////AAAA/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////iT+egAAAC90Uk5TcYB1AC3i/+pC3BinAmzwMs0Ok/1Y5yO9B3/47cUBYuwTnf461QR29B2zTAqJ+yor1XgFAAABiklEQVR4nM3YyUoDQRSF4XsWjQgiSIyLgCCCRPD9H0ZEBBEEEQcCEkgCEjump6quc4dFapPuLH568UFVXQiktzD4h1pANUytDaEKy5jUEVayikgdA0sJSZ3gZ/vjT50CCwlJneF79+RMTfC5f3alpsB78+ZJVdi0X+2pLaXOsqZqShGpHSV/ak/JnWooOVNtSq5Ul5In1aNkTw0oWVMJSsZUipIplaZkSWUo6VNZStrUCCVlaoySKjVOSZEqUeJTRUpsiqBEphhKVIqjRKRYSuUUWErF1Ln+i3Ip4zrM1OQgvyqVmuItKoUL4DUotZZL4CUoJXKF56iUXANPQSmRGzxGpUTmDwophW3iFrgPSoncfbDIyvvgjEXGbPQkMu74QSEjTzIMMvqoVkamOECWkGmOtQVkusP2KDLluX0MmfpikkdmuC7lkFluXhlktqtlEpn1wptAZr6GD5E5hgN9ZJ45Qw+Zb5DSQeYd77SQuSdFDbKAUdg/spABXY0sJFUji0n9IYtKyexr8QvqWs+vmADLpAAAAABJRU5ErkJggg==');
  width: 2px;
  height: 2px;
}
.music-player .player-main .next-icon {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABKCAMAAADEzFl9AAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAJxQTFRF////////AAAA////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////SUdtYQAAADR0Uk5THBgAVIASQPBsAqj/Jc0y/ZMO51i9I/h/B9xCp+4/4sL7iQrFKuxiAf6dE9U69HYEsx1MPC5Rya0AAAF1SURBVHiczdTNSgMxFMXxe4qfCKJUQYSCCD6B7/8OiiCCIK6kiFYEN4ILW2trZiYzObn3LLybSRbzg8A/gQHmmMxfwBaAz3prJ2tt2ggfMss2gHeVZbaNN5lltotXmWV7wLPKMtvHVGaZHTyxsZUt+2JjIyxjY6MsMjbO4mJjLSY23irHVmEVY6uyCrFVWoOx1VpDsdVb/bE5rN7YXFZPbE4rG5vXysXmt7qxRax2bCGrFVvQasQWtdLY4tZfbAprFZvE+o1NZP3EJrPmsY1kltlh9/VwW5nRWpOZzvqvZ/RZY7yIrGNgKurrBA+iO3QK3Gvu4wS4W3wF1hlul4uwdQ7cmMa6wPV6HbPGR1fJLmLNk3pM9wFrkVRj3NYyKYW1SkpgrZMKW0lSUStNKmY1k4pY7aQCVicpt5VJymllk/JZ+aQ8Vl9SDqs3qWprIKlKazCpOms4qRqrlBRvlZOiLSIp0qKS4iwuKcZikypblzM2qaL1DagDTLxWlr9IAAAAAElFTkSuQmCC');
  width: 3px;
  height: 3px;
}
.music-player .player-main .play-icon {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEcAAABYCAMAAACQ7hpjAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAKJQTFRF////AAAA////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////VcmbygAAADZ0Uk5TNQCwxSr/+4kK4kyzHfR1BNUy/ZMO5li9I/h/B9xCpxjwawLNOv6dE+xhAa+jgOdZbOFPimJ2lEv12wAAAdlJREFUeJyt2M1KA0EQBOAqFYmoEGRBJBCEHDSo7/8YgiBKQASRgIhBAol4UNxEdLM/M9Pd03sJ5PCxLDVTM00QHg+3yC8XZ5vkp4cD7PDDxQF2uXBx0CPnHg6wz3cXB4fkm4cD9Pnq4gBHfHFxUJBTDwc45rOLA5zwycXBgHz0cIDhTBmmgKMOU8jRhinslGFayBdvzNEs3qhTLt7iwcXBKTnxcIAReefhAN+CJpA4EDSByBE0gdBJNoHYSYRJ7sTDpHFiYVI5qzDduzjBJlA7gSbQO91NYHG6msDmtJvA6LSawOw0msDu1Jsgx9kMU55ThSnT+Q9TtlOGaW/i4qybwMNZNQEvRPWUes5c3qf81g5OUcymDt/5N4u5zpg36988p1qrWc7G3pHhDPrzai+zO/W91eo093qbs45MvtPRhQbnktftP9UOr267zmZaJ3RW1Dmjg2Xg7KpxYmdphRM924ud3vkkdteQOqnBhcxJD1IkTiAyWkdyvUg74choHOn1K+GIr4NRJxEZqaOadQWd45lq9hZw2hunydGPk7qcv67Nc2zjtpajnth0OrWutTuWiVbbsU3Ymo46Mt2OeQJZcyyRaTtZE9rKMUam4Qytkak746U1MrXnB2fDb9/V6pFeAAAAAElFTkSuQmCC');
}
.music-player .player-main .pause-icon {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAABKBAMAAAAYt1raAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAACFQTFRF////////////AAAA////////////////////////////ddWa+AAAAAt0Uk5TGIBPAC45MP+dXHOLGfUIAAAANUlEQVR4nGNkFGCAAEZFKIOB7wKUwcKYDpfdDZNNeQBlMI/KjsqOyo7KjsqOyo7KjsrSWhYAqSu2KnWmDw0AAAAASUVORK5CYII=');
}
.music-player .player-extra {
  display: flex;
  align-items: center;
  min-width: 0;
  margin-right: 16px;
  flex-shrink: 0;
}
.music-player .player-extra .loop-icon,
.music-player .player-extra .volume-icon,
.music-player .player-extra .menu-icon {
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  display: inline-block;
  vertical-align: middle;
  width: 22px;
  height: 22px;
}
.music-player .player-extra .loop-icon {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHcAAABOCAMAAAAtknndAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAJZQTFRFAAAA////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////wCXFqAAAADJ0Uk5TAE/Almv/yGm+QDKpA5FoWA3q4QeIeCr48h64qEg31WAVmPxQRaBwgJCqv8OhEbo/MWILL3i4AAACXElEQVR4nO2Xa0/CMBSGVzSiZgoBQUFUCF4/aPT//woTEy9RwRsqCvGCl2hgzo6tp1sH62jLl75fOJyGPCvre84pMogQMmQKdeEXEE9K5v5oruZqruYK4iZ7Xjgh4RHQZwgX7HfqQzyXfgjNlcA10ftYuPNfs69j4KZRZw611HMX7M2mn5Rz86htGFl0r5pbxFvN3ynmlh6dz6UbpVwz0/+Do/QFgdz5LzcqXinkll87bmhaL+q4lVsSp56VcfMmOE2ZhOcl2dwiVS6IlyRzq9f097VzJdys5etDhXZHBTf34s/MvCnglr9b/pTrJancjXow1/eSTO7KAyOZsZqYSxmMJfeN8GvKYmVzDcylCgpD3gnkVuqTnV+8xVzGy4fyHMergIdc4Z3Yr3SzNuDXdLfm0dZl2ErlDHO9BskQqKicKvTaYUvZ6To+z+5AwBDoIJxaboavlWqOj+jiDQQ7Jp+YHvIWO03MDfUSnBC4ZP4wPeQq13BKxfYFc5maiLhEhhu2Vh2uPdCzVpeGlJRw7XilD52R7IYXJdCA54vvIaDdExInye76XJaXfBO+FK6xHjxB6bBTLpIb9Jv/BieJG6gv/hurJC6+LALF9xAnN52A/WPlN7aHOLl0vywNalJiuXA++G8ZyriglovxUEQu8VLhQ4iHonILVh+3OHjmEs1153tn5FPIdWYxuy0r5jpeqp4Y4hSNa+wdjzDcjMC1R574F4QRuEalG/uCMBK3nDoUiY3MFS3NZXD3h4y9MdQC48QvCWnuwZFwLlTofjVXczVXc3m5Euok1CkJ/wDl1vCe14SILQAAAABJRU5ErkJggg==');
  width: 25px;
  height: 25px;
}
.music-player .player-extra .volume-icon {
  background-image: url('data:image/png;base64,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');
}
.music-player .player-extra .menu-icon {
  background-image: url('data:image/png;base64,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');
}
.song-details {
  min-width: 0;
  max-width: 140px;
}
.lyrics-modal {
  width: 100vw;
  height: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  box-sizing: border-box;
  position: relative;
  background: #111;
  /* 两边统一为黑色 */
  transition: background 0.4s;
  overflow: hidden;
}
.lyrics-left {
  flex: 0.8;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: #111;
  /* 左侧背景也为黑色 */
  backdrop-filter: blur(10px);
  min-width: 220px;
}
.album-cover {
  margin-bottom: 30px;
}
.album-cover .ant-avatar {
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}
.album-cover .ant-avatar:hover {
  transform: scale(1.05);
}
.song-info-modal {
  text-align: center;
  color: white;
}
.song-info-modal .song-title-modal {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}
.song-info-modal .song-artist-modal {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}
.lyrics-right {
  flex: 1.2;
  display: flex;
  flex-direction: column;
  padding: 40px 0 40px 0;
  background: #111;
  /* 右侧背景也为黑色 */
  position: relative;
}
.lyrics-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 0;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}
.lyrics-container::-webkit-scrollbar {
  width: 6px;
  height: 0 !important;
  /* 禁用底部横向滚动条 */
}
.lyrics-container::-webkit-scrollbar-track {
  background: transparent;
}
.lyrics-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}
.lyrics-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
.lyrics-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  text-align: center;
}
.lyrics-loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.lyric-line {
  padding: 12px 20px;
  margin: 8px 0;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  transition: all 0.3s ease, color 0.2s;
  border-radius: 8px;
  cursor: pointer;
}
.lyric-line:hover {
  color: #fff;
}
.lyric-line.active {
  color: #fff;
  font-weight: bold;
  font-size: 22px;
  background: none !important;
  text-shadow: none;
  letter-spacing: 0;
  border-radius: 0;
  box-shadow: none;
  filter: none;
  transition: all 0.3s ease, color 0.2s;
}
.lyrics-close-btn {
  position: absolute;
  top: 24px;
  right: 32px;
  width: 35px;
  height: 35px;
  background: rgba(0, 0, 0, 0.18);
  border-radius: 50%;
  color: #fff;
  font-size: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}
.lyrics-close-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  color: var(--ant-primary-color, #fff);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18);
}
.lyrics-fade-enter {
  opacity: 0;
  transform: scale(0.98);
  animation: lyricsFadeIn 0.4s forwards;
}
.lyrics-fade-exit {
  opacity: 1;
  transform: scale(1);
  animation: lyricsFadeOut 0.3s forwards;
}
@keyframes lyricsFadeIn {
  to {
    opacity: 1;
    transform: scale(1);
  }
}
@keyframes lyricsFadeOut {
  to {
    opacity: 0;
    transform: scale(1.02);
  }
}
@media (max-width: 768px) {
  .lyrics-modal {
    flex-direction: column;
  }
  .lyrics-modal .lyrics-left {
    padding: 20px;
  }
  .lyrics-modal .lyrics-left .album-cover .ant-avatar {
    width: 160px !important;
    height: 160px !important;
  }
  .lyrics-modal .lyrics-left .song-info-modal .song-title-modal {
    font-size: 18px;
    max-width: 180px;
  }
  .lyrics-modal .lyrics-left .song-info-modal .song-artist-modal {
    font-size: 13px;
    max-width: 180px;
  }
  .lyrics-modal .lyrics-right {
    padding: 10px 0 10px 0;
  }
  .lyrics-modal .lyrics-right .lyrics-container .lyric-line {
    font-size: 13px;
    padding: 8px 10px;
  }
  .lyrics-modal .lyrics-right .lyrics-container .lyric-line.active {
    font-size: 15px;
  }
}
.ant-drawer {
  outline: none !important;
}
.playlist-drawer {
  opacity: 0.85;
}
.playlist-drawer .playlist-container {
  scrollbar-width: thin;
  scrollbar-color: var(--player-scrollbar-thumb) var(--player-scrollbar-track) !important;
  transform: translateZ(0);
  will-change: scroll-position;
  scroll-behavior: auto;
  -webkit-overflow-scrolling: touch;
}
.playlist-drawer .playlist-container::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}
.playlist-drawer .playlist-container::-webkit-scrollbar-track {
  background: var(--player-scrollbar-track) !important;
  border-radius: 6px !important;
}
.playlist-drawer .playlist-container::-webkit-scrollbar-thumb {
  background: var(--player-scrollbar-thumb) !important;
  border-radius: 6px !important;
  border: 2px solid var(--player-scrollbar-track) !important;
}
.playlist-drawer .playlist-container::-webkit-scrollbar-thumb:hover {
  background: var(--player-scrollbar-thumb-hover) !important;
}
.playlist-drawer .playlist-container::-webkit-scrollbar-corner {
  background: var(--player-scrollbar-corner) !important;
}
.playlist-drawer .playlist-container::-webkit-scrollbar-button {
  display: none !important;
}
.playlist-drawer .playlist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.playlist-drawer .playlist-header .playlist-title {
  font-weight: 500;
}
.playlist-drawer .playlist-header .playlist-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
.playlist-drawer .playlist-header .playlist-actions .clear-playlist-btn:hover {
  color: var(--player-clear-btn) !important;
}
.playlist-drawer .playlist-header-total {
  font-size: 11px;
  padding-top: 10px;
}
.playlist-drawer .playlist-item {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  cursor: pointer;
  contain: layout style paint;
  transition: background-color 0.2s ease;
}
.playlist-drawer .playlist-item:hover {
  background-color: var(--player-scrollbar-thumb) !important;
}
.playlist-drawer .playlist-item.playing {
  background-color: var(--player-playing) !important;
}
.playlist-drawer .playlist-empty {
  text-align: center;
  padding: 40px 20px;
}
.header {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  text-align: center;
}
.header .logo {
  min-width: 200px;
}
.header .top-right {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.header .top-right .search {
  color: white;
}
.header .top-right .setting {
  padding-right: 10px;
  font-size: 18px;
  color: white;
}
.play-all-button,
.download-all-button,
.batch-button {
  gap: 2px;
  padding: 0 8px;
  margin-right: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}
.list-checked {
  min-width: 71px;
  display: inline-block;
}
.music-scrollableDiv {
  background-color: var(--playlist-bg, var(--search-bg, #fff));
}
.music-scrollableDiv .list-item {
  transition: background 0.3s;
  display: flex;
  justify-content: flex-end;
  background: var(--playlist-bg, var(--search-bg, #fff));
}
.music-scrollableDiv .list-item.selected {
  background: var(--playlist-selected-bg, var(--search-selected-bg, #e6f7ff)) !important;
}
.music-scrollableDiv .list-item .duration,
.music-scrollableDiv .list-item .album {
  flex: 1;
  color: var(--playlist-text-desc, var(--search-meta-color, #000));
}
.music-scrollableDiv .list-item .list-item-meta {
  flex: 2;
  margin-top: 0;
  padding-left: 5px;
  color: var(--playlist-text-desc, var(--search-meta-color, #000));
}
.music-scrollableDiv .list-item .list-item-meta .list-avatar {
  margin-inline-end: 0;
}
.music-scrollableDiv .list-item .list-item-meta span {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}
.music-scrollableDiv .list-item .action {
  flex: 0.5;
}
.music-scrollableDiv .list-item .album,
.music-scrollableDiv .list-item .duration {
  text-align: center;
}
.music-scrollableDiv .list-item .action {
  text-align: right;
}
.music-scrollableDiv .list-item .action .more-icon,
.music-scrollableDiv .list-item .action .heart-icon {
  padding-left: 10px;
  font-size: 18px;
  cursor: pointer;
  color: var(--playlist-text, #999);
  transition: color 0.3s;
}
.music-scrollableDiv .list-item .action .more-icon:hover,
.music-scrollableDiv .list-item .action .heart-icon:hover {
  color: var(--playlist-text, #333);
}
.music-scrollableDiv .list-item:hover {
  background: rgba(140, 140, 140, 0.1);
}
.music-scrollableDiv .list-idx {
  font-size: 15px;
  color: var(--playlist-text-desc, var(--search-idx-color, rgba(0, 0, 0, 0.39)));
}
.playlist-toolbar {
  position: sticky;
  top: 0;
  z-index: 2;
  background: var(--playlist-bg, #fff);
  padding-top: 8px;
  padding-bottom: 8px;
}
/* 滚动容器样式（假设容器类名为 .music-scrollableDiv） */
.music-scrollableDiv {
  /* ========== 基础设置 ========== */
  overflow: auto;
  /* 确保滚动条出现 */
  /* ========== WebKit 内核浏览器（Chrome/Edge/Safari） ========== */
  /* 滚动条轨道（背景） */
  /* 滚动条滑块（拖动部分） */
}
.music-scrollableDiv::-webkit-scrollbar {
  width: 8px;
  /* 纵向滚动条宽度 */
  height: 8px;
  /* 横向滚动条高度（如需支持横向滚动） */
}
.music-scrollableDiv::-webkit-scrollbar-track {
  background-color: var(--search-scrollbar-track, #f5f5f5);
  border-radius: 4px;
}
.music-scrollableDiv::-webkit-scrollbar-thumb {
  background-color: var(--search-scrollbar-thumb, #ccc);
  border-radius: 4px;
}
.music-scrollableDiv::-webkit-scrollbar-thumb:hover {
  background-color: var(--search-scrollbar-thumb-hover, #999);
  /* 鼠标悬浮效果 */
}
.playlist-toolbar {
  position: sticky;
  top: 0;
  z-index: 2;
  background: var(--playlist-bg, #141414);
  padding-left: 14px;
}
.playlist-toolbar .control-bar {
  display: flex;
  float: right;
  margin-right: 14px;
  align-items: center;
  background-color: var(--control-bar-bg, #2a2a2a);
  padding: 0 2px;
  gap: 8px;
  width: fit-content;
}
.playlist-toolbar .control-bar .search-container {
  position: relative;
  display: flex;
  align-items: center;
}
.playlist-toolbar .control-bar .search-container .search-box {
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-right: 8px;
}
.playlist-toolbar .control-bar .search-container .search-box.show {
  width: 200px;
}
.playlist-toolbar .control-bar .search-container .search-box .search-input {
  width: 100%;
  height: 32px;
  padding: 0 12px;
  border: 1px solid var(--control-border-color, #555);
  border-radius: 4px;
  background-color: var(--control-input-bg, #1a1a1a);
  color: var(--control-text-color, #ffffff);
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
}
.playlist-toolbar .control-bar .search-container .search-box .search-input::placeholder {
  color: var(--control-placeholder-color, #888);
}
.playlist-toolbar .control-bar .search-container .search-box .search-input:focus {
  border-color: var(--control-focus-color, #1890ff);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.playlist-toolbar .control-bar .control-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}
.playlist-toolbar .control-bar .control-item:hover {
  color: var(--control-item-hover-bg, rgba(255, 255, 255, 0.1));
}
.playlist-toolbar .control-bar .control-item.active {
  background-color: rgba(255, 165, 0, 0.2);
}
.playlist-toolbar .control-bar .control-item .control-icon {
  font-size: 18px;
  color: var(--control-icon-color, #ffffff);
  transition: all 0.3s ease;
}
.playlist-toolbar .control-bar .control-item:hover .control-icon {
  color: var(--control-icon-hover-color, #1890ff) !important;
}
.playlist-toolbar .control-bar .control-item .sort-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.playlist-toolbar .control-bar .control-item .list-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.music-scrollableDiv {
  background-color: var(--playlist-bg, #141414);
  min-height: 100vh;
  height: 100%;
  margin: 0;
  padding: 0 2px 190px 2px;
}
.music-scrollableDiv .list-item {
  transition: background 0.3s;
  display: flex;
  justify-content: flex-end;
  background: var(--playlist-bg, #141414);
}
.music-scrollableDiv .list-item.selected {
  background: var(--playlist-selected-bg, #141414) !important;
}
.music-scrollableDiv .list-item .duration,
.music-scrollableDiv .list-item .album {
  flex: 1;
  color: var(--playlist-text-desc, #000);
}
.music-scrollableDiv .list-item .list-item-meta {
  flex: 2;
  margin-top: 0;
  padding-left: 5px;
  color: var(--playlist-text-desc, #000);
}
.music-scrollableDiv .list-item .list-item-meta .list-avatar {
  margin-inline-end: 0;
}
.music-scrollableDiv .list-item .list-item-meta span {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}
.music-scrollableDiv .list-item .action {
  flex: 0.5;
  text-align: right;
}
.music-scrollableDiv .list-item .action .more-icon,
.music-scrollableDiv .list-item .action .heart-icon {
  padding-left: 10px;
  font-size: 18px;
  cursor: pointer;
  color: var(--playlist-text, #999);
  transition: color 0.3s;
}
.music-scrollableDiv .list-item .action .more-icon:hover,
.music-scrollableDiv .list-item .action .heart-icon:hover {
  color: var(--playlist-text, #333);
}
.music-scrollableDiv .list-item .album,
.music-scrollableDiv .list-item .duration {
  text-align: center;
}
.music-scrollableDiv .list-item:hover {
  background: rgba(140, 140, 140, 0.1);
}
.music-scrollableDiv .list-idx {
  font-size: 15px;
  color: var(--playlist-text-desc, rgba(0, 0, 0, 0.39));
}
body {
  background: var(--playlist-bg, #181818);
  margin: 0;
  padding: 0;
}
.album {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
  text-align: center;
}
.music-scrollableDiv::-webkit-scrollbar {
  width: 8px;
}
.music-scrollableDiv::-webkit-scrollbar-track {
  background: var(--playlist-scrollbar-track, transparent);
}
.music-scrollableDiv::-webkit-scrollbar-thumb {
  background-color: var(--playlist-scrollbar-thumb, #ccc);
  border-radius: 4px;
}
.music-scrollableDiv::-webkit-scrollbar-thumb:hover {
  background-color: var(--playlist-scrollbar-thumb-hover, #999);
}
.music-scrollableDiv::-webkit-scrollbar-button {
  display: none;
}
.download-scrollableDiv::-webkit-scrollbar {
  width: 8px;
}
.download-scrollableDiv::-webkit-scrollbar-track {
  background: var(--download-scrollbar-track, transparent);
}
.download-scrollableDiv::-webkit-scrollbar-thumb {
  background-color: var(--download-scrollbar-thumb, #ccc);
  border-radius: 4px;
}
.download-scrollableDiv::-webkit-scrollbar-thumb:hover {
  background-color: var(--download-scrollbar-thumb-hover, #999);
}
.download-scrollableDiv::-webkit-scrollbar-button {
  display: none;
}
.download-scrollableDiv .download-list-item:hover {
  background-color: rgba(0, 0, 0, 0.1) !important;
}
.sort-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.sort-menu {
  min-width: 120px;
  padding: 8px 0;
  background-color: var(--control-bar-bg, #2a2a2a) !important;
  border-radius: 6px !important;
  border: none !important;
}
.sort-section .sort-title {
  padding: 6px 12px;
  font-size: 14px;
  color: var(--control-text-color, #ffffff) !important;
  opacity: 0.7;
  border-bottom: 1px solid var(--control-border-color, #555);
  margin-bottom: 4px;
  user-select: none;
}
.sort-section .sort-options .sort-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  color: var(--control-text-color, #ffffff) !important;
  user-select: none;
}
.sort-section .sort-options .sort-option:hover {
  background-color: var(--control-item-hover-bg, rgba(255, 255, 255, 0.1)) !important;
}
.sort-section .sort-options .sort-option.active {
  color: var(--control-focus-color, #1890ff) !important;
  background-color: var(--control-item-hover-bg, rgba(255, 255, 255, 0.1)) !important;
}
.sort-section .sort-options .sort-option .sort-indicator {
  font-size: 12px;
  margin-left: 8px;
}
.control-icon.active {
  color: var(--control-focus-color, #1890ff) !important;
}
.sort-popover .ant-popover-inner {
  background-color: var(--control-bar-bg, #2a2a2a) !important;
  border: none !important;
  border-radius: 6px !important;
}
.sort-popover .ant-popover-inner-content {
  background-color: var(--control-bar-bg, #2a2a2a) !important;
  border-radius: 6px !important;
  color: var(--control-text-color, #ffffff) !important;
  padding: 0 !important;
}
.sort-popover .ant-popover-arrow {
  display: none !important;
}
.sort-popover.ant-popover .ant-popover-inner {
  background-color: var(--control-bar-bg, #2a2a2a) !important;
}
.sort-popover.ant-popover .ant-popover-inner-content {
  background-color: var(--control-bar-bg, #2a2a2a) !important;
}
body .sort-popover.ant-popover .ant-popover-inner {
  background-color: var(--control-bar-bg, #2a2a2a) !important;
  border: none !important;
  border-radius: 6px !important;
}
body .sort-popover.ant-popover .ant-popover-inner-content {
  background-color: var(--control-bar-bg, #2a2a2a) !important;
  border-radius: 6px !important;
  color: var(--control-text-color, #ffffff) !important;
  padding: 0 !important;
}
body .sort-popover.ant-popover .ant-popover-arrow {
  display: none !important;
}
body .ant-popover.sort-popover .ant-popover-inner {
  background-color: var(--control-bar-bg, #2a2a2a) !important;
  border: none !important;
}
body .ant-popover.sort-popover .ant-popover-inner-content {
  background-color: var(--control-bar-bg, #2a2a2a) !important;
  color: var(--control-text-color, #ffffff) !important;
}
:root {
  --control-bar-bg: #2a2a2a;
  --control-text-color: #ffffff;
  --control-border-color: #555;
  --control-item-hover-bg: rgba(255, 255, 255, 0.1);
  --control-focus-color: #1890ff;
}
[data-theme='dark'] {
  --control-bar-bg: #1f1f1f;
  --control-text-color: #ffffff;
  --control-border-color: #333;
  --control-item-hover-bg: rgba(255, 255, 255, 0.1);
  --control-focus-color: #1890ff;
}
[data-theme='light'] {
  --control-bar-bg: #f5f5f5;
  --control-text-color: #000000;
  --control-border-color: #d9d9d9;
  --control-item-hover-bg: rgba(0, 0, 0, 0.1);
  --control-focus-color: #1890ff;
}
/* 扫描目录列表滚动条（主题适配） */
.scan-list-scroll {
  overflow: auto;
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-bg) var(--scrollbar-track-bg);
}
.scan-list-scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.scan-list-scroll::-webkit-scrollbar-track {
  background: var(--scrollbar-track-bg);
  border-radius: 4px;
}
.scan-list-scroll::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb-bg);
  border-radius: 4px;
}
.scan-list-scroll::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-thumb-hover-bg);
}
.scan-list-scroll::-webkit-scrollbar-button {
  display: none;
}
.scan-list-scroll .ant-list-item {
  border-radius: 6px;
  transition: background-color 0.15s ease;
}
.scan-list-scroll .ant-list-item:hover {
  background: var(--control-item-hover-bg, rgba(0, 0, 0, 0.06));
}
.download-page .download-list-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-bg, #d9d9d9) var(--scrollbar-track-bg, #f5f5f5);
}
.download-page .download-list-scrollbar::-webkit-scrollbar {
  width: 8px;
}
.download-page .download-list-scrollbar::-webkit-scrollbar-track {
  border-radius: 4px;
  background: var(--scrollbar-track-bg, #f5f5f5);
}
.download-page .download-list-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: var(--scrollbar-thumb-bg, #d9d9d9);
}
.download-page .download-list-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-bg, #bfbfbf);
}
.download-page .download-status-info {
  padding: 8px 12px;
  margin-bottom: 12px;
  border-radius: 6px;
  font-size: 14px;
}
.download-page .empty-state {
  text-align: center;
  padding: 40px 20px;
  font-size: 14px;
}
.download-page .download-item {
  margin-bottom: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  padding: 12px 16px;
}
.download-page .download-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.download-page .download-item .download-item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 12px;
  height: 32px;
}
.download-page .download-item .song-info {
  display: flex;
  align-items: center;
  min-width: 200px;
  max-width: 250px;
  flex: 0 0 auto;
}
.download-page .download-item .song-info .song-name {
  font-weight: 500;
  font-size: 14px;
  margin-right: 12px;
  line-height: 32px;
  max-width: 180px;
  min-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}
.download-page .download-item .song-info .status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}
.download-page .download-item .progress-container {
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 300px;
  height: 100%;
}
.download-page .download-item .progress-container .ant-progress {
  margin: 0;
  width: 100%;
}
.download-page .download-item .download-info {
  font-size: 12px;
  min-width: 100px;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
}
.download-page .download-item .file-size {
  font-size: 12px;
  min-width: 60px;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
}
.download-page .download-list-container {
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.download-page .list-content {
  flex: 1;
  overflow: hidden;
}
