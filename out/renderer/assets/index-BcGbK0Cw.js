import { p as theme, a as useNavigate, r as reactExports, R as React, I as InfiniteScroll, D as Divider, n as Skeleton, q as List, A as Avatar } from "./index-jq6SDkto.js";
const { useToken } = theme;
const INITIAL_LETTER = "热门";
const INITIAL_CATEGORY = "全部";
const ARTIST_CATEGORIES = ["华语男", "华语女", "华语组合", "日韩男", "日韩女", "日韩组合", "欧美男", "欧美女", "欧美组合", "其他"];
const ALPHABET = Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i));
const mockArtists = Array.from({ length: 100 }, (_, i) => ({
  id: `artist-${i}`,
  name: `歌手 ${i + 1}`,
  avatar: `https://i.pravatar.cc/150?u=artist-${i}`
}));
function Artist() {
  const { token } = useToken();
  const navigate = useNavigate();
  const [loading, setLoading] = reactExports.useState(false);
  const [data, setData] = reactExports.useState([]);
  const [page, setPage] = reactExports.useState(1);
  const [hasMore, setHasMore] = reactExports.useState(true);
  const [selectedLetter, setSelectedLetter] = reactExports.useState(INITIAL_LETTER);
  const [selectedCategory, setSelectedCategory] = reactExports.useState(INITIAL_CATEGORY);
  const limit = 20;
  const fetchData = reactExports.useCallback((pageToLoad) => {
    setLoading(true);
    setTimeout(() => {
      const startIndex = (pageToLoad - 1) * limit;
      const endIndex = startIndex + limit;
      const results = mockArtists.slice(startIndex, endIndex);
      setData((prevData) => pageToLoad === 1 ? results : [...prevData, ...results]);
      setHasMore(endIndex < mockArtists.length);
      setLoading(false);
      setPage(pageToLoad);
    }, 500);
  }, []);
  reactExports.useEffect(() => {
  }, [selectedLetter, selectedCategory, fetchData]);
  const loadMoreData = () => {
    if (loading || !hasMore) {
      return;
    }
    fetchData(page + 1);
  };
  const itemClick = (id) => {
    navigate(`/home/<USER>/${id}`);
  };
  return /* @__PURE__ */ React.createElement(
    "div",
    {
      className: "artist-container",
      style: {
        "--primary-color": token.colorPrimary,
        "--primary-color-hover": token.colorPrimaryHover,
        "--container-bg": token.colorBgContainer,
        "--text-color": token.colorText,
        "--text-color-secondary": token.colorTextSecondary,
        "--component-background": token.colorFillAlter,
        // 遵从您的指示，为滚动条适配主题色
        "--artist-scrollbar-track": token.colorBgContainer,
        "--artist-scrollbar-thumb": token.colorBorder,
        "--artist-scrollbar-thumb-hover": token.colorPrimary
      }
    },
    /* @__PURE__ */ React.createElement("div", { className: "artist-header" }, /* @__PURE__ */ React.createElement("div", { className: "artist-filter" }, /* @__PURE__ */ React.createElement("div", { className: "artist-filter-letters" }, /* @__PURE__ */ React.createElement(
      "span",
      {
        className: `tag${selectedLetter === "热门" ? " active" : ""}`,
        key: "热门",
        onClick: () => setSelectedLetter("热门")
      },
      "热门"
    ), /* @__PURE__ */ React.createElement("div", { className: "filter-tags-wrapper" }, ALPHABET.map((letter) => /* @__PURE__ */ React.createElement(
      "span",
      {
        className: `tag${selectedLetter === letter ? " active" : ""}`,
        key: letter,
        onClick: () => setSelectedLetter(letter)
      },
      letter
    )), /* @__PURE__ */ React.createElement(
      "span",
      {
        className: `tag${selectedLetter === "#" ? " active" : ""}`,
        key: "#",
        onClick: () => setSelectedLetter("#")
      },
      "#"
    ))), /* @__PURE__ */ React.createElement("div", { className: "artist-filter-category" }, /* @__PURE__ */ React.createElement(
      "span",
      {
        className: `tag${selectedCategory === "全部" ? " active" : ""}`,
        key: "全部",
        onClick: () => setSelectedCategory("全部")
      },
      "全部"
    ), /* @__PURE__ */ React.createElement("div", { className: "filter-tags-wrapper" }, ARTIST_CATEGORIES.map((cat) => /* @__PURE__ */ React.createElement(
      "span",
      {
        className: `tag${selectedCategory === cat ? " active" : ""}`,
        key: cat,
        onClick: () => setSelectedCategory(cat)
      },
      cat
    )))))),
    /* @__PURE__ */ React.createElement("div", { id: "artist-scrollableDiv", className: "artist-list-container" }, /* @__PURE__ */ React.createElement(
      InfiniteScroll,
      {
        dataLength: data.length,
        next: loadMoreData,
        hasMore,
        loader: data.length ? /* @__PURE__ */ React.createElement(Skeleton, { avatar: true, paragraph: { rows: 1 }, active: true }) : "",
        endMessage: /* @__PURE__ */ React.createElement(Divider, { plain: true }, "到底了..."),
        scrollableTarget: "artist-scrollableDiv"
      },
      /* @__PURE__ */ React.createElement(
        List,
        {
          grid: { gutter: 24, xs: 2, sm: 3, md: 4, lg: 5, xl: 6, xxl: 7 },
          dataSource: data,
          renderItem: (item) => /* @__PURE__ */ React.createElement(List.Item, { key: item.id, onClick: () => itemClick(item.id) }, /* @__PURE__ */ React.createElement("div", { className: "artist-list-item" }, /* @__PURE__ */ React.createElement(Avatar, { src: item.avatar, size: 150 }), /* @__PURE__ */ React.createElement("div", { className: "artist-name" }, item.name)))
        }
      )
    ))
  );
}
export {
  Artist as default
};
