import { a as useNavigate, r as reactExports, R as React, b as Row, C as Col } from "./index-jq6SDkto.js";
import { C as Card } from "./index-ClSkETsv.js";
const news = "" + new URL("new-BbE681ag.jpg", import.meta.url).href;
const old = "" + new URL("old-DytwuPLH.jpg", import.meta.url).href;
const douyin = "" + new URL("douyin-DzoplA-F.jpg", import.meta.url).href;
const bumianye = "" + new URL("bumianye-DOhbloqT.jpg", import.meta.url).href;
const laonanren = "" + new URL("laonanren-8IZoRJcp.jpg", import.meta.url).href;
const shangba = "" + new URL("shangba-ycB803EQ.jpg", import.meta.url).href;
const eng = "" + new URL("eng-BmY5PrK5.jpg", import.meta.url).href;
const guofeng = "" + new URL("guofeng-CkNZ7z1S.jpg", import.meta.url).href;
const minyao = "" + new URL("minyao-1F3O_j72.jpg", import.meta.url).href;
const qingyinyue = "" + new URL("qingyinyue-7i5zGUxO.jpg", import.meta.url).href;
const data = [
  {
    id: "1082685104",
    img: news,
    content: "每日最新单曲推荐"
  },
  {
    id: "2996314807",
    img: old,
    content: "经典老歌大放送！一人一首代表作！"
  },
  {
    id: "3539687644",
    img: douyin,
    content: "抖音爆款丨2025热门歌曲"
  },
  {
    id: "3001661381",
    img: bumianye,
    content: "伤感情歌｜不眠夜里的孤单心事"
  },
  {
    id: "2802745654",
    img: laonanren,
    content: "【经典国语】老男人的情怀老男人的歌"
  },
  {
    id: "2508547657",
    img: shangba,
    content: "【伤感】对于你，我总是好了伤疤忘了疼"
  },
  {
    id: "2539910244",
    img: eng,
    content: "【燃爆】抖音中俘虏你耳朵的英文歌曲"
  },
  {
    id: "3014900006",
    img: guofeng,
    content: "国风精选篇：赠君一场千年梦"
  },
  {
    id: "2964988930",
    img: minyao,
    content: "民谣男声丨屏蔽纷纷扰扰"
  },
  {
    id: "2907225045",
    img: qingyinyue,
    content: "解压良药: 舒缓惬意的轻音乐"
  }
];
function HoverCard({ item }) {
  const [hovered, setHovered] = reactExports.useState(false);
  return /* @__PURE__ */ React.createElement(
    Card,
    {
      title: item.title,
      variant: "borderless",
      hoverable: true,
      onMouseEnter: () => setHovered(true),
      onMouseLeave: () => setHovered(false),
      cover: /* @__PURE__ */ React.createElement("div", { className: "hover-card-wrapper" }, /* @__PURE__ */ React.createElement(
        "img",
        {
          alt: "img",
          src: item.img,
          style: {
            transform: hovered ? "scale(1.08)" : "scale(1)",
            opacity: hovered ? 0.85 : 1
          }
        }
      ), hovered && /* @__PURE__ */ React.createElement(
        "span",
        {
          style: {
            transform: `translate(-50%, -50%) scale(${hovered ? 1.1 : 1})`,
            opacity: hovered ? 1 : 0
          }
        },
        /* @__PURE__ */ React.createElement("svg", { width: "32", height: "32", viewBox: "0 0 1024 1024", fill: "currentColor" }, /* @__PURE__ */ React.createElement("path", { d: "M384 256l384 256-384 256z" }))
      )),
      styles: {
        body: {
          padding: 6,
          height: 60
        }
      }
    },
    item.content
  );
}
function RecPlayList() {
  const navigate = useNavigate();
  reactExports.useEffect(() => {
  }, []);
  return /* @__PURE__ */ React.createElement("div", { className: "top" }, /* @__PURE__ */ React.createElement(Row, { gutter: [16, 16], style: { margin: 0 } }, data.map((item, index) => /* @__PURE__ */ React.createElement(
    Col,
    {
      key: index,
      xs: 24,
      sm: 12,
      md: 4,
      lg: 4,
      xl: 4,
      style: { flex: "0 0 20%", maxWidth: "20%" },
      onClick: () => {
        navigate("/home/<USER>/" + item.id, { state: item.id });
      }
    },
    /* @__PURE__ */ React.createElement(HoverCard, { item })
  ))));
}
export {
  RecPlayList as default
};
