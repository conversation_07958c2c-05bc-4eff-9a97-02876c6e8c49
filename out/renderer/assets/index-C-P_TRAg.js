import { u as useLocation, a as useNavigate, r as reactExports, R as React, T as Tabs, O as Outlet } from "./index-jq6SDkto.js";
/* empty css               */
const items = [
  {
    key: "rec",
    label: "推荐"
  },
  {
    key: "bang",
    label: "排行榜"
  },
  {
    key: "artist",
    label: "歌手"
  }
];
function Home() {
  const location = useLocation();
  const navigate = useNavigate();
  const tabKey = (() => {
    const path = location.pathname.replace("/home/", "").split("/")[0];
    return ["rec", "bang", "artist"].includes(path) ? path : "rec";
  })();
  const onChange = (key) => {
    if (tabKey !== key) {
      navigate(`/home/<USER>
    }
  };
  reactExports.useEffect(() => {
    if (location.pathname === "/home" || location.pathname === "/home/") {
      const lastTab = sessionStorage.getItem("homeTab");
      navigate(`/home/<USER>"rec", "bang", "artist"].includes(lastTab) ? lastTab : "rec"}`, { replace: true });
    }
  }, [location.pathname, navigate]);
  reactExports.useEffect(() => {
    sessionStorage.setItem("homeTab", tabKey);
  }, [tabKey]);
  return /* @__PURE__ */ React.createElement("div", null, /* @__PURE__ */ React.createElement(Tabs, { type: "card", activeKey: tabKey, items, onChange }), /* @__PURE__ */ React.createElement(Outlet, null));
}
export {
  Home as default
};
