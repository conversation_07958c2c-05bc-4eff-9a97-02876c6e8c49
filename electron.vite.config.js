import { defineConfig } from 'electron-vite'
import { resolve } from 'path'

export default defineConfig({
  main: {
    build: {
      rollupOptions: {
        input: {
          index: resolve(__dirname, 'electron/main')
        }
      }
    }
  },
  preload: {
    build: {
      rollupOptions: {
        input: {
          index: resolve(__dirname, 'electron/preload')
        }
      }
    }
  },
  renderer: {
    root: '.',
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    // 添加这个配置确保正确输出
    build: {
      outDir: 'out/renderer',
      assetsDir: 'assets',
      rollupOptions: {
        input: {
          index: resolve(__dirname, 'index.html'),
          music: resolve(__dirname, 'music.html')
        }
      }
    }
  }
})