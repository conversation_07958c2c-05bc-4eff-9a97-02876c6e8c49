// 下载管理器
class DownloadManager {
  constructor() {
    this.downloads = new Map()
    this.listeners = new Set()
    this.statusListeners = new Set() // 新增：状态变化监听器
    this.isDownloading = false // 新增：全局下载状态
    this.maxConcurrentDownloads = 3 // 新增：最大并发下载数
    this.activeDownloads = 0 // 新增：当前活跃下载数
    this.downloadQueue = [] // 新增：下载队列
  }

  // 添加下载任务
  addDownload(key, songInfo) {
    const download = {
      key,
      name: songInfo.NAME,
      artist: songInfo.ARTIST,
      quality: songInfo.quality || '标准音质',
      progress: 0,
      status: 'queued', // 初始状态为排队中
      size: songInfo.size || '--',
      totalSize: songInfo.size,
      downloadedSize: '0B',
      songInfo: songInfo, // 保存完整的songInfo
      onPause: this.pauseDownload.bind(this, key),
      onResume: this.resumeDownload.bind(this, key),
      onCancel: this.cancelDownload.bind(this, key),
    }
    
    this.downloads.set(key, download)

    // 将任务添加到队列
    this.downloadQueue.push(key)
    
    // 尝试开始下载
    this.processQueue()
    
    // 更新全局下载状态
    this.updateDownloadStatus()
    
    this.notifyListeners()
    return download
  }

  // 处理下载队列
  processQueue() {
    // 如果当前活跃下载数小于最大并发数，且有排队中的任务
    while (this.activeDownloads < this.maxConcurrentDownloads && this.downloadQueue.length > 0) {
      const nextKey = this.downloadQueue.shift()
      const download = this.downloads.get(nextKey)
      
      if (download && download.status === 'queued') {
        // 开始下载
        download.status = 'downloading'
        this.activeDownloads++
        
        // 通知主进程开始下载
        if (window.audioAPI && window.audioAPI.downloadSong) {
          // 从下载信息中获取URL和filename
          const songInfo = download.songInfo
          if (songInfo && songInfo.url && songInfo.filename) {
            console.log('downloadManager: 开始下载',  songInfo.filename)
            window.audioAPI.downloadSong({
              url: songInfo.url,
              filename: songInfo.filename,
              key: nextKey,
              songInfo: songInfo
            })
          } else {
            // 如果没有URL信息，标记为错误
            this.updateStatus(nextKey, 'error')
          }
        }
      }
    }
    
    this.notifyListeners()
  }

  // 更新下载进度
  updateProgress(key, progress, downloadedSize) {
    const download = this.downloads.get(key)
    if (download) {
      download.progress = Math.min(progress, 100) // 确保进度不超过100%
      download.downloadedSize = downloadedSize
      this.notifyListeners()
    } else {
      console.log('downloadManager: 未找到下载任务', key)
    }
  }

  // 更新下载状态
  updateStatus(key, status) {
    const download = this.downloads.get(key)
    if (download) {
      const previousStatus = download.status
      download.status = status
      
      // 新增：如果下载完成或失败，减少活跃下载数并处理队列
      if (previousStatus === 'downloading' && (status === 'completed' || status === 'error')) {
        this.activeDownloads--
        this.processQueue() // 处理队列中的下一个任务
      }
      
      if (status === 'completed') {
        download.progress = 100
        // 下载完成后，延迟3秒自动清理任务
        setTimeout(() => {
          this.cancelDownload(key)
        }, 3000)
      }
      
      // 更新全局下载状态
      this.updateDownloadStatus()
      
      this.notifyListeners()
    }
  }

  // 暂停下载
  pauseDownload(key) {
    const download = this.downloads.get(key)
    if (download && download.status === 'downloading') {
      this.activeDownloads--
      this.updateStatus(key, 'paused')
      this.processQueue() // 处理队列中的下一个任务
    } else {
      this.updateStatus(key, 'paused')
    }
  }

  // 继续下载
  resumeDownload(key) {
    const download = this.downloads.get(key)
    if (download && download.status === 'paused') {
      // 如果当前活跃下载数未达到上限，直接开始下载
      if (this.activeDownloads < this.maxConcurrentDownloads) {
        this.activeDownloads++
        this.updateStatus(key, 'downloading')
        
        // 通知主进程继续下载
        if (window.audioAPI && window.audioAPI.downloadSong) {
          // 从下载信息中获取URL和filename
          const songInfo = download.songInfo
          if (songInfo && songInfo.url && songInfo.filename) {
            window.audioAPI.downloadSong({
              url: songInfo.url,
              filename: songInfo.filename,
              key: key,
              songInfo: songInfo
            })
          } else {
            // 如果没有URL信息，标记为错误
            this.updateStatus(key, 'error')
          }
        }
      } else {
        // 否则加入队列
        this.downloadQueue.unshift(key) // 使用unshift确保暂停的任务优先
        this.updateStatus(key, 'queued')
      }
    }
  }

  // 取消下载
  cancelDownload(key) {
    const download = this.downloads.get(key)
    if (download) {
      // 如果正在下载，减少活跃下载数
      if (download.status === 'downloading') {
        this.activeDownloads--
      }
      
      // 从队列中移除
      const queueIndex = this.downloadQueue.indexOf(key)
      if (queueIndex > -1) {
        this.downloadQueue.splice(queueIndex, 1)
      }
    }
    
    this.downloads.delete(key)
    
    // 处理队列中的下一个任务
    this.processQueue()
    
    // 更新全局下载状态
    this.updateDownloadStatus()
    
    this.notifyListeners()
  }

  // 获取所有下载
  getDownloads() {
    return Array.from(this.downloads.values())
  }

  // 获取当前活跃下载数
  getActiveDownloadsCount() {
    return this.activeDownloads
  }

  // 获取队列中的任务数
  getQueuedDownloadsCount() {
    return this.downloadQueue.length
  }

  // 设置最大并发下载数
  setMaxConcurrentDownloads(max) {
    this.maxConcurrentDownloads = max
    this.processQueue() // 重新处理队列
  }

  // 添加监听器
  addListener(listener) {
    this.listeners.add(listener)
  }

  // 移除监听器
  removeListener(listener) {
    this.listeners.delete(listener)
  }

  // 通知所有监听器
  notifyListeners() {
    const downloads = this.getDownloads()
    this.listeners.forEach((listener) => {
      try {
        listener(downloads)
      } catch (error) {
        console.error('监听器执行错误:', error)
      }
    })
  }

  // 新增：添加状态监听器
  addStatusListener(listener) {
    this.statusListeners.add(listener)
  }

  // 新增：移除状态监听器
  removeStatusListener(listener) {
    this.statusListeners.delete(listener)
  }

  // 新增：更新全局下载状态
  updateDownloadStatus() {
    const downloads = this.getDownloads()
    const hasActiveDownloads = downloads.some(download => 
      download.status === 'downloading' || download.status === 'paused' || download.status === 'queued'
    )
    
    const wasDownloading = this.isDownloading
    this.isDownloading = hasActiveDownloads
    
    // 如果状态发生变化，通知状态监听器
    if (wasDownloading !== this.isDownloading) {
      this.statusListeners.forEach((listener) => {
        try {
          listener(this.isDownloading)
        } catch (error) {
          console.error('状态监听器执行错误:', error)
        }
      })
    }
  }

  // 新增：获取当前下载状态
  getDownloadStatus() {
    return this.isDownloading
  }
}

// 全局下载管理器实例
export const downloadManager = new DownloadManager()
