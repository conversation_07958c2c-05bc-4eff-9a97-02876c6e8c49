// 下载管理器
class DownloadManager {
  constructor() {
    this.downloads = new Map()
    this.listeners = new Set()
    this.statusListeners = new Set() // 新增：状态变化监听器
    this.isDownloading = false // 新增：全局下载状态
  }

  // 添加下载任务
  addDownload(key, songInfo) {
    console.log('downloadManager: 添加下载任务', key, songInfo)
    const download = {
      key,
      name: songInfo.NAME,
      artist: songInfo.ARTIST,
      quality: songInfo.quality || '标准音质',
      progress: 0,
      status: 'downloading',
      size: songInfo.size || '--',
      totalSize: songInfo.size,
      downloadedSize: '0B',
      onPause: this.pauseDownload.bind(this, key),
      onResume: this.resumeDownload.bind(this, key),
      onCancel: this.cancelDownload.bind(this, key),
    }
    
    this.downloads.set(key, download)
    console.log('downloadManager: 当前下载任务数量', this.downloads.size)
    
    // 更新全局下载状态
    this.updateDownloadStatus()
    
    this.notifyListeners()
    return download
  }

  // 更新下载进度
  updateProgress(key, progress, downloadedSize) {
    console.log('downloadManager: updateProgress被调用', { key, progress, downloadedSize })
    const download = this.downloads.get(key)
    if (download) {
      download.progress = Math.min(progress, 100) // 确保进度不超过100%
      download.downloadedSize = downloadedSize
      console.log('downloadManager: 进度更新成功', { key, progress: download.progress, downloadedSize })
      this.notifyListeners()
    } else {
      console.log('downloadManager: 未找到下载任务', key)
    }
  }

  // 更新下载状态
  updateStatus(key, status) {
    const download = this.downloads.get(key)
    if (download) {
      download.status = status
      if (status === 'completed') {
        download.progress = 100
        // 下载完成后，延迟3秒自动清理任务
        setTimeout(() => {
          this.cancelDownload(key)
        }, 3000)
      }
      
      // 更新全局下载状态
      this.updateDownloadStatus()
      
      this.notifyListeners()
    }
  }

  // 暂停下载
  pauseDownload(key) {
    this.updateStatus(key, 'paused')
  }

  // 继续下载
  resumeDownload(key) {
    this.updateStatus(key, 'downloading')
  }

  // 取消下载
  cancelDownload(key) {
    this.downloads.delete(key)
    
    // 更新全局下载状态
    this.updateDownloadStatus()
    
    this.notifyListeners()
  }

  // 获取所有下载
  getDownloads() {
    return Array.from(this.downloads.values())
  }

  // 添加监听器
  addListener(listener) {
    this.listeners.add(listener)
  }

  // 移除监听器
  removeListener(listener) {
    this.listeners.delete(listener)
  }

  // 通知所有监听器
  notifyListeners() {
    const downloads = this.getDownloads()
    this.listeners.forEach((listener) => {
      try {
        listener(downloads)
      } catch (error) {
        console.error('监听器执行错误:', error)
      }
    })
  }

  // 新增：添加状态监听器
  addStatusListener(listener) {
    this.statusListeners.add(listener)
  }

  // 新增：移除状态监听器
  removeStatusListener(listener) {
    this.statusListeners.delete(listener)
  }

  // 新增：更新全局下载状态
  updateDownloadStatus() {
    const downloads = this.getDownloads()
    const hasActiveDownloads = downloads.some(download => 
      download.status === 'downloading' || download.status === 'paused'
    )
    
    const wasDownloading = this.isDownloading
    this.isDownloading = hasActiveDownloads
    
    // 如果状态发生变化，通知状态监听器
    if (wasDownloading !== this.isDownloading) {
      this.statusListeners.forEach((listener) => {
        try {
          listener(this.isDownloading)
        } catch (error) {
          console.error('状态监听器执行错误:', error)
        }
      })
    }
  }

  // 新增：获取当前下载状态
  getDownloadStatus() {
    return this.isDownloading
  }
}

// 全局下载管理器实例
export const downloadManager = new DownloadManager()

// 格式化文件大小
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i]
} 