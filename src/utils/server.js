import axios from 'axios'
import {userAgentList, userAgentMobile} from './userAgent'

const searchRequest = (type, data) => {
  let postData, referer
  if (type === 'search') {
    postData = { act: 'search', key: data, lang: '' }
    referer = `https://zz123.com/search/?key=${encodeURIComponent(data)}`
  } else if (type === 'home') {
    postData = { act: 'index_faxian', lang: '', page: data }
    referer = 'https://zz123.com'
  } else {
    return Promise.resolve({ status: 400, message: '无效的请求类型' })
  }
  return axios.post('https://zz123.com/ajax/', postData, {
    headers: {
      'User-Agent': userAgentList[Math.floor(Math.random() * userAgentList.length)],
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      'Referer': referer,
    }
  }).then(response => response.data)
    .catch(err => {
      console.error('搜索请求失败:', err)
      return { status: 500, message: '搜索请求失败' }
    })
}

const proxyRequest = ({ type, name, page, avatarUrl, musicId, quality }) => {
  let url, referer, userAgent
  if (type === 'musicId' || type === 'download') {
    userAgent = userAgentMobile[Math.floor(Math.random() * userAgentMobile.length)]
  } else {
    userAgent = userAgentList[Math.floor(Math.random() * userAgentList.length)]
  }
  if (type === 'bang') {
    url = 'https://www.kuwo.cn/'
    referer = ''
  } else if (type === 'search') {
    url = `https://www.kuwo.cn/openapi/v1/www/search/searchKey?key=${encodeURIComponent(name)}&httpsStatus=1&plat=web_www&from=`
    referer = `https://www.kuwo.cn/search/list?key=${encodeURIComponent(name)}`
  } else if (type === 'searchDetail') {
    url = `https://www.kuwo.cn/search/searchMusicBykeyWord?vipver=1&client=kt&ft=music&cluster=0&strategy=2012&encoding=utf8&rformat=json&mobi=1&issubtitle=1&show_copyright_off=1&pn=${page}&rn=20&all=${encodeURIComponent(name)}`
    referer = `https://www.kuwo.cn/search/list?key=${encodeURIComponent(name)}`
  } else if (type === 'avatar') {
    url = avatarUrl
    referer = 'https://www.kuwo.cn/'
  } else if (type === 'musicId') {
    // url = `http://mobi.kuwo.cn/mobi.s?f=web&source=jiakong&type=convert_url_with_sign&rid=${musicId.split('_')[1]}&br=320kmp3` 此接口已寄
    url = `https://mobi.kuwo.cn/mobi.s?f=web&source=kwwear_ar_2.2.3_Fwatch.apk&type=convert_url_with_sign&br=128kmp3&rid=${musicId.split('_')[1]}&from=PC`  // 新接口
    referer = 'https://www.kuwo.cn/'
  } else if (type === 'lyrics') {
    url = `https://www.kuwo.cn/openapi/v1/www/lyric/getlyric?musicId=${musicId}&httpsStatus=1&plat=web_www&from=`
    referer = `https://www.kuwo.cn/play_detail/${musicId}`
  } else if (type === 'download') {
    // 128kmp3、192kmp3、320kmp3、2000kflac
    // url = `http://mobi.kuwo.cn/mobi.s?f=web&source=jiakong&type=convert_url_with_sign&rid=${musicId.split('_')[1]}&br=${quality}`
    url = `https://mobi.kuwo.cn/mobi.s?f=web&source=kwwear_ar_2.2.3_Fwatch.apk&type=convert_url_with_sign&br=${quality}&rid=${musicId.split('_')[1]}&from=PC`
    referer = 'https://www.kuwo.cn/'
  } else {
    return Promise.resolve({ status: 404, message: '无效的请求类型' })
  }
  return axios.get(url, {
    responseType: type === 'avatar' ? 'arraybuffer' : '',
    headers: {
      'User-Agent': userAgent,
      'Referer': referer,
    },
  }).then(response => {
    return {
      status: 200,
      data: response.data,
      headers: response.headers
    }
  }).catch(err => {
    console.error('代理请求失败:', err)
    return { status: 500, message: '请求失败' }
  })
}

export {
  searchRequest,
  proxyRequest
}