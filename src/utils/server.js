import axios from 'axios'

const userAgentList = [
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:80.0) Gecko/20100101 Firefox/80.0',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.30 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Safari/605.1.15',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:80.0) Gecko/20100101 Firefox/80.0',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.30 Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.135 Safari/537.36 Edge/13.10586',
  'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.81 Safari/537.36 SE 2.X MetaSr 1.0',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.121 Safari/537.36 Edg/85.0.564.67'
]

const searchRequest = (type, data) => {
  let postData, referer
  if (type === 'search') {
    postData = { act: 'search', key: data, lang: '' }
    referer = `https://zz123.com/search/?key=${encodeURIComponent(data)}`
  } else if (type === 'home') {
    postData = { act: 'index_faxian', lang: '', page: data }
    referer = 'https://zz123.com'
  } else {
    return Promise.resolve({ status: 400, message: '无效的请求类型' })
  }
  return axios.post('https://zz123.com/ajax/', postData, {
    headers: {
      'User-Agent': userAgentList[Math.floor(Math.random() * userAgentList.length)],
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      'Referer': referer,
    }
  }).then(response => response.data)
    .catch(err => {
      console.error('搜索请求失败:', err)
      return { status: 500, message: '搜索请求失败' }
    })
}

// const proxyRequest = url => {
//   if (!url) {
//     return Promise.reject(new Error('Missing url'))
//   }
//   return axios.get(url, {
//     responseType: 'arraybuffer',
//     headers: {
//       'User-Agent': userAgentList[Math.floor(Math.random() * userAgentList.length)],
//       'Referer': 'https://zz123.com', // 根据需要设置
//     },
//   }).then(response => {
//     return {
//       status: 200,
//       data: response.data,
//       headers: response.headers
//     }
//   }).catch(err => {
//     console.error('代理请求失败:', err)
//     return { status: 500, message: '代理请求失败' }
//   })
// }

const proxyRequest = ({ type, name, page, avatarUrl, musicId, quality }) => {
  const userAgent = userAgentList[Math.floor(Math.random() * userAgentList.length)]
  let url = ''
  let referer = ''

  if (type === 'bang') {
    url = 'https://www.kuwo.cn/'
    referer = ''
  } else if (type === 'search') {
    url = `https://www.kuwo.cn/openapi/v1/www/search/searchKey?key=${encodeURIComponent(name)}&httpsStatus=1&plat=web_www&from=`
    referer = `https://www.kuwo.cn/search/list?key=${encodeURIComponent(name)}`
  } else if (type === 'searchDetail') {
    url = `https://www.kuwo.cn/search/searchMusicBykeyWord?vipver=1&client=kt&ft=music&cluster=0&strategy=2012&encoding=utf8&rformat=json&mobi=1&issubtitle=1&show_copyright_off=1&pn=${page}&rn=20&all=${encodeURIComponent(name)}`
    referer = `https://www.kuwo.cn/search/list?key=${encodeURIComponent(name)}`
  } else if (type === 'avatar') {
    url = avatarUrl
    referer = 'https://www.kuwo.cn/'
  } else if (type === 'musicId') {
    url = `http://mobi.kuwo.cn/mobi.s?f=web&source=jiakong&type=convert_url_with_sign&rid=${musicId.split('_')[1]}`
    referer = 'https://www.kuwo.cn/'
  } else if (type === 'lyrics') {
    url = `https://www.kuwo.cn/openapi/v1/www/lyric/getlyric?musicId=${musicId}&httpsStatus=1&plat=web_www&from=`
    referer = `https://www.kuwo.cn/play_detail/${musicId}`
  } else if (type === 'download') {
    // 128kmp3、192kmp3、320kmp3、2000kflac
    url = `http://mobi.kuwo.cn/mobi.s?f=web&source=jiakong&type=convert_url_with_sign&rid=${musicId.split('_')[1]}&br=${quality}`
    referer = 'https://www.kuwo.cn/'
  } else {
    return Promise.resolve({ status: 404, message: '无效的请求类型' })
  }
  return axios.get(url, {
    responseType: type === 'avatar' ? 'arraybuffer' : '',
    headers: {
      'User-Agent': userAgent,
      'Referer': referer,
    },
  }).then(response => {
    return {
      status: 200,
      data: response.data,
      headers: response.headers
    }
  }).catch(err => {
    console.error('代理请求失败:', err)
    return { status: 500, message: '请求失败' }
  })
}

export {
  searchRequest,
  proxyRequest
}