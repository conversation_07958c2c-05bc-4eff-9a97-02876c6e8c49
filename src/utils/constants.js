const LOCALHOST = 'http://localhost:9966'
const getAvatarUrl = item => {
  if (item.pic) return item.pic  // 如果是推荐歌单里的歌曲
  if (!item.web_albumpic_short || !item.web_artistpic_short) return
  return item.ALBUM ?
    'https://img2.kuwo.cn/star/albumcover/' + item.web_albumpic_short
    :
    'https://img1.kuwo.cn/star/starheads/' + item.web_artistpic_short
}

// 替换图片大小
function replaceSize(url, size) {
  return url.replace(/\/\d+\//, `/${size}/`)
}

export { LOCALHOST, getAvatarUrl, replaceSize }

