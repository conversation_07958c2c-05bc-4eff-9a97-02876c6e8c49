const { inflate } = require('zlib')

const encKey = Buffer.from([0x40, 0x47, 0x61, 0x77, 0x5e, 0x32, 0x74, 0x47, 0x51, 0x36, 0x31, 0x2d, 0xce, 0xd2, 0x6e, 0x69])

const decodeLyric = (str) =>
  new Promise((resolve, reject) => {
    if (!str.length) return
    const bufStr = Buffer.from(str, 'base64').slice(4)
    for (let i = 0, len = bufStr.length; i < len; i++) {
      bufStr[i] = bufStr[i] ^ encKey[i % 16]
    }
    inflate(bufStr, (err, result) => {
      if (err) return reject(err)
      resolve(result.toString())
    })
  })

const headExp = /^.*\[id:\$\w+]\n/

const encodeNames = {
  '&nbsp;': ' ',
  '&amp;': '&',
  '&lt;': '<',
  '&gt;': '>',
  '&quot;': '"',
  '&apos;': '\'',
  '&#039;': '\'',
}

const decodeName = (str = '') => str.replace(/&amp;|&lt;|&gt;|&quot;|&apos;|&#039;/gm, s => encodeNames[s])

const parseLyric = str => {
  str = str.replace(/\r/g, '')
  if (headExp.test(str)) str = str.replace(headExp, '')
  let trans = str.match(/\[language:([\w=\\/+]+)]/)
  let lyric
  let tLyric
  if (trans) {
    str = str.replace(/\[language:[\w=\\/+]+]\n/, '')
    let json = JSON.parse(Buffer.from(trans[1], 'base64').toString())
    for (const item of json.content) {
      if (item.type === 1) {
        tLyric = item.lyricContent
        break
      }
    }
  }
  let i = 0
  let rLyric = str.replace(/\[((\d+),\d+)].*/g, str => {
    let result = str.match(/\[((\d+),\d+)].*/)
    let time = parseInt(result[2])
    let ms = time % 1000
    time /= 1000
    let m = parseInt(time / 60).toString().padStart(2, '0')
    time %= 60
    let s = parseInt(time).toString().padStart(2, '0')
    time = `${m}:${s}.${ms}`
    if (tLyric) tLyric[i] = `[${time}]${tLyric[i++][0]}`
    return str.replace(result[1], time)
  })
  tLyric = tLyric ? tLyric.join('\n') : ''
  rLyric = rLyric.replace(/<(\d+,\d+),\d+>/g, '<$1>')
  rLyric = decodeName(rLyric)
  lyric = rLyric.replace(/<\d+,\d+>/g, '')
  tLyric = decodeName(tLyric)
  return {
    lyric,
    tLyric,
    rLyric,
  }
}

export {
  decodeLyric,
  parseLyric
}
