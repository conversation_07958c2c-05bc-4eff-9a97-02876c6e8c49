import React, { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { Avatar, Divider, List, Skeleton, theme } from 'antd'
import InfiniteScroll from 'react-infinite-scroll-component'
import './index.less'

const { useToken } = theme

const INITIAL_LETTER = '热门'
const INITIAL_CATEGORY = '全部'
const ARTIST_CATEGORIES = ['华语男', '华语女', '华语组合', '日韩男', '日韩女', '日韩组合', '欧美男', '欧美女', '欧美组合', '其他']
const ALPHABET = Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i))

const mockArtists = Array.from({ length: 100 }, (_, i) => ({
  id: `artist-${i}`,
  name: `歌手 ${i + 1}`,
  avatar: `https://i.pravatar.cc/150?u=artist-${i}`
}))

function Artist() {
  const { token } = useToken()
  const navigate = useNavigate()

  const [loading, setLoading] = useState(false)
  const [data, setData] = useState([])
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [selectedLetter, setSelectedLetter] = useState(INITIAL_LETTER)
  const [selectedCategory, setSelectedCategory] = useState(INITIAL_CATEGORY)

  const limit = 20

  const fetchData = useCallback((pageToLoad) => {
    setLoading(true)
    // 模拟网络请求
    setTimeout(() => {
      const startIndex = (pageToLoad - 1) * limit
      const endIndex = startIndex + limit
      const results = mockArtists.slice(startIndex, endIndex)

      setData(prevData => (pageToLoad === 1 ? results : [...prevData, ...results]))
      setHasMore(endIndex < mockArtists.length)
      setLoading(false)
      setPage(pageToLoad)
    }, 500)
  }, [])

  useEffect(() => {
    // fetchData(1)
  }, [selectedLetter, selectedCategory, fetchData])

  const loadMoreData = () => {
    if (loading || !hasMore) {
      return
    }
    fetchData(page + 1)
  }

  const itemClick = (id) => {
    navigate(`/home/<USER>/${id}`)
  }

  return (
    <div
      className="artist-container"
      style={{
        '--primary-color': token.colorPrimary,
        '--primary-color-hover': token.colorPrimaryHover,
        '--container-bg': token.colorBgContainer,
        '--text-color': token.colorText,
        '--text-color-secondary': token.colorTextSecondary,
        '--component-background': token.colorFillAlter,
        // 遵从您的指示，为滚动条适配主题色
        '--artist-scrollbar-track': token.colorBgContainer,
        '--artist-scrollbar-thumb': token.colorBorder,
        '--artist-scrollbar-thumb-hover': token.colorPrimary,
      }}
    >
      <div className="artist-header">
        <div className="artist-filter">
          <div className="artist-filter-letters">
             <span
               className={`tag${selectedLetter === '热门' ? ' active' : ''}`}
               key="热门"
               onClick={() => setSelectedLetter('热门')}>
              热门
            </span>
            <div className="filter-tags-wrapper">
              {ALPHABET.map(letter => (
                <span className={`tag${selectedLetter === letter ? ' active' : ''}`}
                      key={letter}
                      onClick={() => setSelectedLetter(letter)}>
                  {letter}
                </span>
              ))}
              <span className={`tag${selectedLetter === '#' ? ' active' : ''}`}
                    key="#"
                    onClick={() => setSelectedLetter('#')}>
                #
              </span>
            </div>
          </div>
          <div className="artist-filter-category">
            <span className={`tag${selectedCategory === '全部' ? ' active' : ''}`}
                  key="全部"
                  onClick={() => setSelectedCategory('全部')}
            >
            全部
          </span>
            <div className="filter-tags-wrapper">
              {ARTIST_CATEGORIES.map(cat => (
                <span
                  className={`tag${selectedCategory === cat ? ' active' : ''}`}
                  key={cat}
                  onClick={() => setSelectedCategory(cat)}
                >
                  {cat}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div id="artist-scrollableDiv" className="artist-list-container">
        <InfiniteScroll
          dataLength={data.length}
          next={loadMoreData}
          hasMore={hasMore}
          loader={data.length ? <Skeleton avatar paragraph={{ rows: 1 }} active/> : ''}
          endMessage={<Divider plain>到底了...</Divider>}
          scrollableTarget="artist-scrollableDiv"
        >
          <List
            grid={{ gutter: 24, xs: 2, sm: 3, md: 4, lg: 5, xl: 6, xxl: 7 }}
            dataSource={data}
            renderItem={(item) => (
              <List.Item key={item.id} onClick={() => itemClick(item.id)}>
                <div className="artist-list-item">
                  <Avatar src={item.avatar} size={150}/>
                  <div className="artist-name">{item.name}</div>
                </div>
              </List.Item>
            )}
          />
        </InfiniteScroll>
      </div>
    </div>
  )
}

export default Artist