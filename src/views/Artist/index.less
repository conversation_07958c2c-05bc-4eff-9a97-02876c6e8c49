.artist-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  height: 100%;
  background-color: var(--container-bg, #f9f9f9);
  padding: 20px 5px;
  box-sizing: border-box;
  width: 100%;

  .artist-header {

    .artist-filter {
      background: var(--component-background, #fff);
      padding: 16px;
      border-radius: 8px;

      .artist-filter-letters,
      .artist-filter-category {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .filter-tags-wrapper {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          flex: 1;
          margin-left: 16px;
        }
      }

      .tag {
        padding: 4px 12px;
        border-radius: 16px;
        cursor: pointer;
        transition: all 0.3s;
        background-color: var(--component-background, #f0f0f0);
        color: var(--text-color, #555);
        font-size: 14px;
        min-width: 48px;
        text-align: center;
        user-select: none;

        &:hover {
          background-color: var(--primary-color-hover, #e0e0e0);
          color: #fff;
        }

        &.active {
          background-color: var(--primary-color, #1890ff);
          color: #fff;
          font-weight: bold;
        }
      }
    }
  }

  .artist-list-container {
    // 假设 Header 是 64px, Player 是 72px, 上下 padding 是 40px, header-margin 是 20px
    // 64 + 72 + 40 + 20 = 196
    height: calc(100vh - 370px);
    overflow-y: auto;
    padding: 0 16px;
    width: 100%;

    &::-webkit-scrollbar {
      width: 8px; /* 纵向滚动条宽度 */
      height: 8px; /* 横向滚动条高度（如需支持横向滚动） */
    }

    /* 滚动条轨道（背景） */

    &::-webkit-scrollbar-track {
      background-color: var(--artist-scrollbar-track, #f5f5f5);
      border-radius: 4px;
    }

    /* 滚动条滑块（拖动部分） */

    &::-webkit-scrollbar-thumb {
      background-color: var(--artist-scrollbar-thumb, #ccc);
      border-radius: 4px;

      &:hover {
        background-color: var(--artist-scrollbar-thumb-hover, #999); /* 鼠标悬浮效果 */
      }
    }
  }

  .ant-list-item {
    padding: 0 !important;
    border: none !important;
    margin-bottom: 24px;
  }

  .artist-list-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: transform 0.2s ease-in-out;

    &:hover {
      transform: translateY(-5px);
    }

    .ant-avatar {
      margin-bottom: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .artist-name {
      font-size: 14px;
      color: var(--text-color, #333);
      font-weight: 500;
    }
  }
}