.playlist-toolbar {
  position: sticky;
  top: 0;
  z-index: 2;
  background: var(--playlist-bg, rgb(20, 20, 20));
}

.music-scrollableDiv {
  background-color: var(--playlist-bg, rgb(20, 20, 20));
  min-height: 100vh;
  height: 100%;
  margin: 0;
  padding: 0 2px 190px 2px;

  .list-item {
    transition: background 0.3s;
    display: flex;
    justify-content: flex-end;
    background: var(--playlist-bg, rgb(20, 20, 20));

    &.selected {
      background: var(--playlist-selected-bg, rgb(20, 20, 20)) !important;
    }

    .duration, .album {
      flex: 1;
      color: var(--playlist-text-desc, #000);
    }

    .list-item-meta {
      flex: 2;
      margin-top: 0;
      padding-left: 5px;
      color: var(--playlist-text-desc, #000);

      .list-avatar {
        margin-inline-end: 0;
      }

      span {
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 180px;
      }
    }

    .action {
      flex: 0.5;
      text-align: right;

      .more-icon, .heart-icon {
        padding-left: 10px;
        font-size: 18px;
        cursor: pointer;
        color: var(--playlist-text, #999);
        transition: color 0.3s;

        &:hover {
          color: var(--playlist-text, #333);
        }
      }
    }

    .album, .duration {
      text-align: center;
    }
  }

  .list-item:hover {
    background: rgba(140, 140, 140, 0.1);
  }

  .list-idx {
    font-size: 15px;
    color: var(--playlist-text-desc, rgba(0, 0, 0, 0.39));
  }
}

body {
  background: var(--playlist-bg, #181818);
  margin: 0;
  padding: 0;
}

.album {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
  text-align: center;
}

.music-scrollableDiv {
  // 自定义滚动条样式 (for Webkit browsers)
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--playlist-scrollbar-track, transparent);
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--playlist-scrollbar-thumb, #ccc);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: var(--playlist-scrollbar-thumb-hover, #999);
  }

  // 隐藏滚动条的上下箭头按钮
  &::-webkit-scrollbar-button {
    display: none;
  }
}

.download-scrollableDiv {
  // 自定义滚动条样式 (for Webkit browsers)
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--download-scrollbar-track, transparent);
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--download-scrollbar-thumb, #ccc);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: var(--download-scrollbar-thumb-hover, #999);
  }

  // 隐藏滚动条的上下箭头按钮
  &::-webkit-scrollbar-button {
    display: none;
  }

  // 下载列表项样式
  .download-list-item {
    &:hover {
      background-color: rgba(0, 0, 0, 0.1) !important;
    }
  }
}