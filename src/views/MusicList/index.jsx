import React, { useEffect, useRef, useState } from 'react'
import {
  Avatar,
  Button,
  Checkbox,
  ConfigProvider,
  Divider,
  Dropdown,
  Empty,
  List,
  message as MESSAGE,
  Modal,
  Radio,
  Skeleton,
  theme,
  Popover
} from 'antd'
import {
  BarsOutlined,
  CaretRightOutlined,
  DownloadOutlined,
  HeartOutlined,
  MoreOutlined,
  PlusOutlined,
  SearchOutlined,
  CloseOutlined,
  SwapOutlined,
  UnorderedListOutlined,
  CheckOutlined,
  FolderOutlined,
  AimOutlined
} from '@ant-design/icons'
import InfiniteScroll from 'react-infinite-scroll-component'
import './index.less'
import axios from 'axios'
import { getAvatarUrl, LOCALHOST } from '@/utils/constants'
import { downloadManager } from '@/utils/downloadManager'
import avatar from '@/assets/imgs/avatar.png'

// 工具函数：格式化时长 mm:ss
function formatDuration(duration) {
  // 确保duration是数字
  const dur = parseFloat(duration) || 0

  // 处理NaN或无效值
  if (isNaN(dur) || dur < 0) {
    return '00:00'
  }

  const minutes = Math.floor(dur / 60)
  const seconds = Math.floor(dur % 60)
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// 工具函数：解析 MINFO 属性，提取音质和大小信息
function parseMINFO(info) {
  if (!info) return []

  // 根据比特率映射音质
  const getQualityByBitrate = (bitrate) => {
    const bitrateNum = parseInt(bitrate)
    if (bitrateNum >= 2000) return '无损音质'
    if (bitrateNum >= 320) return '顶级音质'
    if (bitrateNum >= 192) return '高品音质'
    if (bitrateNum >= 128) return '标准音质'
    return '标准音质'
  }

  try {
    // 按分号分割每个音质选项
    const options = info.split(';')
    const result = []
    options.forEach(option => {
      if (!option.trim()) return
      // 解析每个选项的属性
      const attrs = {}
      option.split(',').forEach(attr => {
        const [key, value] = attr.split(':')
        if (key && value) {
          attrs[key.trim()] = value.trim()
        }
      })
      if (attrs.level && attrs.size && attrs.bitrate) {
        result.push({
          level: attrs.level,
          bitrate: attrs.bitrate,
          format: attrs.format,
          size: attrs.size,
          quality: getQualityByBitrate(attrs.bitrate)
        })
      }
    })
    return result
  } catch (error) {
    console.error('解析 MINFO 失败:', error)
    return []
  }
}

// 工具函数：根据音质选择获取对应的文件大小
function getFileSizeByQuality(info, quality) {
  const options = parseMINFO(info)
  if (!options.length) return null

  // 根据选择的音质查找对应的比特率范围
  const getBitrateRange = (quality) => {
    switch (quality) {
      case '128kmp3':
        return { min: 128, max: 191 } // 128k
      case '192kmp3':
        return { min: 192, max: 319 } // 192k
      case '320kmp3':
        return { min: 320, max: 1999 } // 320k
      case '2000kflac':
        return { min: 2000, max: Infinity } // 2000k+
      default:
        return { min: 128, max: 191 }
    }
  }

  const bitrateRange = getBitrateRange(quality)

  // 找到匹配比特率范围的音质选项
  let matchedOption = options.find(option => {
    const bitrate = parseInt(option.bitrate)
    return bitrate >= bitrateRange.min && bitrate <= bitrateRange.max
  })

  // 如果没有找到匹配的音质，选择比选择音质低的最高音质
  if (!matchedOption) {
    // 按比特率排序，选择比目标比特率低的最高比特率
    const targetBitrate = bitrateRange.min
    const lowerBitrateOptions = options.filter(option =>
      parseInt(option.bitrate) < targetBitrate
    )

    if (lowerBitrateOptions.length > 0) {
      // 选择比目标比特率低的最高比特率
      const sortedLowerOptions = lowerBitrateOptions.sort((a, b) =>
        parseInt(b.bitrate) - parseInt(a.bitrate)
      )
      matchedOption = sortedLowerOptions[0]
    } else {
      // 如果没有更低的比特率，才选择最高比特率
      const sortedOptions = options.sort((a, b) =>
        parseInt(b.bitrate) - parseInt(a.bitrate)
      )
      matchedOption = sortedOptions[0]
    }
  }
  return matchedOption ? matchedOption.size : null
}

/**
 * PlayList 组件（自包含批量操作、无限滚动、播放等功能）
 * @param {Array} data 播放列表数据
 * @param {string} currentMusicId 当前播放音乐ID
 * @param {Function} onPlay 播放回调（item）
 * @param {Function} onPlaySelected 批量操作下播放选中歌曲回调
 * @param {Function} loadMoreData 加载更多数据回调
 * @param {boolean} hasMore 是否还有更多数据
 * @param {boolean} loading 加载状态
 * @param {object} token 主题token（可选）
 * @param {string} className 自定义样式类（可选）
 * @param {boolean} isShowToolbar 是否显示操作栏
 * @param {number} isShowEndMessage 歌曲数量大于指定值时显示底部消息
 * @param {boolean} isLocalPlay 是否本地播放
 * @param {function} onRefresh 刷新回调
 */

function MusicList({
  data = [],
  currentMusicId,
  onPlay,
  onPlaySelected,
  loadMoreData,
  hasMore = false,
  loading = false,
  token,
  className = '',
  isShowToolbar = true,
  isShowEndMessage = 10,
  isLocalPlay = false,
  onRefresh
}) {
  const [message, contextHolder] = MESSAGE.useMessage()
  // 批量操作相关内部状态
  const [checkedShow, setCheckedShow] = useState(false)
  const [checkedList, setCheckedList] = useState([])
  const scrollRef = useRef(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [downloadQuality, setDownloadQuality] = useState('128kmp3') // 音质选择
  const [selectedSongs, setSelectedSongs] = useState([]) // 选中的歌曲
  const [downloadPath, setDownloadPath] = useState('') // 下载路径
  const [downloadListData, setDownloadListData] = useState([])
  const [isRightClickDownload, setIsRightClickDownload] = useState(false) // 是否是右键下载
  const [isDownloading, setIsDownloading] = useState(false) // 是否正在下载
  // 添加搜索框相关状态
  const [showSearchBox, setShowSearchBox] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [filteredData, setFilteredData] = useState(data) // 过滤后的数据状态
  const searchInputRef = useRef(null)
  // 添加排序相关状态
  const [sortField, setSortField] = useState('default') // 排序字段
  const [sortOrder, setSortOrder] = useState('asc') // 排序方向
  // 扫描歌曲目录相关状态
  const [addMenuVisible, setAddMenuVisible] = useState(false) // 添加菜单可见性
  const [autoScanOpen, setAutoScanOpen] = useState(false) // 自动扫描弹窗可见性
  const [scanDirs, setScanDirs] = useState([]) // 扫描的目录
  const [scanning, setScanning] = useState(false) // 扫描按钮是否可用

  const { token: antdToken } = theme.useToken ? theme.useToken() : { token: {} }

  // 主题切换时同步 token 到 CSS 变量
  useEffect(() => {
    // fallback: 如果 token 不存在，尝试从 antd theme context 获取
    let t = token
    if (!t || Object.keys(t).length === 0) t = antdToken

    if (t) {
      // 设置到 document.body 确保全局可用
      document.body.style.setProperty('--control-bar-bg', t.colorBgElevated)
      document.body.style.setProperty('--control-item-hover-bg', t.colorPrimaryBg)
      document.body.style.setProperty('--control-icon-color', t.colorText)
      document.body.style.setProperty('--control-icon-hover-color', t.colorPrimary)
      document.body.style.setProperty('--control-border-color', t.colorBorderSecondary)
      document.body.style.setProperty('--control-input-bg', t.colorBgContainer)
      document.body.style.setProperty('--control-text-color', t.colorText)
      document.body.style.setProperty('--control-placeholder-color', t.colorTextDescription)
      document.body.style.setProperty('--control-focus-color', t.colorPrimary)

      // 同时设置到 scrollRef.current（保持原有逻辑）
      if (scrollRef.current) {
        scrollRef.current.style.setProperty('--playlist-bg', t.colorBgContainer)
        scrollRef.current.style.setProperty('--playlist-selected-bg', t.colorPrimaryBg)
        scrollRef.current.style.setProperty('--playlist-text', t.colorText)
        scrollRef.current.style.setProperty('--playlist-text-desc', t.colorTextDescription)
        scrollRef.current.style.setProperty('--playlist-border', t.colorBorderSecondary)
        scrollRef.current.style.setProperty('--playlist-scrollbar-track', t.colorBgContainer)
        scrollRef.current.style.setProperty('--playlist-scrollbar-thumb', t.colorBorderSecondary)
        scrollRef.current.style.setProperty('--playlist-scrollbar-thumb-hover', t.colorPrimary)
        // 添加 control-bar 相关的 CSS 变量
        scrollRef.current.style.setProperty('--control-bar-bg', t.colorBgElevated)
        scrollRef.current.style.setProperty('--control-item-hover-bg', t.colorPrimaryBg)
        scrollRef.current.style.setProperty('--control-icon-color', t.colorText)
        scrollRef.current.style.setProperty('--control-icon-hover-color', t.colorPrimary)
        // 添加搜索框相关的 CSS 变量
        scrollRef.current.style.setProperty('--control-border-color', t.colorBorderSecondary)
        scrollRef.current.style.setProperty('--control-input-bg', t.colorBgContainer)
        scrollRef.current.style.setProperty('--control-text-color', t.colorText)
        scrollRef.current.style.setProperty('--control-placeholder-color', t.colorTextDescription)
        scrollRef.current.style.setProperty('--control-focus-color', t.colorPrimary)
      }
    }
  }, [token, antdToken])

  // 单项勾选
  const onCheckChange = item => {
    setCheckedList(prev => {
      if (prev.includes(item.MUSICRID)) {
        return prev.filter(id => id !== item.MUSICRID)
      } else {
        return [...prev, item.MUSICRID]
      }
    })
  }

  // 列表项点击（批量模式下勾选）
  const itemCheckClick = (e, item) => {
    if (checkedShow) {
      onCheckChange(item)
    }
  }

  // 全选/取消全选
  const handleCheckAll = () => {
    const isAllSelected = filteredData.length > 0 && filteredData.every(item => checkedList.includes(item.MUSICRID))
    if (isAllSelected) {
      setCheckedList([])
    } else {
      setCheckedList(filteredData.map(item => item.MUSICRID))
    }
  }

  // 退出批量操作
  const handleExitBatch = () => {
    setCheckedShow(false)
    setCheckedList([])
  }

  // 播放全部（可根据需要扩展）
  const handlePlayAll = () => {
    if (filteredData.length > 0) {
      if (onPlaySelected) {
        // 播放所有过滤后的歌曲
        onPlaySelected(filteredData)
      } else if (onPlay) {
        // 如果没有批量播放功能，则播放第一首
        onPlay(filteredData[0])
      }
    }
  }

// 添加下载列表的全选/取消全选函数
  const handleDownloadCheckAll = () => {
    const isAllSelected = downloadListData.length > 0 && downloadListData.every(item => selectedSongs.includes(item.MUSICRID))
    if (isAllSelected) {
      setSelectedSongs([])
    } else {
      setSelectedSongs(downloadListData.map(item => item.MUSICRID))
    }
  }

// 修改计算总文件大小的逻辑，使用真实的 MINFO 数据
  const totalSize = selectedSongs.reduce((total, songId) => {
    const song = downloadListData.find(item => item.MUSICRID === songId)
    if (!song) return total

    // 尝试从 MINFO 获取真实大小
    const realSize = getFileSizeByQuality(song.MINFO, downloadQuality)
    if (realSize) {
      // 解析大小字符串（如 "9.60Mb", "768.88Kb"）
      const sizeMatch = realSize.match(/^([\d.]+)([KMGT]?[bB])$/)
      if (sizeMatch) {
        const [, size, unit] = sizeMatch
        const sizeNum = parseFloat(size)
        const multiplier = unit.toLowerCase() === 'kb' ? 1024 :
          unit.toLowerCase() === 'mb' ? 1024 * 1024 :
            unit.toLowerCase() === 'gb' ? 1024 * 1024 * 1024 : 1
        return total + (sizeNum * multiplier)
      }
    }
    // 如果没有 MINFO 数据，不计入总大小
    return total
  }, 0)

  // 格式化文件大小
  function formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i]
  }

// 开始下载
  const startDownload = async () => {
    try {
      setIsDownloading(true)

      // 确保DownLoad组件有时间注册监听器
      await new Promise(resolve => setTimeout(resolve, 100))

      // 获取选中的歌曲信息
      const songsToDownload = downloadListData.filter(item =>
        selectedSongs.includes(item.MUSICRID)
      )

      // 批量下载
      for (let i = 0; i < songsToDownload.length; i++) {
        const song = songsToDownload[i]
        // 获取对应音质的下载URL
        const downloadUrl = await getDownloadUrl(song.MUSICRID, downloadQuality)

        // 添加到下载管理器（无论是否有URL都添加）
        const downloadKey = `${song.MUSICRID}_${Date.now()}`
        const songInfo = {
          MUSICRID: song.MUSICRID,
          NAME: song.NAME,
          ARTIST: song.ARTIST,
          ALBUM: song.ALBUM,
          DURATION: song.DURATION || 0,
          quality: downloadQuality === '128kmp3' ? '标准音质' :
            downloadQuality === '192kmp3' ? '高品音质' :
              downloadQuality === '320kmp3' ? '顶级音质' :
                downloadQuality === '2000kflac' ? '无损音质' : '标准音质',
          size: getFileSizeByQuality(song.MINFO, downloadQuality) || '--',
          cover: getAvatarUrl(song)
        }

        if (downloadUrl) {
          // 清理文件名，移除特殊字符和限制长度
          const cleanArtist = song.ARTIST.replace(/[<>:"/\\|?*$]/g, '').trim()
          const cleanName = song.NAME.replace(/[<>:"/\\|?*$]/g, '').trim()

          // 限制文件名长度，避免过长
          const maxLength = 100
          const artistPart = cleanArtist.length > maxLength / 2 ? cleanArtist.substring(0, maxLength / 2) : cleanArtist
          const namePart = cleanName.length > maxLength / 2 ? cleanName.substring(0, maxLength / 2) : cleanName

          // 根据音质选择确定文件扩展名
          let fileExtension = '.mp3'

          if (downloadQuality === '2000kflac') {
            fileExtension = '.flac'
          } else if (downloadQuality === '320kmp3') {
            fileExtension = '.mp3'
          } else if (downloadQuality === '192kmp3') {
            fileExtension = '.mp3'
          } else if (downloadQuality === '128kmp3') {
            fileExtension = '.mp3'
          }

          // 根据URL中的格式进一步确定扩展名
          if (downloadUrl.toLowerCase().includes('.flac')) {
            fileExtension = '.flac'
          } else if (downloadUrl.toLowerCase().includes('.m4a')) {
            fileExtension = '.m4a'
          } else if (downloadUrl.toLowerCase().includes('.wav')) {
            fileExtension = '.wav'
          } else if (downloadUrl.toLowerCase().includes('.aac')) {
            fileExtension = '.aac'
          } else if (downloadUrl.toLowerCase().includes('.ogg')) {
            fileExtension = '.ogg'
          } else if (downloadUrl.toLowerCase().includes('.mgg')) {
            // 添加MGG格式支持
            fileExtension = '.mgg'
          }

          const filename = `${artistPart} - ${namePart}${fileExtension}`

          // 将下载信息添加到songInfo中
          songInfo.url = encodeURI(downloadUrl)
          songInfo.filename = filename
        }

        downloadManager.addDownload(downloadKey, songInfo)

        if (!downloadUrl) {
          // 如果没有URL，标记为错误状态
          downloadManager.updateStatus(downloadKey, 'error')
          console.error('无法获取下载URL')
        }
      }

      // 关闭下载对话框
      setIsModalOpen(false)
      setIsDownloading(false)

      // 如果是右键下载，清空选中状态
      if (isRightClickDownload) {
        setCheckedList([])
        setIsRightClickDownload(false)
      }
    } catch (error) {
      message.error('下载失败: ' + error.message)
      setIsDownloading(false)
    }
  }

  // 获取下载URL的函数
  const getDownloadUrl = async (musicId, quality) => {
    try {
      const res = await axios.get(LOCALHOST + '/proxy', {
        params: {
          type: 'download',
          musicId: musicId,
          quality: quality // 传递音质参数
        }
      })

      const url = res.data?.data?.url

      // 检查URL是否有效
      if (url && typeof url === 'string') {
        // 检查URL是否指向音频文件
        const audioExtensions = ['.mp3', '.m4a', '.flac', '.wav', '.aac', '.ogg']
        const hasAudioExtension = audioExtensions.some(ext => url.toLowerCase().includes(ext))

        if (!hasAudioExtension) {
          console.warn('下载URL可能不是音频文件:', url)
        }

        return url
      }

      return null
    } catch (error) {
      console.error('获取下载URL失败:', error)
      return null
    }
  }

  // 下载列表项点击
  const handleDownloadItemClick = (item) => {
    setSelectedSongs(prev => {
      if (prev.includes(item.MUSICRID)) {
        return prev.filter(id => id !== item.MUSICRID)
      } else {
        return [...prev, item.MUSICRID]
      }
    })
  }

  // 更改下载目录
  const handleChangeDir = async () => {
    const dir = await window.audioAPI.selectDownloadDir()
    if (dir) {
      setDownloadPath(dir)
    }
  }

  // 右键下载歌曲
  const handleDownload = async item => {
    // 下载本地歌曲直接返回
    if (!item.MUSICRID) return
    if (item.path) return message.warning('已下载!')
    // 设置当前歌曲为选中状态，然后打开下载对话框
    setCheckedList([item.MUSICRID])
    setIsRightClickDownload(true) // 标记为右键下载
    setIsModalOpen(true)
  }

  useEffect(() => {
    const getDownloadDir = async () => {
      if (window.audioAPI && window.audioAPI.getDownloadDir) {
        const dir = await window.audioAPI.getDownloadDir()
        setDownloadPath(dir)
      }
    }
    getDownloadDir()
  }, [])

  // 当 checkedList 变化时，更新 downloadListData
  useEffect(() => {
    if (checkedList.length > 0) {
      const filteredData = data.filter(item => checkedList.includes(item.MUSICRID))
      setDownloadListData(filteredData)
      // 初始化选中状态为全选
      setSelectedSongs(filteredData.map(item => item.MUSICRID))
    } else {
      setDownloadListData([])
      setSelectedSongs([])
    }
  }, [checkedList, data])

  // 当 Modal 打开时，确保数据同步
  useEffect(() => {
    if (isModalOpen && checkedList.length > 0) {
      // Modal 打开时，重新设置 downloadListData 和 selectedSongs
      const filteredData = data.filter(item => checkedList.includes(item.MUSICRID))
      setDownloadListData(filteredData)
      setSelectedSongs(filteredData.map(item => item.MUSICRID))
    }
  }, [isModalOpen, checkedList, data])

  // 监听下载进度和完成事件
  useEffect(() => {
    let unlistenProgress
    let unlistenDone
    if (window.audioAPI) {
      // 监听下载进度
      unlistenProgress = window.audioAPI.onDownloadProgress((progress) => {
        // 更新下载管理器中的进度
        if (progress.key && progress.progress !== undefined) {
          downloadManager.updateProgress(
            progress.key,
            progress.progress,
            progress.downloadedSize || '0B'
          )
        }
      })

      // 监听下载完成
      unlistenDone = window.audioAPI.onDownloadDone((info) => {
        console.log('MusicList: 下载完成: ', info.filename)
        if (info.state === 'completed') {
          // 更新下载状态为完成
          if (info.key) {
            downloadManager.updateStatus(info.key, 'completed')
          }
        } else {
          // 下载失败时只更新状态，不显示通知
          if (info.key) {
            downloadManager.updateStatus(info.key, 'error')
          }
        }
      })
    }

    // 在组件销毁时，调用清理函数
    return () => {
      if (unlistenProgress) unlistenProgress()
      if (unlistenDone) unlistenDone()
    }
  }, [])

  // 监听下载管理器更新，显示下载完成通知
  useEffect(() => {
    const handleDownloadsUpdate = (downloads) => {
      // 移除下载完成通知逻辑，只保留状态管理
      // 下载状态指示器会通过导航栏图标动画显示
    }
    downloadManager.addListener(handleDownloadsUpdate)

    return () => {
      downloadManager.removeListener(handleDownloadsUpdate)
    }
  }, [])

  // 添加搜索过滤逻辑
  useEffect(() => {
    if (!searchValue.trim()) {
      setFilteredData(data)
    } else {
      const filtered = data.filter(item =>
        item.NAME?.toLowerCase().includes(searchValue.toLowerCase()) ||
        item.ARTIST?.toLowerCase().includes(searchValue.toLowerCase()) ||
        item.ALBUM?.toLowerCase().includes(searchValue.toLowerCase())
      )
      setFilteredData(filtered)
    }
  }, [searchValue, data])

  // 监听封面更新，局部更新列表数据中的 localCover（兼容 preload 暴露的不同接口）
  useEffect(() => {
    if (!window) return
    const subscribe = (window.coverAPI && window.coverAPI.onCoverUpdated) || window.onCoverUpdated
    if (!subscribe) return
    const off = subscribe(({ hash, localCover, MUSICRID } = {}) => {
      if (!hash && !MUSICRID) return
      setFilteredData(prev => prev.map(item => (item.hash === hash || item.MUSICRID === MUSICRID) ? { ...item, localCover } : item))
    })
    return () => { try { if (typeof off === 'function') off() } catch (e) {} }
  }, [])

  // 修改搜索过滤逻辑，保持排序
  useEffect(() => {
    let filtered = data

    if (searchValue.trim()) {
      filtered = data.filter(item => {
        const searchLower = searchValue.toLowerCase()
        return (
          (item.NAME || item.SONGNAME || '').toLowerCase().includes(searchLower) ||
          (item.ARTIST || '').toLowerCase().includes(searchLower) ||
          (item.ALBUM || '').toLowerCase().includes(searchLower)
        )
      })
    }

    // 应用当前排序
    if (sortField !== 'default') {
      filtered = [...filtered].sort((a, b) => {
        let aValue, bValue

        switch (sortField) {
          case 'name':
            aValue = (a.NAME || a.SONGNAME || '').toLowerCase()
            bValue = (b.NAME || b.SONGNAME || '').toLowerCase()
            break
          case 'artist':
            aValue = (a.ARTIST || '').toLowerCase()
            bValue = (b.ARTIST || '').toLowerCase()
            break
          case 'album':
            aValue = (a.ALBUM || '').toLowerCase()
            bValue = (b.ALBUM || '').toLowerCase()
            break
          case 'duration':
            aValue = parseInt(a.DURATION || 0)
            bValue = parseInt(b.DURATION || 0)
            break
          case 'created':
            aValue = parseInt(a.created || 0)
            bValue = parseInt(b.created || 0)
            break
          default:
            return 0
        }

        if (sortOrder === 'asc') {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
        } else {
          return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
        }
      })
    }

    setFilteredData(filtered)
  }, [data, searchValue, sortField, sortOrder])

  // 渲染批量操作栏
  const renderBatchBar = () => (
    <div className="playlist-toolbar">
      <span className="list-checked">
        <Checkbox
          checked={filteredData.length > 0 && filteredData.every(item => checkedList.includes(item.MUSICRID))}
          indeterminate={checkedList.length > 0 && !filteredData.every(item => checkedList.includes(item.MUSICRID))}
          onClick={handleCheckAll}
        />
        <span style={{ marginLeft: 4 }}>已选</span>{checkedList.length}
      </span>
      <Button className="batch-button" icon={<PlusOutlined/>} title="添加到" style={{ marginLeft: 8 }}/>
      {
        !isLocalPlay && <Button className="download-all-button"
                                icon={<DownloadOutlined/>}
                                title="下载"
                                style={{ marginLeft: 8 }}
                                onClick={() => {
                                  if (checkedList.length)
                                    setIsModalOpen(true)
                                }}

        />
      }
      <Button className="play-all-button" color="danger" variant="solid" icon={<CaretRightOutlined/>}
              autoInsertSpace={false} style={{ marginLeft: 8 }} onClick={() => onPlaySelected(downloadListData)}>
        播放
      </Button>
      <Button className="batch-button" autoInsertSpace={false} style={{ float: 'right' }} onClick={handleExitBatch}>
        退出批量操作
      </Button>
    </div>
  )

  // 添加排序函数
  const handleSort = (field, order) => {
    setSortField(field)
    setSortOrder(order)
  }

  // 重置排序
  const resetSort = () => {
    setSortField('default')
    setSortOrder('asc')
  }

  // 手动添加歌曲
  const handleManualAdd = async () => {
    setAddMenuVisible(false)
    try {
      const { success, message: MESSAGE, songs, skipped } = await window.audioAPI.selectAudioFiles()
      if (skipped) {
        message.info(`已跳过 ${skipped} 首已存在的歌曲`)
      }
      if (success) {
        if (songs.length === 0) return
        message.success(`成功添加 ${songs.length} 首歌曲`)
        // 更新播放列表
        onRefresh && onRefresh()
      } else {
        if (MESSAGE === '取消选择') return
        message.error(MESSAGE || '添加失败')
      }
    } catch (error) {
      message.error('添加失败: ' + error.message)
    }
  }

  // 点击自动扫描目录
  const handleAutoScan = async () => {
    await loadDefaultDirs()
    setAutoScanOpen(true)
    setAddMenuVisible(false)
  }

  // 读取目录（从 store）
  const loadDefaultDirs = async () => {
    const dirs = await window.audioAPI.getDefaultMusicDirs()
    setScanDirs(dirs || [])
  }

  // 选择目录（已持久化，直接使用返回的最新列表）
  const handleAddFolder = async () => {
    const res = await window.audioAPI.selectScanFolders()
    if (res.success) {
      setScanDirs(res.dirs || [])
    }
  }

  // 删除目录
  const handleRemoveDir = async (p) => {
    const res = await window.audioAPI.removeScanDir?.(p)
    if (res?.success) setScanDirs(res.dirs || [])
  }

  // 确认立即扫描目录
  const handleConfirmScan = async () => {
    try {
      setScanning(true)
      const { success, songs, added, skipped } = await window.audioAPI.scanAudioInFolders()
      if (skipped) {
        message.info(`已跳过 ${skipped} 首已存在的歌曲`)
      }
      if (success) {
        if (added) {
          console.log(songs)
          message.success(`已添加 ${added} 首歌曲`)
        }
      } else {
        message.error('扫描失败')
      }
    } catch (err) {
      message.error(err.message || '扫描失败')
    } finally {
      setScanning(false)
      setAutoScanOpen(false)
      // 统一刷新一次
      onRefresh && onRefresh()
    }
  }

  // 立即扫描歌曲
  const handleImmediateScan = async () => {
    await handleConfirmScan()
  }

  // 渲染顶部操作栏
  function renderTopBar() {
    const handleSearch = () => {
      setShowSearchBox(!showSearchBox)
      // 如果打开搜索框，聚焦到输入框
      if (!showSearchBox) {
        setTimeout(() => {
          searchInputRef.current?.focus()
        }, 300) // 等待动画完成
      }
    }

    const handleCloseSearch = (e) => {
      e.stopPropagation() // 阻止事件冒泡
      setShowSearchBox(false)
      setSearchValue('')
    }

    const handleSearchChange = (e) => {
      setSearchValue(e.target.value)
    }

    const handleSearchKeyPress = (e) => {
      if (e.key === 'Enter') {
        // 执行搜索
        console.log('执行搜索:', searchValue)
      } else if (e.key === 'Escape') {
        // 关闭搜索框
        setShowSearchBox(false)
        setSearchValue('')
      }
    }

    const handleSearchBlur = (e) => {
      // 检查失去焦点的目标是否是搜索框本身或其子元素
      const searchContainer = e.currentTarget.closest('.search-container')
      const relatedTarget = e.relatedTarget

      // 如果失去焦点的目标不是搜索框相关的元素，且搜索值为空，则关闭搜索框
      if (!searchContainer?.contains(relatedTarget) && !searchValue.trim()) {
        // 添加延迟，避免与点击事件冲突
        setTimeout(() => {
          setShowSearchBox(false)
        }, 100)
      }
    }

    return (
      <div className="playlist-toolbar">
        <Button className="play-all-button" variant="solid" icon={<CaretRightOutlined/>}
                autoInsertSpace={false} onClick={handlePlayAll}>
          播放全部
        </Button>
        {
          isLocalPlay
            ?
            <div className="control-bar">
              <div className="search-container" onBlur={handleSearchBlur}>
                <div className={`search-box ${showSearchBox ? 'show' : ''}`}>
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder="搜索歌曲..."
                    value={searchValue}
                    onChange={handleSearchChange}
                    onKeyDown={handleSearchKeyPress}
                    className="search-input"
                  />
                </div>
                <div className="control-item">
                  {searchValue ? (
                    <CloseOutlined
                      className="control-icon"
                      onClick={handleCloseSearch}
                    />
                  ) : (
                    <SearchOutlined
                      className="control-icon"
                      onClick={handleSearch}
                    />
                  )}
                </div>
              </div>
              <div className="control-item">
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'manual',
                        label: '手动添加歌曲',
                        onClick: handleManualAdd
                      },
                      {
                        key: 'auto',
                        label: '自动扫描歌曲',
                        onClick: handleAutoScan
                      },
                      {
                        key: 'immediate',
                        label: '立即扫描歌曲',
                        onClick: handleImmediateScan
                      }
                    ]
                  }}
                  open={addMenuVisible}
                  onOpenChange={setAddMenuVisible}
                  trigger={['click']}
                  placement="bottom"
                >
                  <PlusOutlined className="control-icon"/>
                </Dropdown>
              </div>
              <div className={`control-item`}>
                <div className="sort-container">
                  <Popover
                    content={
                      <div className="sort-menu">
                        <div className="sort-section">
                          <div className="sort-title">排序方式</div>
                          <div className="sort-options">
                            <div
                              className={`sort-option ${sortField === 'default' ? 'active' : ''}`}
                              onClick={() => resetSort()}
                            >
                              <span>默认顺序</span>
                              {sortField === 'default' && <CheckOutlined/>}
                            </div>
                            <div
                              className={`sort-option ${sortField === 'created' ? 'active' : ''}`}
                              onClick={() => handleSort('created', sortField === 'created' && sortOrder === 'asc' ? 'desc' : 'asc')}
                            >
                              <span>创建时间</span>
                              {sortField === 'created' && (
                                <span className="sort-indicator">{sortOrder === 'asc' ? '↑' : '↓'}</span>)}
                            </div>
                            <div
                              className={`sort-option ${sortField === 'name' ? 'active' : ''}`}
                              onClick={() => handleSort('name', sortField === 'name' && sortOrder === 'asc' ? 'desc' : 'asc')}
                            >
                              <span>歌曲名称</span>{sortField === 'name' && (
                              <span className="sort-indicator">{sortOrder === 'asc' ? '↑' : '↓'}</span>)}
                            </div>
                            <div
                              className={`sort-option ${sortField === 'artist' ? 'active' : ''}`}
                              onClick={() => handleSort('artist', sortField === 'artist' && sortOrder === 'asc' ? 'desc' : 'asc')}
                            >
                              <span>歌手</span>
                              {sortField === 'artist' && (
                                <span className="sort-indicator">{sortOrder === 'asc' ? '↑' : '↓'}</span>)}
                            </div>
                            <div
                              className={`sort-option ${sortField === 'album' ? 'active' : ''}`}
                              onClick={() => handleSort('album', sortField === 'album' && sortOrder === 'asc' ? 'desc' : 'asc')}
                            >
                              <span>专辑</span>
                              {sortField === 'album' && (
                                <span className="sort-indicator">{sortOrder === 'asc' ? '↑' : '↓'}</span>)}
                            </div>
                            <div
                              className={`sort-option ${sortField === 'duration' ? 'active' : ''}`}
                              onClick={() => handleSort('duration', sortField === 'duration' && sortOrder === 'asc' ? 'desc' : 'asc')}
                            >
                              <span>时长</span>
                              {sortField === 'duration' && (
                                <span className="sort-indicator">{sortOrder === 'asc' ? '↑' : '↓'}</span>)}
                            </div>
                          </div>
                        </div>
                      </div>
                    }
                    trigger="click"
                    placement="bottom"
                    arrow={false}
                    overlayClassName="sort-popover"
                  >
                    <SwapOutlined
                      rotate={90}
                      className={`control-icon ${sortField !== 'default' ? 'active' : ''}`}
                    />
                  </Popover>
                </div>
              </div>
              <div className="control-item">
                <div className="list-container">
                  <UnorderedListOutlined className="control-icon" onClick={() => setCheckedShow(true)}/>
                </div>
              </div>
            </div>
            :
            <Button className="batch-button" icon={<BarsOutlined/>}
                    autoInsertSpace={false}
                    style={{ marginLeft: 8 }}
                    onClick={() => setCheckedShow(true)}
            >
              批量操作
            </Button>
        }
      </div>
    )
  }

  // 处理播放：点击单曲时，以“过滤后列表”为播放列表，从当前项开始顺序播放
  const handlePlayItem = (item) => {
    // 在线播放：只播放当前单曲，不把全部结果塞进播放列表
    if (onPlay) {
      onPlay(item)
      return
    }
    // 选择播放：仍按当前列表从该位置开始顺序播放
    if (onPlaySelected) {
      const currentIndex = filteredData.findIndex(song => song.MUSICRID === item.MUSICRID)
      const reorderedList = currentIndex >= 0
        ? [...filteredData.slice(currentIndex), ...filteredData.slice(0, currentIndex)]
        : filteredData
      // 仅触发一次播放：不在此处做封面补全，避免对每一首都发请求
      onPlaySelected(reorderedList)
    }
  }

  // 滚动到当前播放歌曲
  const handleScrollToCurrent = () => {
    try {
      const container = scrollRef.current || document.getElementById('music-scrollableDiv')
      if (!container) {
        message?.warning?.('无法找到滚动容器')
        return
      }

      const target = container.querySelector('.list-item.selected')
      if (!target) {
        message?.warning?.('当前歌曲不在列表中')
        return
      }

      // 使用更稳定的滚动方式，避免影响页面布局
      const containerRect = container.getBoundingClientRect()
      const targetRect = target.getBoundingClientRect()

      // 计算目标元素相对于容器的位置
      const containerScrollTop = container.scrollTop
      const targetOffsetTop = targetRect.top - containerRect.top + containerScrollTop

      // 计算滚动位置，使目标元素居中
      const containerHeight = container.clientHeight
      const targetHeight = targetRect.height
      const scrollTop = targetOffsetTop - (containerHeight / 2) + (targetHeight / 2) + 73

      // 平滑滚动到目标位置
      container.scrollTo({
        top: Math.max(0, scrollTop),
        behavior: 'smooth'
      })

    } catch (error) {
      console.error('滚动定位失败:', error)
      message?.error?.('定位失败')
    }
  }

  return (
    <>
      {contextHolder}
      <div
        ref={scrollRef}
        className={`music-scrollableDiv ${className}`}
        style={{
          height: 'calc(100vh - 220px)',
          overflow: 'auto',
          display: 'block',
          position: 'relative'
        }}
        id="music-scrollableDiv"
      >
        {isShowToolbar && (checkedShow ? renderBatchBar() : renderTopBar())}

        {/* 显示搜索结果数量提示 */}
        {searchValue && (
          <div style={{
            padding: '8px 16px',
            color: 'var(--control-text-color, #ffffff)',
            fontSize: '14px',
            backgroundColor: 'var(--control-bar-bg, #2a2a2a)',
            marginBottom: '8px',
            borderRadius: '4px'
          }}>
            找到 {filteredData.length} 首歌曲
          </div>
        )}

        {filteredData.length > 0 ? (
          <InfiniteScroll
            dataLength={data.length}
            next={checkedShow ? undefined : loadMoreData}
            hasMore={checkedShow ? false : hasMore}
            loader={!checkedShow ? <Skeleton avatar paragraph={{ rows: 1 }} active/> : ''}
            endMessage={filteredData.length >= isShowEndMessage && !checkedShow && <Divider plain>到底了...</Divider>}
            scrollableTarget="music-scrollableDiv"
            style={{ overflow: 'visible' }}
          >
            <ConfigProvider
              theme={{
                token: token,
                components: {
                  List: {
                    avatarMarginRight: '8px'
                  },
                },
              }}
            >
              <List
                dataSource={filteredData}
                renderItem={(item, idx) => (
                  <Dropdown
                    trigger={checkedShow ? [] : ['contextMenu']}
                    menu={{
                      items: [
                        { key: 1, label: '播放', },
                        { key: 2, label: '添加到歌单' },
                        { key: 3, label: '下载' },
                      ],
                      onClick: ({ key }) => {
                        if (key === '1' && onPlay) {
                          handlePlayItem(item)
                        }
                        if (key === '2') {
                          console.log(item)
                        }
                        if (key === '3') {
                          handleDownload(item)
                        }
                      }
                    }}
                  >
                    <List.Item
                      style={{ padding: '11px 14px' }}
                      onClick={e => itemCheckClick(e, item)}
                      className={`list-item${currentMusicId === item.MUSICRID ? ' selected' : ''}`}
                      key={idx}
                      extra={
                        !checkedShow && (
                          <div className="action">
                            <HeartOutlined className="heart-icon"/>
                            <Dropdown
                              trigger={['click', 'contextMenu']}
                              menu={{
                                items: [
                                  { key: 1, label: '播放', },
                                  { key: 2, label: '添加到歌单' },
                                  { key: 3, label: '下载' },
                                ],
                                onClick: ({ key }) => {
                                  if (key === '1' && onPlay) {
                                    handlePlayItem(item)
                                  }
                                  if (key === '2') {
                                    console.log(item)
                                  }
                                  if (key === '3') {
                                    handleDownload(item)
                                  }
                                }
                              }}
                            >
                              <MoreOutlined className="more-icon"/>
                            </Dropdown>
                          </div>
                        )
                      }
                      onDoubleClick={() => {
                        if (!checkedShow) handlePlayItem(item)
                      }}
                    >
                      {
                        !checkedShow
                          ? <span className="list-idx">{(idx + 1).toString().padStart(2, '0')}</span>
                          : <Checkbox checked={checkedList.includes(item.MUSICRID)}
                                      onChange={() => onCheckChange(item)}
                                      onClick={e => e.stopPropagation()}/>
                      }
                      <List.Item.Meta
                        className="list-item-meta"
                        avatar={
                          <Avatar className="list-avatar"
                                  size={50} shape="square"
                                  alt={item.ARTIST}
                                  key={item.MUSICRID}
                                  src={(function() {
                                    try {
                                      const cache = window._coverCache || {}
                                      if (item.hash && cache[item.hash]) return `file://${cache[item.hash]}`
                                      if (item.MUSICRID && cache[item.MUSICRID]) return `file://${cache[item.MUSICRID]}`
                                      if (item.localCover) return `file://${item.localCover}`
                                      if (item.cover) return `${LOCALHOST}/proxy?type=avatar&avatarUrl=${item.cover}`
                                    } catch (e) {}
                                    return avatar
                                  })()}
                          />
                        }
                        title={<span>{item.NAME}</span>}
                        description={<span>{item.ARTIST}</span>}
                      />
                      <div className="album">{item.ALBUM}</div>
                      <div className="duration">{formatDuration(item.DURATION)}</div>
                    </List.Item>
                  </Dropdown>
                )}
              />
            </ConfigProvider>
          </InfiniteScroll>
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE}/>
        )}
      </div>

      {/* 悬浮定位按钮：定位当前播放的歌曲到视窗中部 */}
      {currentMusicId && filteredData.length > 0 && (
        <Button
          type="text"
          shape="circle"
          icon={<AimOutlined/>}
          onClick={handleScrollToCurrent}
          style={{
            position: 'fixed',
            right: 12,
            bottom: 140,
            zIndex: 1000,
            backgroundColor: 'transparent',
            border: 'none',
            color: token?.colorPrimary || '#1677ff',
            fontSize: '18px',
            width: '40px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'all 0.3s ease',
            opacity: 0.7,
          }}
          onMouseEnter={(e) => {
            e.target.style.opacity = '1'
            e.target.style.transform = 'scale(1.1)'
          }}
          onMouseLeave={(e) => {
            e.target.style.opacity = '0.7'
            e.target.style.transform = 'scale(1)'
          }}
          title="定位到当前播放"
        />
      )}

      <Modal
        title={<div style={{ fontSize: 20, marginBottom: 15 }}>下载</div>}
        open={isModalOpen}
        footer={null}
        onCancel={() => {
          setIsModalOpen(false)
          // 如果是右键下载，清空选中状态
          if (isRightClickDownload) {
            setCheckedList([])
            setIsRightClickDownload(false)
          }
        }}
        width={600}
        style={{ top: 50 }}
      >
        <div style={{ padding: '0 16px' }}>
          {/* 音质选择 */}
          <div style={{ marginBottom: 20, display: 'flex' }}>
            <div style={{ margin: '0 12px 12px 0', fontWeight: 500 }}>音质选择:</div>
            <div>
              <Radio.Group
                value={downloadQuality}
                onChange={(e) => setDownloadQuality(e.target.value)}
                style={{ display: 'flex', gap: 16 }}
              >
                <Radio value="128kmp3">标准音质</Radio>
                <Radio value="192kmp3">高品音质</Radio>
                <Radio value="320kmp3">顶级音质</Radio>
                <Radio value="2000kflac">无损音质</Radio>
              </Radio.Group>
            </div>
          </div>

          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div style={{ width: '33.33%' }}>
              <Checkbox
                checked={downloadListData.length > 0 && downloadListData.every(item => selectedSongs.includes(item.MUSICRID))}
                indeterminate={selectedSongs.length > 0 && !downloadListData.every(item => selectedSongs.includes(item.MUSICRID))}
                onChange={handleDownloadCheckAll}
              >
                已选{selectedSongs.length}首
              </Checkbox>
            </div>
            <div style={{ width: '33.33%', textAlign: 'center' }}>音质</div>
            <div style={{ width: '33.33%', textAlign: 'right' }}>
              大小共({totalSize > 0 ? formatFileSize(totalSize) : '--'})
            </div>
          </div>
          <div style={{ height: 1, background: '#ccc', margin: '10px 0' }}/>

          <div className="download-scrollableDiv" style={{ height: 300, overflow: 'auto' }}>
            <List
              dataSource={downloadListData}
              renderItem={item => (
                <List.Item
                  key={item.MUSICRID}
                  onClick={() => handleDownloadItemClick(item)}
                  style={{
                    cursor: 'pointer',
                    borderRadius: '6px',
                    transition: 'background-color 0.2s ease',
                    marginBottom: '4px'
                  }}
                  className="download-list-item"
                >
                  <Checkbox
                    checked={selectedSongs.includes(item.MUSICRID)}
                    onChange={() => handleDownloadItemClick(item)}
                    onClick={e => e.stopPropagation()}
                  />
                  <List.Item.Meta
                    style={{ flex: 1, minWidth: 0 }}
                    description={
                      <div style={{
                        paddingLeft: 10,
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: '250px'
                      }}>
                        {item.ARTIST + ' - ' + item.NAME}
                      </div>
                    }
                  />
                  <div style={{ width: '30%', whiteSpace: 'nowrap', color: '#666', textAlign: 'center' }}>
                    {(() => {
                      const options = parseMINFO(item.MINFO)

                      // 根据选择的音质查找对应的比特率范围
                      const getBitrateRange = (quality) => {
                        switch (quality) {
                          case '128kmp3':
                            return { min: 128, max: 191 }
                          case '192kmp3':
                            return { min: 192, max: 319 }
                          case '320kmp3':
                            return { min: 320, max: 1999 }
                          case '2000kflac':
                            return { min: 2000, max: Infinity }
                          default:
                            return { min: 128, max: 191 }
                        }
                      }

                      const bitrateRange = getBitrateRange(downloadQuality)

                      // 找到匹配比特率范围的音质选项
                      let matchedOption = options.find(option => {
                        const bitrate = parseInt(option.bitrate)
                        return bitrate >= bitrateRange.min && bitrate <= bitrateRange.max
                      })

                      // 如果没有找到匹配的音质，选择比选择音质低的最高音质
                      if (!matchedOption && options.length > 0) {
                        const targetBitrate = bitrateRange.min
                        const lowerBitrateOptions = options.filter(option =>
                          parseInt(option.bitrate) < targetBitrate
                        )

                        if (lowerBitrateOptions.length > 0) {
                          // 选择比目标比特率低的最高比特率
                          const sortedLowerOptions = lowerBitrateOptions.sort((a, b) =>
                            parseInt(b.bitrate) - parseInt(a.bitrate)
                          )
                          matchedOption = sortedLowerOptions[0]
                        } else {
                          // 如果没有更低的比特率，才选择最高比特率
                          const sortedOptions = options.sort((a, b) =>
                            parseInt(b.bitrate) - parseInt(a.bitrate)
                          )
                          matchedOption = sortedOptions[0]
                        }
                      }

                      return matchedOption ? matchedOption.quality :
                        (downloadQuality === '128kmp3' ? '标准音质' :
                          downloadQuality === '192kmp3' ? '高品音质' :
                            downloadQuality === '320kmp3' ? '顶级音质' :
                              downloadQuality === '2000kflac' ? '无损音质' : downloadQuality)
                    })()}
                  </div>
                  <div style={{
                    width: '32%',
                    whiteSpace: 'nowrap',
                    color: '#666',
                    textAlign: 'right',
                    paddingRight: 5
                  }}>
                    {(() => {
                      const realSize = getFileSizeByQuality(item.MINFO, downloadQuality)
                      return realSize || '--'
                    })()}
                  </div>
                </List.Item>
              )}
            />
          </div>

          {/* 下载路径 */}
          <div style={{ marginBottom: 24 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-end' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div>下载到:</div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{
                    padding: '8px 12px',
                    maxWidth: 250,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}
                       title={downloadPath}
                  >
                    {downloadPath}
                  </div>
                  <Button size="small" onClick={handleChangeDir}>更改目录</Button>
                </div>
              </div>
              {/* 操作按钮 */}
              <div>
                <Button
                  type="primary"
                  onClick={startDownload}
                  loading={isDownloading}
                  disabled={isDownloading || selectedSongs.length === 0}
                >
                  立即下载
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Modal>
      {/*{扫描歌曲相关}*/}
      <Modal
        open={autoScanOpen}
        title="扫描目录添加"
        onCancel={() => setAutoScanOpen(false)}
        footer={[
          <Button key="add" icon={<PlusOutlined/>} onClick={handleAddFolder}>
            添加文件夹
          </Button>,
          <Button key="ok" type="primary" loading={scanning} onClick={handleConfirmScan}>
            立即扫描
          </Button>
        ]}
      >
        <div style={{ height: 260, overflow: 'auto' }} className="scan-list-scroll">
          <List
            dataSource={scanDirs}
            locale={{ emptyText: '暂无目录，请点击"添加文件夹"' }}
            renderItem={(dir) => (
              <List.Item
                style={{ padding: '6px 4px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                actions={[
                  <Button key="del" type="link" size="small" danger onClick={() => handleRemoveDir(dir.path)}>
                    删除
                  </Button>
                ]}
              >
                <div>
                  <FolderOutlined style={{ marginRight: 8 }}/>
                  {dir.path || dir.name}
                </div>
              </List.Item>
            )}
          />
        </div>
      </Modal>
    </>
  )
}

export default MusicList