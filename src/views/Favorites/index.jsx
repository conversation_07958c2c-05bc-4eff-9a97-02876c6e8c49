import React, { useState, useEffect } from 'react'
import { Empty, Button, Input, Select, Space, Typography, Card, Statistic } from 'antd'
import { HeartOutlined, SearchOutlined, DeleteOutlined, PlayCircleOutlined } from '@ant-design/icons'
import MusicList from '../MusicList'
import { useFavorites } from '@/hooks/useFavorites'
import './index.less'

const { Search } = Input
const { Option } = Select
const { Title } = Typography

function Favorites() {
  const [searchKeyword, setSearchKeyword] = useState('')
  const [sortBy, setSortBy] = useState('created_at') // created_at, name, artist
  const [filterBy, setFilterBy] = useState('all') // all, online, local
  
  const {
    favorites,
    loading,
    loadFavorites,
    removeFavorite,
    searchFavorites,
    getFavoriteStats
  } = useFavorites()

  // 过滤和搜索后的收藏列表
  const [filteredFavorites, setFilteredFavorites] = useState([])

  // 统计信息
  const stats = getFavoriteStats()

  // 处理搜索
  const handleSearch = (value) => {
    setSearchKeyword(value)
  }

  // 处理排序
  const handleSortChange = (value) => {
    setSortBy(value)
  }

  // 处理筛选
  const handleFilterChange = (value) => {
    setFilterBy(value)
  }

  // 播放收藏的歌曲
  const handlePlayFavorite = async (item) => {
    // 转换收藏数据格式为播放器需要的格式
    const songInfo = {
      MUSICRID: item.musicrid,
      NAME: item.name,
      SONGNAME: item.name,
      ARTIST: item.artist,
      ALBUM: item.album,
      DURATION: item.duration,
      cover: item.cover,
      source: item.source
    }

    // 设置播放列表为当前过滤后的收藏列表
    const playList = filteredFavorites.map(fav => ({
      MUSICRID: fav.musicrid,
      NAME: fav.name,
      SONGNAME: fav.name,
      ARTIST: fav.artist,
      ALBUM: fav.album,
      DURATION: fav.duration,
      cover: fav.cover,
      source: fav.source
    }))

    // 调用播放API
    if (window.audioAPI?.play) {
      // 设置播放列表
      if (window.audioAPI.setPlayList) {
        window.audioAPI.setPlayList(playList)
      }

      if (item.source === 'local') {
        // 本地文件播放
        window.audioAPI.play(`file://${item.path}`, 0, songInfo, item.musicrid)
      } else {
        // 在线播放需要先获取播放链接
        try {
          // 这里需要根据您的API获取播放链接
          console.log('播放在线收藏歌曲:', songInfo)
          // 可以调用现有的获取播放链接逻辑
        } catch (error) {
          console.error('播放收藏歌曲失败:', error)
        }
      }

      // 设置当前播放歌曲ID
      if (window.audioAPI.setCurrentMusicId) {
        window.audioAPI.setCurrentMusicId(item.musicrid)
      }
    }
  }

  // 播放所有收藏
  const handlePlayAll = () => {
    if (filteredFavorites.length === 0) return

    const playList = filteredFavorites.map(fav => ({
      MUSICRID: fav.musicrid,
      NAME: fav.name,
      SONGNAME: fav.name,
      ARTIST: fav.artist,
      ALBUM: fav.album,
      DURATION: fav.duration,
      cover: fav.cover,
      source: fav.source
    }))

    // 设置播放列表
    if (window.audioAPI?.setPlayList) {
      window.audioAPI.setPlayList(playList)
    }

    // 播放第一首
    handlePlayFavorite(filteredFavorites[0])
  }

  // 清空所有收藏
  const handleClearAll = async () => {
    if (favorites.length === 0) return
    
    // 这里可以添加确认对话框
    const confirmed = window.confirm(`确定要清空所有 ${favorites.length} 首收藏歌曲吗？`)
    if (!confirmed) return

    // 批量删除所有收藏
    for (const item of favorites) {
      await removeFavorite(item.musicrid)
    }
  }

  // 更新过滤后的列表
  useEffect(() => {
    let result = favorites

    // 搜索过滤
    if (searchKeyword) {
      result = searchFavorites(searchKeyword)
    }

    // 来源过滤
    if (filterBy !== 'all') {
      result = result.filter(item => item.source === filterBy)
    }

    // 排序
    result = [...result].sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return (a.name || '').localeCompare(b.name || '')
        case 'artist':
          return (a.artist || '').localeCompare(b.artist || '')
        case 'created_at':
        default:
          return b.created_at - a.created_at // 最新的在前
      }
    })

    setFilteredFavorites(result)
  }, [favorites, searchKeyword, sortBy, filterBy, searchFavorites])

  // 转换为MusicList组件需要的数据格式
  const musicListData = filteredFavorites.map(item => ({
    MUSICRID: item.musicrid,
    NAME: item.name,
    SONGNAME: item.name,
    ARTIST: item.artist,
    ALBUM: item.album,
    DURATION: item.duration,
    cover: item.cover,
    source: item.source
  }))

  return (
    <div className="favorites-container">
      {/* 头部统计和操作区 */}
      <div className="favorites-header">
        <div className="favorites-title">
          <HeartOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
          <Title level={3} style={{ margin: 0 }}>我的收藏</Title>
        </div>
        
        <div className="favorites-stats">
          <Space size="large">
            <Statistic title="总计" value={stats.total} />
            <Statistic title="在线" value={stats.online} />
            <Statistic title="本地" value={stats.local} />
          </Space>
        </div>
      </div>

      {/* 操作栏 */}
      <div className="favorites-toolbar">
        <Space size="middle">
          <Search
            placeholder="搜索收藏的歌曲..."
            allowClear
            onSearch={handleSearch}
            onChange={(e) => !e.target.value && handleSearch('')}
            style={{ width: 300 }}
            prefix={<SearchOutlined />}
          />
          
          <Select
            value={sortBy}
            onChange={handleSortChange}
            style={{ width: 120 }}
          >
            <Option value="created_at">收藏时间</Option>
            <Option value="name">歌曲名称</Option>
            <Option value="artist">艺术家</Option>
          </Select>
          
          <Select
            value={filterBy}
            onChange={handleFilterChange}
            style={{ width: 100 }}
          >
            <Option value="all">全部</Option>
            <Option value="online">在线</Option>
            <Option value="local">本地</Option>
          </Select>
        </Space>

        <Space>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={handlePlayAll}
            disabled={filteredFavorites.length === 0}
          >
            播放全部
          </Button>
          
          <Button
            danger
            icon={<DeleteOutlined />}
            onClick={handleClearAll}
            disabled={favorites.length === 0}
          >
            清空收藏
          </Button>
        </Space>
      </div>

      {/* 收藏列表 */}
      <div className="favorites-content">
        {filteredFavorites.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              favorites.length === 0 
                ? "还没有收藏任何歌曲" 
                : "没有找到匹配的收藏歌曲"
            }
          >
            {favorites.length === 0 && (
              <Button type="primary" onClick={() => window.history.back()}>
                去发现音乐
              </Button>
            )}
          </Empty>
        ) : (
          <MusicList
            data={musicListData}
            onPlay={handlePlayFavorite}
            onPlaySelected={(songs) => {
              if (songs.length > 0) {
                // 设置播放列表
                if (window.audioAPI?.setPlayList) {
                  window.audioAPI.setPlayList(songs)
                }
                handlePlayFavorite(songs[0])
              }
            }}
            loading={loading}
            hasMore={false}
            isShowToolbar={false}
            className="favorites-music-list"
          />
        )}
      </div>
    </div>
  )
}

export default Favorites
