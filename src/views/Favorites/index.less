.favorites-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-color, #f5f5f5);

  .favorites-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: var(--card-bg, #fff);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .favorites-title {
      display: flex;
      align-items: center;
      
      h3 {
        color: var(--text-color, #333);
      }
    }

    .favorites-stats {
      .ant-statistic {
        .ant-statistic-title {
          color: var(--text-color-secondary, #666);
          font-size: 12px;
        }
        
        .ant-statistic-content {
          color: var(--primary-color, #1890ff);
          font-weight: 600;
        }
      }
    }
  }

  .favorites-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 20px;
    background: var(--card-bg, #fff);
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .ant-input-search {
      .ant-input {
        background: var(--input-bg, #fafafa);
        border-color: var(--border-color, #d9d9d9);
        
        &:focus {
          border-color: var(--primary-color, #1890ff);
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }

    .ant-select {
      .ant-select-selector {
        background: var(--input-bg, #fafafa);
        border-color: var(--border-color, #d9d9d9);
      }
    }

    .ant-btn {
      &.ant-btn-primary {
        background: var(--primary-color, #1890ff);
        border-color: var(--primary-color, #1890ff);
        
        &:hover {
          background: var(--primary-color-hover, #40a9ff);
          border-color: var(--primary-color-hover, #40a9ff);
        }
      }

      &.ant-btn-dangerous {
        &:hover {
          background: #ff7875;
          border-color: #ff7875;
        }
      }
    }
  }

  .favorites-content {
    flex: 1;
    background: var(--card-bg, #fff);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-empty {
      padding: 60px 20px;
      
      .ant-empty-description {
        color: var(--text-color-secondary, #666);
      }
    }

    .favorites-music-list {
      height: 100%;
      
      // 覆盖MusicList的样式
      .music-list-container {
        border: none;
        border-radius: 0;
        box-shadow: none;
      }

      // 收藏列表特殊样式
      .list-item {
        transition: all 0.2s ease;
        
        &:hover {
          background: var(--hover-bg, #f0f0f0);
          
          .heart-icon {
            opacity: 1;
            transform: scale(1.1);
          }
        }

        .heart-icon {
          opacity: 0.6;
          transition: all 0.2s ease;
          cursor: pointer;
          font-size: 16px;
          
          &:hover {
            opacity: 1;
            transform: scale(1.2);
          }
          
          &.favorited {
            opacity: 1;
            color: #ff4d4f !important;
          }
        }

        // 收藏时间显示
        .favorite-time {
          font-size: 12px;
          color: var(--text-color-secondary, #999);
          margin-left: 8px;
        }
      }
    }
  }
}

// 暗色主题适配
[data-theme='dark'] {
  .favorites-container {
    background: #141414;

    .favorites-header,
    .favorites-toolbar,
    .favorites-content {
      background: #1f1f1f;
      border-color: #303030;
    }

    .favorites-header {
      .favorites-title h3 {
        color: #fff;
      }
    }

    .favorites-toolbar {
      .ant-input-search .ant-input {
        background: #262626;
        border-color: #434343;
        color: #fff;
        
        &::placeholder {
          color: #8c8c8c;
        }
      }

      .ant-select .ant-select-selector {
        background: #262626;
        border-color: #434343;
        color: #fff;
      }
    }

    .favorites-content {
      .favorites-music-list {
        .list-item {
          &:hover {
            background: #262626;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .favorites-container {
    padding: 10px;

    .favorites-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }

    .favorites-toolbar {
      flex-direction: column;
      gap: 12px;
      
      > * {
        width: 100%;
      }
    }
  }
}

// 动画效果
@keyframes heartBeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.heart-icon.favorited {
  animation: heartBeat 0.3s ease-in-out;
}
