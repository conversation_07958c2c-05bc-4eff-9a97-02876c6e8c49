.play-all-button, .download-all-button, .batch-button {
  gap: 2px;
  padding: 0 8px;
  margin-right: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.list-checked {
  margin: 0 2px;
  min-width: 60px;
  display: inline-block;
}

.music-scrollableDiv {
  background-color: var(--playlist-bg, var(--search-bg, #fff));

  .list-item {
    transition: background 0.3s;
    display: flex;
    justify-content: flex-end;
    background: var(--playlist-bg, var(--search-bg, #fff));
    &.selected {
      background: var(--playlist-selected-bg, var(--search-selected-bg, #e6f7ff)) !important;
    }

    .duration, .album {
      flex: 1;
      color: var(--playlist-text-desc, var(--search-meta-color, #000));
    }

    .list-item-meta {
      flex: 2;
      margin-top: 0;
      padding-left: 5px;
      color: var(--playlist-text-desc, var(--search-meta-color, #000));

      .list-avatar {
        margin-inline-end: 0;
      }
      span {
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 180px;
      }
    }

    .action {
      flex: 0.5;
    }

    .album, .duration {
      text-align: center;
    }

    .action {
      text-align: right;
    }

    .action {
      .more-icon, .heart-icon {
        padding-left: 10px;
        font-size: 18px;
        cursor: pointer;
        color: var(--playlist-text, #999);
        transition: color 0.3s;

        &:hover {
          color: var(--playlist-text, #333);
        }
      }
    }
  }

  .list-item:hover {
    background: rgba(140, 140, 140, 0.1);
  }

  .list-idx {
    font-size: 15px;
    color: var(--playlist-text-desc, var(--search-idx-color, rgba(0, 0, 0, 0.39)));
  }
}

.playlist-toolbar {
  position: sticky;
  top: 0;
  z-index: 2;
  background: var(--playlist-bg, #fff);
  padding-top: 8px;
  padding-bottom: 8px;
}

/* 滚动容器样式（假设容器类名为 .music-scrollableDiv） */
.music-scrollableDiv {
  /* ========== 基础设置 ========== */
  overflow: auto; /* 确保滚动条出现 */

  /* ========== WebKit 内核浏览器（Chrome/Edge/Safari） ========== */

  &::-webkit-scrollbar {
    width: 8px; /* 纵向滚动条宽度 */
    height: 8px; /* 横向滚动条高度（如需支持横向滚动） */
  }

  /* 滚动条轨道（背景） */

  &::-webkit-scrollbar-track {
    background-color: var(--search-scrollbar-track, #f5f5f5);
    border-radius: 4px;
  }

  /* 滚动条滑块（拖动部分） */

  &::-webkit-scrollbar-thumb {
    background-color: var(--search-scrollbar-thumb, #ccc);
    border-radius: 4px;

    &:hover {
      background-color: var(--search-scrollbar-thumb-hover, #999); /* 鼠标悬浮效果 */
    }
  }
}
