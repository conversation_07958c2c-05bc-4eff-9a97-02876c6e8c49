import React, { useState, useEffect, useCallback, useRef } from 'react'
import { Tabs, message, theme } from 'antd'
import axios from 'axios'

import './index.less'
import { LOCALHOST, getAvatarUrl } from '@/utils/constants'
import useSearchStore from '@/store/searchStore'
import MusicList from '@/views/MusicList'

const { useToken } = theme

function SearchDetail() {
  const { token } = useToken()

  const searchTerm = useSearchStore((state) => state.searchTerm)
  const lastSearchTerm = useSearchStore((state) => state.lastSearchTerm)
  const setLastSearchTerm = useSearchStore((state) => state.setLastSearchTerm)
  const searchData = useSearchStore((state) => state.searchData)
  const setSearchData = useSearchStore((state) => state.setSearchData)
  const searchPage = useSearchStore((state) => state.searchPage)
  const setSearchPage = useSearchStore((state) => state.setSearchPage)
  const [tabKey, setTabKey] = useState(0)
  const [loading, setLoading] = useState(false)
  const [currentMusicId, setCurrentMusicId] = useState(null)

  const onChange = key => {
    setTabKey(key)
  }

  const playMusic = item => {
    // 重复点击当前播放歌曲
    if (item.MUSICRID === currentMusicId) {
      // 直接调到歌曲开头
      window.audioAPI.seek(0)
      return
    }
    // 1. 同步播放列表和当前歌曲ID到主进程
    const list = searchData.map(item => {
      return {
        NAME: item.NAME || item.SONGNAME,
        ARTIST: item.ARTIST,
        ALBUM: item.ALBUM,
        cover: getAvatarUrl(item),
        duration: item.DURATION || 0,
        MUSICRID: item.MUSICRID
      }
    })
    window.audioAPI.setPlayList(list)
    window.audioAPI.setCurrentMusicId(item.MUSICRID)
    const musicId = item.MUSICRID
    axios.get(LOCALHOST + '/proxy', {
      params: {
        type: 'musicId',
        musicId
      }
    }).then(res => {
        if (res.data.code === 200) {
          const { url, duration } = res.data.data
          // 2. play时传递musicId参数
          const songInfo = {
            NAME: item.NAME || item.SONGNAME,
            ARTIST: item.ARTIST,
            ALBUM: item.ALBUM || '',
            cover: getAvatarUrl(item),
            duration: duration || 0,
            MUSICRID: item.MUSICRID
          }
          window.audioAPI.play(url, 0, songInfo, musicId)
        }
      }
    ).catch(err => {
      message.error('获取音乐链接失败，请稍后再试')
    })
  }

  // 存储最新的 searchTerm
  const searchTermRef = useRef(searchTerm)

  // 存储最新的 page
  const pageRef = useRef(searchPage)

  // 每次 searchTerm 变化时更新 ref
  useEffect(() => {
    searchTermRef.current = searchTerm
    console.log('[SearchTerm 更新]', searchTerm)
  }, [searchTerm])

  // 每次 page 变化时更新 ref
  useEffect(() => {
    pageRef.current = searchPage
  }, [searchPage])

  // 重构 fetchData 函数，直接使用最新的 searchTermRef
  const fetchData = useCallback(() => {
    return axios.get(LOCALHOST + '/proxy', {
      params: {
        type: 'searchDetail',
        name: searchTermRef.current, // 始终使用最新值
        page: pageRef.current, // 使用最新页码
      }
    })
  }, []) // 无依赖项，避免闭包问题

  // 重构 loadMoreData 函数
  const loadMoreData = useCallback(async () => {
    if (loading) return
    console.log('[LOAD MORE] 当前搜索词:', searchTermRef.current, '当前页码:', pageRef.current)
    setLoading(true)
    try {
      const response = await fetchData()
      if (response.data?.abslist) {
        const newData = [...searchData, ...response.data.abslist]
        setSearchData(newData)
        setSearchPage(searchPage + 1) // 更新页码状态
      }
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      setLoading(false)
    }
  }, [loading, searchData, searchPage])

  // 处理搜索词变化
  useEffect(() => {
    if (searchTerm && searchTerm !== lastSearchTerm) {
      // 新搜索，清空并请求
      setSearchPage(0)
      setSearchData([])
      setLastSearchTerm(searchTerm)
      // 请求第一页
      const loadInitialData = async () => {
        try {
          const response = await axios.get(LOCALHOST + '/proxy', {
            params: {
              type: 'searchDetail',
              name: searchTerm,
              page: 0,
            }
          })
          if (response.data?.abslist) {
            setSearchData(response.data.abslist)
            setSearchPage(1)
          }
        } catch (error) {
          console.error('加载初始数据失败:', error)
        }
      }
      loadInitialData()
    }
    // 如果 searchTerm 没变且 searchData 有内容，什么都不做，直接用
  }, [searchTerm, lastSearchTerm, setSearchData, setSearchPage, setLastSearchTerm])

  useEffect(() => {
    // 初始同步
    window.audioAPI.getState().then(state => {
      setCurrentMusicId(state.currentMusicId)
    })
    // 监听主进程歌曲变化
    const handler = (event, state) => {
      setCurrentMusicId(state.currentMusicId)
    }
    window.audioAPI.onStateUpdate(handler)
    return () => {
      // 组件卸载时移除监听
      window.audioAPI.removeStateUpdate(handler)
    }
  }, [])

  // 主题适配
  useEffect(() => {
    const root = document.documentElement
    root.style.setProperty('--search-meta-color', token.colorText)
    root.style.setProperty('--search-bg', token.colorBgContainer)
    root.style.setProperty('--search-selected-bg', token.colorPrimaryBg)
    root.style.setProperty('--search-idx-color', token.colorTextDescription)
    root.style.setProperty('--search-scrollbar-track', token.colorBgContainer)
    root.style.setProperty('--search-scrollbar-thumb', token.colorBorderSecondary)
    root.style.setProperty('--search-scrollbar-thumb-hover', token.colorPrimary)
    // 你可以继续设置其它 token 变量
  }, [token])

  return (
    <div>
      <div className="search-detail">
        <Tabs
          onChange={onChange}
          type="card"
          items={['单曲', '专辑', '歌手'].map((item, id) => {
            return {
              label: item,
              key: id
            }
          })}
        />
        {tabKey === 0 &&
          <>
            {/* 只保留播放列表区域，操作栏交由 PlayList 组件自带 */}
            <MusicList
              data={searchData} // 列表数据
              currentMusicId={currentMusicId} // 当前播放ID
              onPlay={item => {
                setCurrentMusicId(item.MUSICRID)
                playMusic(item)
              }} // 播放事件
              loadMoreData={loadMoreData} // 加载更多
              hasMore={searchData.length < 200 && !loading} // 是否还有更多
              loading={loading} // 加载状态
              token={token} // 主题token
            />
          </>
        }

        {tabKey === 1 && <div>专辑内容</div>}
        {tabKey === 2 && <div>歌手内容</div>}
      </div>
    </div>
  )
}

export default SearchDetail