import React, { useEffect } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import { Tabs } from 'antd'
import './index.less'

const items = [
  {
    key: 'rec',
    label: '推荐',
  },
  {
    key: 'bang',
    label: '排行榜',
  },
  {
    key: 'artist',
    label: '歌手',
  }
]

function Home() {
  const location = useLocation()
  const navigate = useNavigate()
  // 直接从路由获取当前 tab key
  const tabKey = (() => {
    const path = location.pathname.replace('/home/', '').split('/')[0]
    return ['rec', 'bang', 'artist'].includes(path) ? path : 'rec'
  })()

  const onChange = key => {
    if (tabKey !== key) {
      navigate(`/home/<USER>
    }
  }

  useEffect(() => {
    // 如果是 /home 或 /home/<USER>
    if (location.pathname === '/home' || location.pathname === '/home/') {
      const lastTab = sessionStorage.getItem('homeTab')
      navigate(`/home/<USER>'rec', 'bang', 'artist'].includes(lastTab) ? lastTab : 'rec'}`, { replace: true })
    }
  }, [location.pathname, navigate])

  useEffect(() => {
    sessionStorage.setItem('homeTab', tabKey)
  }, [tabKey])

  return (
    <div>
      <Tabs type="card" activeKey={tabKey} items={items} onChange={onChange}/>
      <Outlet/>
    </div>
  )
}

export default Home