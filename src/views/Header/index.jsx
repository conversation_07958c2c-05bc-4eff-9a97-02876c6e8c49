import React, { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { SearchOutlined, LeftOutlined, ReloadOutlined, SunOutlined, MoonOutlined } from '@ant-design/icons'
import { Input, AutoComplete, theme } from 'antd'
import axios from 'axios'
import './index.less'
import { LOCALHOST } from '@/views/Bang/initial'
import useSearchStore from '@/store/searchStore'

function MyHeader(props) {
  const [searchValue, setSearchValue] = useState('')
  const [show, setShow] = useState(false)
  const [options, setOptions] = useState([])
  const navigate = useNavigate()
  const autoCompleteRef = useRef(null)
  const setSearchTerm = useSearchStore((state) => state.setSearchTerm)
  const searchTerm = useSearchStore((state) => state.searchTerm)
  const { themeMode, setThemeMode } = props
  const { token } = theme.useToken()

  const extractRewords = arr => {
    return arr.map(str => {
      const match = str.match(/RELWORD=([^\r\n]*)/)
      return { value: match ? match[1] : '' }
    })
  }

  const searchRequest = async name => {
    const res = await axios.get(LOCALHOST + '/proxy', {
      params: {
        type: 'search',
        name
      }
    })
    if (res.status === 200) {
      const data = extractRewords(res.data.data)
      // 保证每一项 key 唯一
      setOptions(data.map((item, idx) => ({ ...item, key: item.value + '_' + idx })))
    }
  }

  const onChange = value => {
    setSearchValue(value)
    window.electronAPI.sendSearch(value)
  }

  const onSearchClick = () => {
    if (searchValue.trim()) {
      setShow(false)
      setSearchTerm(searchValue)
      navigate('/search')
      autoCompleteRef.current.blur()
    }
  }

  const onSearch = async data => {
    if (data.trim()) {
      await searchRequest(data)
      setShow(true)
    } else {
      setShow(false)
    }
  }

  const onPressEnter = () => {
    onSearchClick()
  }

  const onSelect = data => {
    navigate('/search')
    setSearchTerm(data)
    setShow(false)
    autoCompleteRef.current.blur()
  }

  useEffect(() => {
    window.electronAPI.searchInitial(data => {
      setSearchValue(data)
    })
  }, [])

  // 从全局store同步搜索值
  useEffect(() => {
    if (searchValue && searchTerm) {
      setSearchValue(searchTerm)
    }
  }, [searchTerm])

  return (
    <div className="header">
      <div className="logo">img</div>
      <div className="top-right">
        <div className="search">
          <LeftOutlined
            style={{
              cursor: 'pointer',
              marginRight: 10,
              color: token.colorText
            }}
            title="后退"
            onClick={() => window.history.back()}
          />
          <ReloadOutlined
            style={{
              cursor: 'pointer',
              marginRight: 10,
              color: token.colorText
            }}
            title="刷新"
            onClick={() => {
              window.location.reload()
            }}
          />
          <AutoComplete
            className="auto-complete"
            ref={autoCompleteRef}
            allowClear
            open={show}
            options={options.map((item, idx) => ({ ...item, key: item.key || (item.value + '_' + idx) }))}
            value={searchValue}
            onChange={onChange}
            onSelect={onSelect}
            onSearch={onSearch}
            onBlur={() => setShow(false)}
            onFocus={() => {
              if (searchValue.trim()) {
                setShow(true)
              }
            }}
          >
            <Input
              value={searchValue}
              placeholder="搜索"
              onPressEnter={onPressEnter}
              prefix={<SearchOutlined
                style={{ cursor: 'pointer' }}
                title="搜索"
                onClick={e => {
                  e.stopPropagation()
                  onSearchClick()
                }}
              />}
            />
          </AutoComplete>
        </div>
        <div className="setting">
          {
            themeMode === 'light'
              ?
              <MoonOutlined
                title="切换主题"
                style={{ color: '#000000' }}
                onClick={() => setThemeMode(prev => prev === 'light' ? 'dark' : 'light')}
              />
              :
              <SunOutlined
                title="切换主题"
                style={{ color: '#ffffff' }}
                onClick={() => setThemeMode(prev => prev === 'light' ? 'dark' : 'light')}
              />
          }

        </div>
      </div>
    </div>
  )
}

export default MyHeader

