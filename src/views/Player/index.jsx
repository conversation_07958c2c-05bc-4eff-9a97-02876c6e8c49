import React, { useState, useEffect, useCallback, useRef } from 'react'
import { <PERSON>lex, Avatar, Slider, Tooltip, ConfigProvider, message, theme } from 'antd'
import axios from 'axios'

import './index.less'
import { LOCALHOST, getAvatarUrl } from '@/utils/constants'
import leiGod from '../../../public/imgs/leiGod.png'

function Player(props) {

  const [playIcon, setPlayIcon] = useState(true) // 播放图标状态
  const [isPlay, setIsPlay] = useState(false) // 是否正在播放
  const [progress, setProgress] = useState(0) // 进度条百分比
  const [time, setTime] = useState('00:00 / 00:00') // 当前时间 / 总时间
  const [isModalOpen, setIsModalOpen] = useState(false) // 歌词界面显示
  const [showAnimation, setShowAnimation] = useState(false) // 动画状态
  const [audioState, setAudioState] = useState({  // 音频状态
    isPlaying: false,
    currentTime: 0,
    currentTrack: null,
    duration: 0,
    volume: 0.5,
    ended: false,
    songInfo: null
  })
  // 歌词相关状态
  const [lyrics, setLyrics] = useState([]) // 歌词数组
  const [currentLyricIndex, setCurrentLyricIndex] = useState(0) // 当前歌词索引
  const lyricsRef = useRef(null) // 歌词容器ref
  const [lyricsLoadedId, setLyricsLoadedId] = useState(null) // 已加载歌词的歌曲ID
  const [lyricsLoading, setLyricsLoading] = useState(false) // 歌词加载状态

  // 提取工具函数
  const formatTime = (time) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`
  }

  // 解析歌词时间戳
  const parseLyrics = lyricsText => {
    if (!lyricsText) return []
    const lines = lyricsText.split('\n')
    const timeMap = new Map()

    lines.forEach(line => {
      const timeRegex = /\[(\d{2}):(\d{2})\.(\d{2,3})]/
      const match = line.match(timeRegex)
      if (match) {
        const minutes = parseInt(match[1])
        const seconds = parseInt(match[2])
        const milliseconds = parseInt(match[3])
        const time = minutes * 60 + seconds + milliseconds / 1000
        const text = line.replace(timeRegex, '').trim()

        if (text) {
          if (timeMap.has(time)) {
            timeMap.get(time).push(text)
          } else {
            timeMap.set(time, [text])
          }
        }
      }
    })

    const parsedLyrics = []
    timeMap.forEach((texts, time) => {
      parsedLyrics.push({
        time,
        text: texts.join('\n')
      })
    })

    return parsedLyrics.sort((a, b) => a.time - b.time)
  }

  // 获取歌词
  const fetchLyrics = async id => {
    setLyricsLoading(true)
    const songName = audioState.songInfo?.NAME || ''
    const artist = audioState.songInfo?.ARTIST || ''
    // 如果本地有 则直接用
    if (!audioState.songInfo.isLocalPlay) {
      const { lyrics, success } = await window.audioAPI.getLocalLyrics(`${artist} - ${songName}`)
      if (success) {
        setLyrics(parseLyrics(lyrics))
        setLyricsLoading(false)
      } else {
        // 本地没有则请求
        try {
          const response = await axios.get(LOCALHOST + '/proxy', {
            params: {
              type: 'lyrics',
              musicId: id
            }
          })
          const { code, data } = response.data
          if (code === 200 && data?.lrclist.length) {
            // 将歌词数组转换为LRC格式
            const lrcText = data.lrclist.map(item => {
              const time = parseFloat(item.time)
              const minutes = Math.floor(time / 60)
              const seconds = Math.floor(time % 60)
              const milliseconds = Math.floor((time % 1) * 1000)
              return `[${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}]${item.lineLyric}`
            }).join('\n')
            const parsedLyrics = parseLyrics(lrcText)
            // 下载到本地
            await window.audioAPI.saveLyrics({ name: `${artist} - ${songName}`, lrc: lrcText })
            setLyrics(parsedLyrics)
            // 歌词加载完成后立即定位到当前位置
            setTimeout(() => {
              scrollToCurrentTime(audioState.currentTime)
            }, 100) // 给DOM一点时间更新
          } else {
            // 如果没有歌词，显示默认信息
            setLyrics([
              { time: 0, text: songName || '暂无歌词' },
              { time: 1, text: artist || '' }
            ])
          }
        } catch (error) {
          console.error('获取歌词失败:', error)
          setLyrics([
            { time: 0, text: songName || '暂无歌词' },
            { time: 1, text: artist || '' }
          ])
        } finally {
          setLyricsLoading(false)
        }
      }

    }
  }

  // 更新当前歌词索引
  const updateCurrentLyric = (currentTime) => {
    if (lyrics.length === 0) return
    let index = 0
    for (let i = 0; i < lyrics.length; i++) {
      if (currentTime >= lyrics[i].time) {
        index = i
      } else {
        break
      }
    }
    if (index !== currentLyricIndex) {
      setCurrentLyricIndex(index)
      // 滚动到当前歌词
      scrollToCurrentLyric(index)
    }
  }

  // 滚动到当前歌词
  const scrollToCurrentLyric = (index) => {
    if (lyricsRef.current) {
      const lyricElements = lyricsRef.current.querySelectorAll('.lyric-line')
      if (lyricElements[index]) {
        lyricElements[index].scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
      }
    }
  }

  // 立即定位到当前播放位置（用于歌词界面打开时）
  const scrollToCurrentTime = (currentTime) => {
    if (lyrics.length === 0) return
    let index = 0
    for (let i = 0; i < lyrics.length; i++) {
      if (currentTime >= lyrics[i].time) {
        index = i
      } else {
        break
      }
    }
    setCurrentLyricIndex(index)
    // 使用 setTimeout 确保 DOM 已更新
    setTimeout(() => {
      if (lyricsRef.current) {
        const lyricElements = lyricsRef.current.querySelectorAll('.lyric-line')
        if (lyricElements[index]) {
          lyricElements[index].scrollIntoView({
            behavior: 'auto', // 使用 auto 而不是 smooth，避免动画延迟
            block: 'center'
          })
        }
      }
    }, 50)
  }

  // 优化播放函数
  const playAudio = async () => {
    try {
      if (audioState.currentTrack) {
        await window.audioAPI.play(audioState.currentTrack, audioState.currentTime, audioState.songInfo)
      }
    } catch (err) {
      message.error('播放音频失败，请检查音频文件路径或网络连接。')
    }
  }

  const pauseAudio = () => {
    window.audioAPI.pause()
  }

  const nextAudio = async () => {
    const state = await window.audioAPI.getState()
    const { playList, currentMusicId } = state
    if (!playList || !currentMusicId) return
    // 如果是在本地播放
    if (state.songInfo.isLocalPlay) {
      const nextIdx = (playList.findIndex(item => item.MUSICRID === currentMusicId) + 1) % playList.length
      const nextSongInfo = playList[nextIdx]
      window.audioAPI.play(`file://${nextSongInfo.path}`, 0, nextSongInfo, nextSongInfo.MUSICRID)
      window.audioAPI.setCurrentMusicId(nextSongInfo.MUSICRID)
      const { lyrics, success } = await window.audioAPI.getLocalLyrics(audioState.currentTrack)
      if (success) {
        setLyrics(parseLyrics(lyrics))
      }
      return
    }
    // 找到当前音乐在播放列表中的索引
    const idx = playList.findIndex(item => item.MUSICRID === currentMusicId)
    if (idx === -1) return
    // 计算下一首音乐的索引
    const nextIdx = (idx + 1) % playList.length
    const nextSongInfo = playList[nextIdx]
    // 获取播放链接
    const musicId = nextSongInfo.MUSICRID
    axios.get(LOCALHOST + '/proxy', {
      params: {
        type: 'musicId',
        musicId
      }
    }).then(res => {
        if (res.data.code === 200) {
          const { url, duration } = res.data.data
          // 2. play时传递musicId参数
          const songInfo = {
            NAME: nextSongInfo.NAME || nextSongInfo.SONGNAME,
            ARTIST: nextSongInfo.ARTIST,
            ALBUM: nextSongInfo.ALBUM || '',
            cover: nextSongInfo.cover,
            duration: duration || 0,
            MUSICRID: nextSongInfo.MUSICRID
          }
          window.audioAPI.play(url, 0, songInfo, musicId)
          window.audioAPI.setCurrentMusicId(nextSongInfo.MUSICRID)
        }
      }
    ).catch(err => {
      message.error('获取下一首歌曲失败，请稍后重试。')
    })
  }

  const prevAudio = async () => {
    const state = await window.audioAPI.getState()
    const { playList, currentMusicId } = state
    if (!playList || !currentMusicId) return
    // 本地播放
    if (state.songInfo.isLocalPlay) {
      const prevIdx = (playList.findIndex(item => item.MUSICRID === currentMusicId) - 1 + playList.length) % playList.length
      const prevSongInfo = playList[prevIdx]
      window.audioAPI.play(`file://${prevSongInfo.path}`, 0, prevSongInfo, prevSongInfo.MUSICRID)
      window.audioAPI.setCurrentMusicId(prevSongInfo.MUSICRID)
      const { lyrics, success } = await window.audioAPI.getLocalLyrics(audioState.currentTrack)
      if (success) {
        setLyrics(parseLyrics(lyrics))
      }
      return
    }
    // 找到当前音乐在播放列表中的索引
    const idx = playList.findIndex(item => item.MUSICRID === currentMusicId)
    if (idx === -1) return
    // 计算上一首音乐的索引
    const prevIdx = (idx - 1 + playList.length) % playList.length
    const prevSongInfo = playList[prevIdx]
    const musicId = prevSongInfo.MUSICRID
    axios.get(LOCALHOST + '/proxy', {
      params: {
        type: 'musicId',
        musicId
      }
    }).then(res => {
      if (res.data.code === 200) {
        const { url, duration } = res.data.data
        const songInfo = {
          NAME: prevSongInfo.NAME || prevSongInfo.SONGNAME,
          ARTIST: prevSongInfo.ARTIST,
          ALBUM: prevSongInfo.ALBUM || '',
          cover: prevSongInfo.cover,
          duration: duration || 0,
          MUSICRID: prevSongInfo.MUSICRID
        }
        window.audioAPI.play(url, 0, songInfo, musicId)
        window.audioAPI.setCurrentMusicId(prevSongInfo.MUSICRID)
      }
    }).catch(
      err => {
        message.error('获取上一首歌曲失败，请稍后重试。')
      }
    )
  }

  // 切换播放状态
  const changePlayIcon = () => {
    if (isPlay) {
      pauseAudio()
    } else {
      playAudio()
    }
    setIsPlay(!isPlay)
    setPlayIcon(!playIcon)
  }

  const changeVolume = value => {
    // 更新音量状态
    window.audioAPI.setVolume(value / 100)
  }

// 更新时间
  const updateTime = () => {
    if (audioState.currentTime) {
      const currentTime = audioState.currentTime
      const duration = audioState.duration
      // 格式化为 mm:ss
      const formattedTime = formatTime(currentTime)
      const formattedDuration = formatTime(duration)
      // 更新状态
      setTime(`${formattedTime} / ${formattedDuration}`)
      // 更新歌词
      updateCurrentLyric(currentTime)
    }
  }

  useEffect(() => {
    if (audioState.currentTrack) {
      updateTime()
    }
    if (audioState.ended) {
      setIsPlay(false)
      setPlayIcon(true)
      setProgress(0)
      setTime('00:00 / 00:00')
      // 自动播放下一曲
      nextAudio()
    }
  }, [audioState])

  // 跳转到指定位置播放
  const handleSeekChange = useCallback((v) => {
    if (audioState.isPlaying) {
      setIsPlay(true)
      setPlayIcon(false)
    }
    setProgress(v)
    window.audioAPI.seek(v)
  }, [audioState.isPlaying])

// 页面加载时获取音频状态
  useEffect(() => {
    const fetchAudioState = async () => {
      const state = await window.audioAPI.getState()
      if (state) {
        setAudioState(state)
        setProgress(state.currentTime)
        setIsPlay(state.isPlaying)
        setPlayIcon(!state.isPlaying)
      }
    }

    fetchAudioState()
    // 监听状态更新
    const handleStateUpdate = (event, state) => {
      setAudioState(state)
      setProgress(state.currentTime)
      setIsPlay(state.isPlaying)
      setPlayIcon(!state.isPlaying)
    }
    window.audioAPI.onStateUpdate(handleStateUpdate)

    return () => {
      window.audioAPI.removeListener('audio-state', handleStateUpdate)
    }
  }, [])

  // 歌曲切换时重置歌词加载标记
  useEffect(() => {
    setLyrics([])
    setCurrentLyricIndex(0)
    if (audioState.songInfo?.isLocalPlay) {
      window.audioAPI.getLocalLyrics(audioState.currentTrack).then(({ success, lyrics }) => {
        if (success) {
          setLyrics(parseLyrics(lyrics))
        }
      })
      return
    }
    if (isModalOpen && audioState.songInfo?.MUSICRID && audioState.songInfo?.MUSICRID !== lyricsLoadedId) {
      const musicId = audioState.songInfo.MUSICRID.split('_')[1]
      fetchLyrics(musicId)
      setLyricsLoadedId(audioState.songInfo.MUSICRID)
    } else {
      setLyricsLoadedId(null)
    }
    // eslint-disable-next-line
  }, [audioState.songInfo?.MUSICRID])

  // 歌词数据更新后立即定位
  useEffect(() => {
    if (lyrics.length > 0 && isModalOpen && !lyricsLoading) {
      scrollToCurrentTime(audioState.currentTime)
    }
  }, [lyrics, isModalOpen, lyricsLoading])

  // 头像点击事件
  const handleAvatarClick = async () => {
    if (isModalOpen) {
      return handleCloseLyrics()
    }
    setIsModalOpen(true)
    setShowAnimation(true)
    console.log(audioState)
    // 先读取本地歌词
    if (audioState.songInfo?.isLocalPlay) {
      const { lyrics, success } = await window.audioAPI.getLocalLyrics(audioState.currentTrack)
      if (success) {
        setLyrics(parseLyrics(lyrics))
      }
      return
    }
    const musicId = audioState.songInfo?.MUSICRID?.split('_')[1]
    if (audioState.songInfo?.MUSICRID && audioState.songInfo?.MUSICRID !== lyricsLoadedId) {
      fetchLyrics(musicId)
      setLyricsLoadedId(audioState.songInfo.MUSICRID)
    } else if (lyrics.length > 0) {
      // 如果歌词已经加载，立即定位到当前位置
      scrollToCurrentTime(audioState.currentTime)
    }
  }

  // 关闭歌词界面动画
  const handleCloseLyrics = () => {
    setShowAnimation(false)
    setTimeout(() => setIsModalOpen(false), 300) // 300ms和动画时长一致
  }

  // 打开时自动加动画class
  useEffect(() => {
    if (isModalOpen) {
      setShowAnimation(true)
    }
  }, [isModalOpen])

  return (
    <>
      {/* 歌词弹窗div方式实现 */}
      {isModalOpen && (
        <div
          className={`lyrics-modal ${showAnimation ? 'lyrics-fade-enter' : 'lyrics-fade-exit'}`}
          style={{
            width: '100vw',
            height: 'calc(100vh - 85px)',
            position: 'fixed',
            top: 0,
            left: 0,
            zIndex: 1000,
            background: '#111', // 这里改为黑色
            margin: 0,
            padding: 0,
            boxSizing: 'border-box',
            overflow: 'hidden',
            transition: 'background 0.4s'
          }}
        >
          {/* 右上角关闭按钮 */}
          <div className="lyrics-close-btn" onClick={handleCloseLyrics}>×</div>
          <div className="lyrics-left">
            <div className="album-cover">
              <Avatar
                shape="square"
                size={300}
                src={audioState.songInfo?.cover
                  ? `${LOCALHOST}/proxy?type=avatar&avatarUrl=${audioState.songInfo?.cover}`
                  : leiGod}
              />
            </div>
            <div className="song-info-modal">
              <h3 className="song-title-modal">{audioState.songInfo?.NAME}</h3>
              <p className="song-artist-modal">{audioState.songInfo?.ARTIST}</p>
            </div>
          </div>
          <div className="lyrics-right">
            <div className="lyrics-container" ref={lyricsRef}>
              {lyricsLoading ? (
                <div className="lyrics-loading">正在加载歌词...</div>
              ) : (
                lyrics.map((lyric, index) => (
                  <div
                    key={index}
                    className={`lyric-line ${index === currentLyricIndex ? 'active' : ''}`}
                  >
                    {lyric.text}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      )}
      <div className="music-player">
        <span className="player-progress">
           <Flex gap="small" vertical>
             <Slider
               style={{ margin: 0, padding: 0 }}
               styles={{
                 track: {
                   background: 'red',
                 },
                 rail: {
                   background: 'rgba(255, 255, 255, 0.3)',
                 },
                 handle: {
                   display: 'none',
                 }
               }}
               tooltip={{ formatter: null }}
               max={audioState.duration}
               value={progress}
               onChange={v => setProgress(v)}
               onChangeComplete={handleSeekChange}
             />
           </Flex>
      </span>
        <div className="player-main">
          <div className="song-info">
            <div className="song-avatar">
              <Avatar shape="square" size={60}
                      onClick={handleAvatarClick}
                      src={audioState.songInfo?.cover
                        ? `${LOCALHOST}/proxy?type=avatar&avatarUrl=${audioState.songInfo?.cover}`
                        : leiGod}
              />
            </div>
            <div className="song-details">
              <span className="song-title">{audioState.songInfo?.NAME}</span>
              <span className="song-artist">{audioState.songInfo?.ARTIST}</span>
            </div>
          </div>
          <div className="player-controls">
            <span className="music-time">{time}</span>
            <div className="music-actions">
              <span title="上一曲" className="music-icon prev-icon" onClick={prevAudio}/>
              <span onClick={changePlayIcon}>
              {
                playIcon
                  ?
                  <span className="music-icon play-icon" title="播放"/>
                  :
                  <span className="music-icon pause-icon" title="暂停"/>
              }
            </span>
              < span title="下一曲" className="music-icon next-icon" onClick={nextAudio}/>
            </div>
          </div>
          <div className="player-extra">
            <span className="music-icon loop-icon"/>
            <Tooltip
              color="rgba(0, 0, 0, 0.4)"
              title={
                <ConfigProvider
                  theme={{
                    components: {
                      Slider: {
                        colorPrimary: 'rgb(203,9,9)', // 滑块颜色
                        algorithm: true, // 启用算法
                      },
                    },
                  }}
                >
                  <Slider
                    styles={{
                      track: {
                        background: 'red',
                      },
                      rail: {
                        background: 'rgba(255, 255, 255, 0.3)',
                      },
                      handle: {},
                    }}
                    onChange={changeVolume}
                    vertical
                    defaultValue={audioState.volume * 100}
                    style={{ height: 100 }}/>
                </ConfigProvider>
              }
            >
              <span className="music-icon volume-icon"/>
            </Tooltip>
            <span className="music-icon menu-icon"/>
          </div>
        </div>
      </div>
    </>
  )
}

export default Player