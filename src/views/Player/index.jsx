import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import { useFavorites } from '../../hooks/useFavorites'
import { Flex, Avatar, Slider, Tooltip, message as MESSAGE, Drawer, List, theme, Dropdown } from 'antd'
import { DeleteOutlined, HeartOutlined, HeartFilled } from '@ant-design/icons'
import axios from 'axios'

import './index.less'
import { LOCALHOST, replaceSize } from '@/utils/constants'
import leiGod from '../../../public/imgs/leiGod.png'

function Player() {
  const { token } = theme.useToken()
  const [message, contextHolder] = MESSAGE.useMessage()
  const [playIcon, setPlayIcon] = useState(true) // 播放图标状态
  const [isPlay, setIsPlay] = useState(false) // 是否正在播放
  const [time, setTime] = useState('00:00 / 00:00') // 当前时间 / 总时间
  const [isModalOpen, setIsModalOpen] = useState(false) // 歌词界面显示
  const [showAnimation, setShowAnimation] = useState(false) // 动画状态
  const [audioState, setAudioState] = useState({  // 音频状态
    isPlaying: false,
    currentTime: 0,
    currentTrack: null,
    duration: 0,
    volume: 0.5,
    ended: false,
    songInfo: null
  })
  // 歌词相关状态
  const [lyrics, setLyrics] = useState([]) // 歌词数组
  const [currentLyricIndex, setCurrentLyricIndex] = useState(0) // 当前歌词索引
  const lyricsRef = useRef(null) // 歌词容器ref
  const [lyricsLoadedId, setLyricsLoadedId] = useState(null) // 已加载歌词的歌曲ID
  const [lyricsLoading, setLyricsLoading] = useState(false) // 歌词加载状态
  const [isDragging, setIsDragging] = useState(false) // 进度条拖拽状态
  const [localProgress, setLocalProgress] = useState(0) // 本地进度条值
  const seekTimeoutRef = useRef(null) // 跳转定时器ref
  const isSeekingRef = useRef(false) // 是否正在跳转
  const volumeTimeoutRef = useRef(0) // 音量调整定时器ref
  const [playlistVisible, setPlaylistVisible] = useState(false) // 播放列表显示状态
  const lastCoverHashRef = useRef(null) // 记录已尝试补封面的 hash，避免重复请求
  const playlistContainerRef = useRef(null) // 播放列表容器引用
  const currentSongRef = useRef(null) // 当前播放歌曲的DOM引用

  // 收藏功能Hook
  const { isFavorite, toggleFavorite } = useFavorites()

  // 优化：缓存头像URL，避免重复计算
  const avatarUrl = useMemo(() => {
    if (!audioState.songInfo?.cover) return leiGod
    return `${LOCALHOST}/proxy?type=avatar&avatarUrl=${audioState.songInfo.cover}`
  }, [audioState.songInfo?.cover])

  // 处理收藏点击
  const handleFavoriteClick = useCallback(async () => {
    if (!audioState.songInfo?.MUSICRID) {
      MESSAGE.error('当前没有播放歌曲')
      return
    }

    await toggleFavorite(audioState.songInfo)
  }, [audioState.songInfo, toggleFavorite])

  // 显示播放列表
  const showPlaylist = () => {
    setPlaylistVisible(!playlistVisible)
  }

  // 滚动到当前播放的歌曲
  const scrollToCurrentSong = useCallback(() => {
    if (!playlistVisible || !playlistContainerRef.current) return

    try {
      const container = playlistContainerRef.current
      // 通过 currentMusicId 查找当前播放歌曲的DOM元素
      const currentSong = container.querySelector(`.playlist-item.playing`)
      if (!currentSong) return

      // 计算滚动位置，使当前歌曲居中显示
      const containerRect = container.getBoundingClientRect()
      const songRect = currentSong.getBoundingClientRect()

      const containerScrollTop = container.scrollTop
      const songOffsetTop = songRect.top - containerRect.top + containerScrollTop

      const containerHeight = container.clientHeight
      const songHeight = songRect.height
      const scrollTop = songOffsetTop - (containerHeight / 2) + (songHeight / 2)

      // 平滑滚动到目标位置
      container.scrollTo({
        top: Math.max(0, scrollTop),
        behavior: 'smooth'
      })
    } catch (error) {
      console.error('滚动到当前歌曲失败:', error)
    }
  }, [playlistVisible])

  // 监听播放状态变化，自动滚动到当前歌曲
  useEffect(() => {
    if (playlistVisible && audioState.currentMusicId) {
      // 使用 requestAnimationFrame 确保DOM已更新
      requestAnimationFrame(() => {
        setTimeout(scrollToCurrentSong, 50)
      })
    }
  }, [audioState.currentMusicId, playlistVisible, scrollToCurrentSong])

  // 播放列表打开时，自动滚动到当前歌曲
  useEffect(() => {
    if (playlistVisible && audioState.currentMusicId) {
      requestAnimationFrame(() => {
        setTimeout(scrollToCurrentSong, 100)
      })
    }
  }, [playlistVisible, audioState.currentMusicId, scrollToCurrentSong])

  // 播放指定歌曲
  const playSong = async (song) => {
    try {
      // 根据歌曲类型决定 URL 参数
      let url = ''
      if (song.isLocalPlay && song.path) {
        // 本地歌曲：直接传递 file:// URL，主进程直接播放
        url = `file://${song.path}`
      } else {
        // 在线歌曲：传递空字符串，让主进程通过 musicId 获取播放链接
        url = ''
      }
      const result = await window.audioAPI.play(url, 0, song, song.MUSICRID, audioState.playList)
      if (!result.success) {
        message.error(result.message || '播放失败')
      }
    } catch (error) {
      console.error('播放失败:', error)
      message.error('播放失败')
    }
  }

  // 从播放列表中删除歌曲
  const removeFromPlaylist = async (index) => {
    try {
      const newPlaylist = audioState.playList.filter((_, i) => i !== index)
      const removedSong = audioState.playList[index]

      // 如果移除的是当前播放的歌曲，需要特殊处理
      if (removedSong.MUSICRID === audioState.currentMusicId) {
        if (newPlaylist.length === 0) {
          // 如果播放列表为空，清空播放状态
          await window.audioAPI.setPlayList([])
          setLocalProgress(0)
          message.success('已从播放列表移除，播放列表已清空')
          return
        } else {
          // 如果还有歌曲，自动播放下一首
          const nextIndex = index >= newPlaylist.length ? 0 : index
          const nextSong = newPlaylist[nextIndex]

          // 先更新播放列表
          await window.audioAPI.setPlayList(newPlaylist)

          // 然后播放下一首歌曲
          if (nextSong.isLocalPlay && nextSong.path) {
            await window.audioAPI.play(`file://${nextSong.path}`, 0, nextSong, nextSong.MUSICRID, newPlaylist)
          } else {
            await window.audioAPI.play('', 0, nextSong, nextSong.MUSICRID, newPlaylist)
          }

          message.success('已从播放列表移除，自动播放下一首')
          return
        }
      }

      // 移除的不是当前播放歌曲，正常处理
      await window.audioAPI.setPlayList(newPlaylist)
      message.success('已从播放列表移除')
    } catch (error) {
      message.error('移除失败')
    }
  }

  // 清空播放列表
  const clearPlaylist = async () => {
    try {
      await window.audioAPI.setPlayList([])
      setLocalProgress(0)
    } catch (e) {
      message.error('清空失败')
    }
  }

  // 获取右键菜单项
  const getContextMenuItems = (item, index) => [
    {
      key: 'play',
      label: '播放',
      onClick: () => playSong(item)
    },
    {
      key: 'delete',
      label: '从播放列表移除',
      onClick: () => removeFromPlaylist(index)
    },
    {
      key: 'like',
      label: '添加到收藏',
      onClick: () => message.info('收藏功能开发中...')
    },
    {
      key: 'download',
      label: '下载',
      onClick: () => message.info('下载功能开发中...')
    }
  ]

  // 提取工具函数
  const formatTime = (time) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`
  }

  // 解析歌词时间戳
  const parseLyrics = lyricsText => {
    if (!lyricsText) return []
    const lines = lyricsText.split('\n')
    const timeMap = new Map()

    lines.forEach(line => {
      const timeRegex = /\[(\d{2}):(\d{2})\.(\d{2,3})]/
      const match = line.match(timeRegex)
      if (match) {
        const minutes = parseInt(match[1])
        const seconds = parseInt(match[2])
        const milliseconds = parseInt(match[3])
        const time = minutes * 60 + seconds + milliseconds / 1000
        const text = line.replace(timeRegex, '').trim()

        if (text) {
          if (timeMap.has(time)) {
            timeMap.get(time).push(text)
          } else {
            timeMap.set(time, [text])
          }
        }
      }
    })

    const parsedLyrics = []
    timeMap.forEach((texts, time) => {
      parsedLyrics.push({
        time,
        text: texts.join('\n')
      })
    })

    return parsedLyrics.sort((a, b) => a.time - b.time)
  }

  // 获取歌词（优先按歌曲 hash 从本地读取；失败则在线获取并按 hash 保存）
  const fetchLyrics = async (id) => {
    setLyricsLoading(true)
    const songHash = audioState.songInfo?.hash

    // 如果在线歌曲有本地hash则通过hash获取
    if (songHash) {
      console.log('在线歌曲有本地歌词')
      const { lyrics, success } = await window.audioAPI.getLocalLyrics(audioState.songInfo)
      if (success && lyrics) {
        setLyrics(parseLyrics(lyrics))
        setLyricsLoading(false)
        return
      } else {
        setLyrics([{ time: 0, text: '暂无歌词' }])
      }
    }

    // 在线播放不保存 因为没有hash
    try {
      const response = await axios.get(LOCALHOST + '/proxy', {
        params: { type: 'lyrics', musicId: id }
      })
      console.log('获取在线歌曲歌词')
      const { code, data } = response.data
      if (code === 200 && data?.lrclist?.length) {
        const lrcText = data.lrclist.map(item => {
          const time = parseFloat(item.time)
          const minutes = Math.floor(time / 60)
          const seconds = Math.floor(time % 60)
          const milliseconds = Math.floor((time % 1) * 1000)
          return `[${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}]${item.lineLyric}`
        }).join('\n')

        const parsedLyrics = parseLyrics(lrcText)
        setLyrics(parsedLyrics)
        setTimeout(() => scrollToCurrentTime(audioState.currentTime), 100)
      } else {
        setLyrics([{ time: 0, text: '暂无歌词' }])
      }
    } catch (error) {
      console.error('获取歌词失败:', error)
      setLyrics([{ time: 0, text: '暂无歌词' }])
    } finally {
      setLyricsLoading(false)
    }
  }

  // 更新当前歌词索引
  const updateCurrentLyric = (currentTime) => {
    if (lyrics.length === 0) return
    let index = 0
    for (let i = 0; i < lyrics.length; i++) {
      if (currentTime >= lyrics[i].time) {
        index = i
      } else {
        break
      }
    }
    if (index !== currentLyricIndex) {
      setCurrentLyricIndex(index)
      // 滚动到当前歌词
      scrollToCurrentLyric(index)
    }
  }

  // 滚动到当前歌词
  const scrollToCurrentLyric = (index) => {
    if (lyricsRef.current) {
      const lyricElements = lyricsRef.current.querySelectorAll('.lyric-line')
      if (lyricElements[index]) {
        lyricElements[index].scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
      }
    }
  }

  // 立即定位到当前播放位置（用于歌词界面打开时）
  const scrollToCurrentTime = (currentTime) => {
    if (lyrics.length === 0) return
    let index = 0
    for (let i = 0; i < lyrics.length; i++) {
      if (currentTime >= lyrics[i].time) {
        index = i
      } else {
        break
      }
    }
    setCurrentLyricIndex(index)
    // 使用 setTimeout 确保 DOM 已更新
    setTimeout(() => {
      if (lyricsRef.current) {
        const lyricElements = lyricsRef.current.querySelectorAll('.lyric-line')
        if (lyricElements[index]) {
          lyricElements[index].scrollIntoView({
            behavior: 'auto', // 使用 auto 而不是 smooth，避免动画延迟
            block: 'center'
          })
        }
      }
    }, 50)
  }

  // 优化播放函数
  const playAudio = async () => {
    try {
      if (audioState.currentTrack) {
        const result = await window.audioAPI.play(audioState.currentTrack, audioState.currentTime, audioState.songInfo)
        if (!result.success) {
          message.error(result.message || '播放失败')
        }
      }
    } catch (err) {
      message.error('播放音频失败，请检查音频文件路径或网络连接。')
    }
  }

  const pauseAudio = async () => {
    try {
      const result = await window.audioAPI.pause()
      if (!result.success) {
        message.error(result.message || '暂停失败')
      }
    } catch (err) {
      message.error('暂停失败，请稍后重试。')
    }
  }

  const nextAudio = async () => {
    try {
      const result = await window.audioAPI.nextAudio()
      if (!result.success) {
        message.error(result.message || '切换下一首失败')
      }
    } catch (err) {
      message.error('切换下一首失败，请稍后重试。')
    }
  }

  const prevAudio = async () => {
    try {
      const result = await window.audioAPI.prevAudio()
      if (!result.success) {
        message.error(result.message || '切换上一首失败')
      }
    } catch (err) {
      message.error('切换上一首失败，请稍后重试。')
    }
  }

  // 切换播放状态
  const changePlayIcon = () => {
    if (!audioState.currentTrack) return
    if (isPlay) {
      pauseAudio()
    } else {
      playAudio()
    }
    setIsPlay(!isPlay)
    setPlayIcon(!playIcon)
  }

  // 更新音量
  const changeVolume = value => {
    // 防抖
    if (volumeTimeoutRef.current) {
      clearTimeout(volumeTimeoutRef.current)
    }
    volumeTimeoutRef.current = setTimeout(() => {
      window.audioAPI.setVolume(value / 100)
    }, 100)
  }

  useEffect(() => {
    if (audioState.currentTrack) {
      updateTime()
    }
    if (audioState.ended) {
      setIsPlay(false)
      setPlayIcon(true)
      setTime('00:00 / 00:00')
      // 自动播放下一曲 现在由主进程控制
      // nextAudio()
    }
  }, [audioState])

  // 处理拖拽结束 - 执行真正跳转
  const handleSeekChange = useCallback((v) => {
    setLocalProgress(v)
    isSeekingRef.current = true
    // 清除之前的定时器
    if (seekTimeoutRef.current) {
      clearTimeout(seekTimeoutRef.current)
    }
    // 立即执行跳转
    window.audioAPI.seek(v)
    // 延迟重置状态
    seekTimeoutRef.current = setTimeout(() => {
      setIsDragging(false)
      isSeekingRef.current = false
    }, 500) // 增加延迟时间
  }, [])

  // 处理拖拽中 - 只更新本地状态
  const handleSeekDragging = useCallback((v) => {
    setIsDragging(true)
    setLocalProgress(v)
    isSeekingRef.current = true
    // 清除之前的定时器
    if (seekTimeoutRef.current) {
      clearTimeout(seekTimeoutRef.current)
    }
  }, [])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (seekTimeoutRef.current) {
        clearTimeout(seekTimeoutRef.current)
      }
    }
  }, [])

  // 更新时间 - 使用本地进度或音频进度
  const updateTime = () => {
    if (audioState.currentTime) {
      const currentTime = audioState.currentTime
      const duration = audioState.duration
      // 只有在非交互状态下才更新进度条
      if (!isDragging && !isSeekingRef.current) {
        setLocalProgress(currentTime)
      }
      const formattedTime = formatTime(currentTime)
      const formattedDuration = formatTime(duration)
      setTime(`${formattedTime} / ${formattedDuration}`)
      updateCurrentLyric(currentTime)
    }
  }

  // 页面加载时获取音频状态
  useEffect(() => {
    const fetchAudioState = async () => {
      const state = await window.audioAPI.getState()
      if (state) {
        setAudioState(state)
        setIsPlay(state.isPlaying)
        setPlayIcon(!state.isPlaying)
      }
    }

    fetchAudioState()
    // 监听主进程下发的音频状态（精简处理：完整替换，以保证歌词/进度等副作用正常）
    const handleStateUpdate = (event, state) => {
      try {
        const cache = window._coverCache || {}
        const merged = { ...state }
        // 将 cache 中的 localCover 合并到 incoming playList
        if (state.playList) {
          merged.playList = state.playList.map(item => {
            const key = item.hash || item.MUSICRID
            if (key && cache[key]) return { ...item, localCover: cache[key] }
            return item
          })
        }

        // 合并 songInfo 的 localCover
        if (state.songInfo) {
          const key = state.songInfo.hash || state.songInfo.MUSICRID
          if (key && cache[key]) merged.songInfo = { ...state.songInfo, localCover: cache[key] }
        }

        // 将合并后的 localCover 写回全局 cache，供后续合并使用
        try {
          window._coverCache = window._coverCache || {}
          for (const it of merged.playList || []) {
            if (it.localCover) {
              if (it.hash) window._coverCache[it.hash] = it.localCover
              if (it.MUSICRID) window._coverCache[it.MUSICRID] = it.localCover
            }
          }
          if (merged.songInfo && merged.songInfo.localCover) {
            const si = merged.songInfo
            if (si.hash) window._coverCache[si.hash] = si.localCover
            if (si.MUSICRID) window._coverCache[si.MUSICRID] = si.localCover
          }
        } catch (e) {}

        // 有条件地更新 state：如果 playList 标识相同且 localCover 无变化，则跳过更新，避免重复渲染覆盖
        setAudioState(merged)
        setIsPlay(state.isPlaying)
        setPlayIcon(!state.isPlaying)
      } catch (e) {
        console.error('[Player] handleStateUpdate err', e)
        setAudioState(state)
        setIsPlay(state.isPlaying)
        setPlayIcon(!state.isPlaying)
      }
    }
    window.audioAPI.onStateUpdate(handleStateUpdate)

    // 订阅封面更新：仅局部合并 localCover，避免替换整个 audioState
    const coverHandler = (payload) => {
      try {
        const { hash, localCover } = payload || {}
        if (!hash) return
        // 写入全局短期缓存
        window._coverCache = window._coverCache || {}
        if (hash) window._coverCache[hash] = localCover

        // 局部更新当前 state 中对应项
        setAudioState(prev => {
          if (!prev) return prev
          const next = { ...prev }
          if (next.songInfo && (next.songInfo.hash === hash)) {
            next.songInfo = { ...next.songInfo, localCover }
          }
          if (Array.isArray(next.playList)) {
            next.playList = next.playList.map(it => {
              if (it.hash === hash) return { ...it, localCover }
              return it
            })
          }

          return next
        })
      } catch (e) {}
    }

    const progressHandler = (payload) => {
      try {
        const { currentTime, duration, isPlaying } = payload || {}
        setAudioState(prev => ({
          ...prev,
          currentTime: currentTime ?? prev.currentTime,
          duration: duration ?? prev.duration,
          isPlaying: isPlaying ?? prev.isPlaying
        }))
        // 同步播放状态/图标
        setIsPlay(isPlaying ?? isPlay)
        setPlayIcon(!(isPlaying ?? isPlay))
        // 更新进度条/歌词
        updateTime()
      } catch (e) {}
    }

    window.coverAPI.onCoverUpdated(coverHandler)
    window.coverAPI.onAudioProgress(progressHandler)

    return () => {
      // 使用 preload 暴露的移除方法，确保能正确注销
      try { window.audioAPI.removeStateUpdate(handleStateUpdate) } catch (e) {}
      try { window.coverAPI.removeCoverUpdated(coverHandler) } catch (e) {}
      try { window.coverAPI.removeAudioProgress(progressHandler) } catch (e) {}
    }
  }, [])

  // 歌词/播放器界面切歌时，若无本地封面则按hash获取
  useEffect(() => {
    const s = audioState.songInfo
    const hash = s?.hash
    if (!hash || s?.localCover) return
    if (!window.audioAPI?.fetchAndUpdateCoverByHash) return
    if (lastCoverHashRef.current === hash) return
    lastCoverHashRef.current = hash
    window.audioAPI.fetchAndUpdateCoverByHash(hash).then((p) => {
      if (p) {
        setAudioState(prev => ({
          ...prev,
          songInfo: prev.songInfo ? { ...prev.songInfo, localCover: p } : prev.songInfo
        }))
      }
    }).catch(() => {})
  }, [audioState.songInfo?.hash])

  // 歌曲切换时重置歌词加载标记
  useEffect(() => {
    setLyrics([])
    setCurrentLyricIndex(0)
    setLyricsLoadedId(null)

    // 如果歌词页面是打开的，则自动获取新歌曲的歌词
    if (isModalOpen && audioState.songInfo) {
      if (audioState.songInfo?.isLocalPlay) {
        // 本地歌曲：尝试获取本地歌词
        window.audioAPI.getLocalLyrics(audioState.songInfo).then(({ success, lyrics }) => {
          console.log('获取本地歌曲歌词')
          if (success && lyrics) {
            setLyrics(parseLyrics(lyrics))
            // 立即定位到当前位置
            setTimeout(() => scrollToCurrentTime(audioState.currentTime), 100)
          } else {
            setLyrics([{ time: 0, text: '暂无歌词' }])
          }
        })
      } else {
        // 在线歌曲：获取歌词
        const musicId = audioState.songInfo?.MUSICRID?.split('_')[1]
        if (musicId) {
          fetchLyrics(musicId)
          setLyricsLoadedId(audioState.songInfo.MUSICRID)
        }
      }
    }
  }, [audioState.songInfo?.MUSICRID]) // 移除 isModalOpen 依赖 防止歌词获取两次

  // 歌词数据更新后立即定位
  useEffect(() => {
    if (lyrics.length > 0 && isModalOpen && !lyricsLoading) {
      scrollToCurrentTime(audioState.currentTime)
    }
  }, [lyrics, isModalOpen, lyricsLoading])

  // 头像点击事件
  const handleAvatarClick = async () => {
    setPlaylistVisible(false)
    if (isModalOpen) {
      return handleCloseLyrics()
    }
    setIsModalOpen(true)
    setShowAnimation(true)

    // 只在歌词页面打开时获取歌词
    if (audioState.songInfo?.isLocalPlay) {
      console.log('获取本地歌曲歌词')
      // 本地歌曲：尝试获取本地歌词
      const { lyrics, success } = await window.audioAPI.getLocalLyrics(audioState.songInfo)
      if (success && lyrics) {
        setLyrics(parseLyrics(lyrics))
        // 立即定位到当前位置
        setTimeout(() => scrollToCurrentTime(audioState.currentTime), 100)
      } else {
        setLyrics([{ time: 0, text: '暂无歌词' }])
      }
      return
    }

    // 在线歌曲：获取歌词
    const musicId = audioState.songInfo?.MUSICRID?.split('_')[1]
    if (musicId && audioState.songInfo?.MUSICRID !== lyricsLoadedId) {
      fetchLyrics(musicId)
      setLyricsLoadedId(audioState.songInfo.MUSICRID)
    } else if (lyrics.length > 0) {
      // 如果歌词已经加载，立即定位到当前位置
      setTimeout(() => scrollToCurrentTime(audioState.currentTime), 100)
    }
  }

  // 关闭歌词界面动画
  const handleCloseLyrics = () => {
    setShowAnimation(false)
    setTimeout(() => setIsModalOpen(false), 300) // 300ms和动画时长一致
  }

  // 打开时自动加动画class
  useEffect(() => {
    if (isModalOpen) {
      setShowAnimation(true)
    }
  }, [isModalOpen])

  return (
    <>
      {contextHolder}
      {/* 歌词弹窗div方式实现 */}
      {isModalOpen && (
        <div
          className={`lyrics-modal ${showAnimation ? 'lyrics-fade-enter' : 'lyrics-fade-exit'}`}
          style={{
            width: '100vw',
            height: 'calc(100vh - 85px)',
            position: 'fixed',
            top: 0,
            left: 0,
            zIndex: 1000,
            background: '#111', // 这里改为黑色
            margin: 0,
            padding: 0,
            boxSizing: 'border-box',
            overflow: 'hidden',
            transition: 'background 0.4s'
          }}
        >
          {/* 右上角关闭按钮 */}
          <div className="lyrics-close-btn" onClick={handleCloseLyrics}>×</div>
          <div className="lyrics-left">
            <div className="album-cover">
              <Avatar
                shape="square"
                size={300}
                src={audioState.songInfo?.localCover
                  ? `file://${audioState.songInfo.localCover}`
                  : (audioState.songInfo?.cover
                    ? `${LOCALHOST}/proxy?type=avatar&avatarUrl=${replaceSize(audioState.songInfo?.cover, 500)}`
                    : leiGod)}
              />
            </div>
            <div className="song-info-modal">
              <h3 className="song-title-modal">{audioState.songInfo?.NAME}</h3>
              <p className="song-artist-modal">{audioState.songInfo?.ARTIST}</p>
            </div>
          </div>
          <div className="lyrics-right">
            <div className="lyrics-container" ref={lyricsRef}>
              {lyricsLoading ? (
                <div className="lyrics-loading">正在加载歌词...</div>
              ) : (
                lyrics.map((lyric, index) => (
                  <div
                    key={index}
                    className={`lyric-line ${index === currentLyricIndex ? 'active' : ''}`}
                  >
                    {lyric.text}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      )}
      <div className="music-player">
        <span className="player-progress">
           <Flex gap="small" vertical>
             <Slider
               style={{ margin: 0, padding: 0 }}
               styles={{
                 track: {
                   background: 'red',
                 },
                 rail: {
                   background: 'rgba(255, 255, 255, 0.3)',
                 },
                 handle: {
                   display: 'none',
                 }
               }}
               tooltip={{ formatter: null }}
               max={audioState.duration}
               value={localProgress}
               onChange={handleSeekDragging}
               onChangeComplete={handleSeekChange}
             />
           </Flex>
      </span>
        <div className="player-main">
          <div className="song-info">
            <div className="song-avatar">
              <Avatar shape="square" size={60}
                      onClick={handleAvatarClick}
                      src={audioState.songInfo?.localCover
                        ? `file://${audioState.songInfo.localCover}`
                        : (audioState.songInfo?.cover
                          ? `${LOCALHOST}/proxy?type=avatar&avatarUrl=${audioState.songInfo?.cover}`
                          : leiGod)}
              />
            </div>
            <div className="song-details">
              <span className="song-title">{audioState.songInfo?.NAME}</span>
              <span className="song-artist">{audioState.songInfo?.ARTIST}</span>
            </div>
          </div>
          <div className="player-controls">
            <span className="music-time">{time}</span>
            <div className="music-actions">
              <span title="上一曲" className="music-icon prev-icon" onClick={prevAudio}/>
              <span onClick={changePlayIcon}>
              {
                playIcon
                  ?
                  <span className="music-icon play-icon" title="播放"/>
                  :
                  <span className="music-icon pause-icon" title="暂停"/>
              }
            </span>
              < span title="下一曲" className="music-icon next-icon" onClick={nextAudio}/>
            </div>
            {/* 收藏按钮 */}
            <div className="favorite-action">
              {audioState.songInfo?.MUSICRID && isFavorite(audioState.songInfo.MUSICRID) ? (
                <HeartFilled
                  className="favorite-icon favorited"
                  onClick={handleFavoriteClick}
                  title="取消收藏"
                  style={{ color: '#ff4d4f', fontSize: '18px' }}
                />
              ) : (
                <HeartOutlined
                  className="favorite-icon"
                  onClick={handleFavoriteClick}
                  title="收藏"
                  style={{ fontSize: '18px' }}
                />
              )}
            </div>
          </div>
          <div className="player-extra">
            <span className="music-icon loop-icon"/>
            <Tooltip
              color="rgba(0, 0, 0, 0.4)"
              title={
                <Slider
                  onChange={changeVolume}
                  vertical
                  defaultValue={audioState.volume * 100}
                  style={{ height: 100 }}/>
              }
            >
              <span className="music-icon volume-icon"/>
            </Tooltip>
            <span
              className="music-icon menu-icon"
              onClick={showPlaylist}
              title="播放列表"
              style={{ cursor: 'pointer' }}
            />
          </div>
        </div>
      </div>

      {/* 播放列表抽屉 */}
      <Drawer
        title={
          <>
            <div className="playlist-header">
              <span className="playlist-title">播放列表</span>
              <div className="playlist-actions">
              <span
                className="clear-playlist-btn"
                style={{
                  fontSize: '20px',
                  cursor: 'pointer',
                  '--player-clear-btn': token.colorPrimary
                }}
                title="清空播放列表"
                onClick={clearPlaylist}
              >
                <DeleteOutlined/>
              </span>
              </div>
            </div>
            <div className="playlist-header-total"
                 style={{ color: token.colorTextSecondary }}>{`共 ${audioState?.playList?.length || 0} 首歌曲`}
            </div>
          </>
        }
        closeIcon={null}
        placement="right"
        onClose={() => setPlaylistVisible(false)}
        open={playlistVisible}
        width={350}
        className="playlist-drawer"
        styles={{
          header: {
            padding: '18px 14px 0 14px',
            backgroundColor: token.colorBgContainer,
            borderBottom: `1px solid ${token.colorBorderSecondary}`
          },
          body: {
            backgroundColor: token.colorBgContainer,
            padding: '0 0 88px 0',  // 为底部播放器留出空间
          }
        }}
      >
        <div
          ref={playlistContainerRef}
          className="playlist-container"
          style={{
            maxHeight: 'calc(100vh - 163px)',
            overflowY: 'auto',
            overflowX: 'hidden',
            // 使用主题 token 动态设置滚动条样式
            '--player-scrollbar-track': token.colorBgContainer,
            '--player-scrollbar-thumb': token.colorBorderSecondary,
            '--player-playing': token.colorPrimaryBg,
            '--player-list-hover': token.colorBorderSecondary
          }}
        >
          {
            audioState?.playList?.length
              ?
              <List
                dataSource={audioState.playList}
                renderItem={(item, index) => (
                  <Dropdown
                    trigger={['contextMenu']}
                    menu={{ items: getContextMenuItems(item, index) }}
                    placement="bottomRight"
                  >
                    <List.Item
                      ref={audioState.currentMusicId === item.MUSICRID ? currentSongRef : null}
                      className={`playlist-item ${audioState.currentMusicId === item.MUSICRID ? 'playing' : ''}`}
                      style={{
                        backgroundColor: audioState.currentMusicId === item.MUSICRID
                          ? token.colorPrimaryBg
                          : 'transparent',
                        borderBottom: `1px solid ${token.colorBorderSecondary}`,
                        padding: '6px 16px',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease'
                      }}
                      onDoubleClick={() => playSong(item)}
                    >
                      <List.Item.Meta
                        avatar={
                          <Avatar
                            shape="square"
                            size={40}
                            key={item.MUSICRID}
                            src={item.localCover
                              ? `file://${item.localCover}`
                              : (item.cover
                                ? `${LOCALHOST}/proxy?type=avatar&avatarUrl=${item.cover}`
                                : leiGod)}
                            style={{ border: `1px solid ${token.colorBorderSecondary}` }}
                          />
                        }
                        title={
                          <span
                            style={{
                              color: audioState.currentMusicId === item.MUSICRID
                                ? token.colorPrimary
                                : token.colorText,
                              fontWeight: audioState.currentMusicId === item.MUSICRID ? 'bold' : 'normal'
                            }}
                          >
                        {item.NAME || item.SONGNAME}
                      </span>
                        }
                        description={
                          <span style={{
                            color: audioState.currentMusicId === item.MUSICRID
                              ? token.colorPrimary
                              : token.colorTextSecondary
                          }}>
                        {item.ARTIST || '未知艺术家'}
                      </span>
                        }
                      />
                    </List.Item>
                  </Dropdown>
                )}
              />
              :
              <div className="playlist-empty">播放列表为空</div>
          }
        </div>
      </Drawer>
    </>
  )
}

export default Player