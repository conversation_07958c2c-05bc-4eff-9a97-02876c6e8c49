.music-player {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 85px;
  background-color: #181818;
  z-index: 1001;
  color: #fff;

  .player-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 6px;
    color: white;
    width: 100%;

    .song-info {
      display: flex;
      min-width: 0;
      max-width: 220px;
      flex-shrink: 0;
      width: 220px; /* 固定宽度，防止影响后续布局 */

      .song-avatar {
        margin-top: -6px;
        margin-left: 16px;
      }

      .song-details {
        display: flex;
        flex-direction: column;
        padding-left: 5px;
        min-width: 0;
        max-width: 140px;

        .song-title {
          font-size: 16px;
          margin-bottom: 4px;
          text-align: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 140px; /* 限制最大宽度，防止样式垮掉 */
        }

        .song-artist {
          text-align: left;
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 140px;
        }
      }
    }

    .player-controls {
      flex: 1;
      min-width: 0;
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      span {
        padding: 6px;
      }

      .music-time {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.4);
        display: flex;
        align-items: center;
        margin-right: 18px;
        text-align: left;
        min-width: 70px;
        max-width: 82px;
        flex-shrink: 0;
      }

      .music-actions {
        display: inline-flex;
        align-items: center;
        gap: 20px;
      }
    }

    .music-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 25px;
      margin-right: 20px;
      cursor: pointer;
      vertical-align: middle;
    }

    .prev-icon, .next-icon, .play-icon, .pause-icon {
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
      width: 8px;
      height: 8px;
      display: inline-block;
      vertical-align: middle;
    }

    .prev-icon {
      background-image: url('../../assets/imgs/prev.png');
      width: 2px;
      height: 2px;
    }

    .next-icon {
      background-image: url('../../assets/imgs/next.png');
      width: 3px;
      height: 3px;
    }

    .play-icon {
      background-image: url('../../assets/imgs/play.png');
    }

    .pause-icon {
      background-image: url('../../assets/imgs/pause.png');
    }
  }

  .player-extra {
    display: flex;
    align-items: center;
    min-width: 0;
    margin-right: 16px;
    flex-shrink: 0;

    .loop-icon, .volume-icon, .menu-icon {
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
      display: inline-block;
      vertical-align: middle;
      width: 22px;
      height: 22px;
    }

    .loop-icon {
      background-image: url('../../assets/imgs/list-loop.png');
      width: 25px;
      height: 25px;
    }

    .volume-icon {
      background-image: url('../../assets/imgs/volume.png');
    }

    .menu-icon {
      background-image: url('../../assets/imgs/play-list.png');
    }
  }
}

.song-details {
  min-width: 0;
  max-width: 140px;
}

// 歌词模态框样式
.lyrics-modal {
  width: 100vw;
  height: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  box-sizing: border-box;
  position: relative;
  background: #111; /* 两边统一为黑色 */
  transition: background 0.4s;
  overflow: hidden;
}

.lyrics-left {
  flex: 0.8;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: #111; /* 左侧背景也为黑色 */
  backdrop-filter: blur(10px);
  min-width: 220px;
}

.album-cover {
  margin-bottom: 30px;

  .ant-avatar {
    border: 4px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.song-info-modal {
  text-align: center;
  color: white;

  .song-title-modal {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
  }

  .song-artist-modal {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
  }
}

.lyrics-right {
  flex: 1.2;
  display: flex;
  flex-direction: column;
  padding: 40px 0 40px 0;
  background: #111; /* 右侧背景也为黑色 */
  position: relative;
}

.lyrics-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 0;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;

  &::-webkit-scrollbar {
    width: 6px;
    height: 0 !important; /* 禁用底部横向滚动条 */
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.lyrics-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  text-align: center;

  &::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 12px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.lyric-line {
  padding: 12px 20px;
  margin: 8px 0;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  transition: all 0.3s ease, color 0.2s;
  border-radius: 8px;
  cursor: pointer;

  &:hover {
    color: #fff;
  }

  &.active {
    color: #fff;
    font-weight: bold;
    font-size: 22px;
    background: none !important;
    text-shadow: none;
    letter-spacing: 0;
    border-radius: 0;
    box-shadow: none;
    filter: none;
    transition: all 0.3s ease, color 0.2s;
  }
}

// 右上角关闭按钮
.lyrics-close-btn {
  position: absolute;
  top: 24px;
  right: 32px;
  width: 35px;
  height: 35px;
  background: rgba(0, 0, 0, 0.18);
  border-radius: 50%;
  color: #fff;
  font-size: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);

  &:hover {
    background: rgba(255, 255, 255, 0.25);
    color: var(--ant-primary-color, #fff);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18);
  }
}

// 淡入淡出动画
.lyrics-fade-enter {
  opacity: 0;
  transform: scale(0.98);
  animation: lyricsFadeIn 0.4s forwards;
}

.lyrics-fade-exit {
  opacity: 1;
  transform: scale(1);
  animation: lyricsFadeOut 0.3s forwards;
}

@keyframes lyricsFadeIn {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes lyricsFadeOut {
  to {
    opacity: 0;
    transform: scale(1.02);
  }
}

// 响应式
@media (max-width: 768px) {
  .lyrics-modal {
    flex-direction: column;

    .lyrics-left {
      padding: 20px;

      .album-cover {
        .ant-avatar {
          width: 160px !important;
          height: 160px !important;
        }
      }

      .song-info-modal {
        .song-title-modal {
          font-size: 18px;
          max-width: 180px;
        }

        .song-artist-modal {
          font-size: 13px;
          max-width: 180px;
        }
      }
    }

    .lyrics-right {
      padding: 10px 0 10px 0;

      .lyrics-container {
        .lyric-line {
          font-size: 13px;
          padding: 8px 10px;

          &.active {
            font-size: 15px;
          }
        }
      }
    }
  }
}
