import React, { useEffect, useState, useRef } from 'react'
import { Table, Progress, Button, Space, Tabs, Modal, message } from 'antd'
import { theme } from 'antd'
import MusicList from '../MusicList'
import { downloadManager } from '@/utils/downloadManager'

const downloadingColumns = [
  {
    title: '歌曲',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    render: (text, record) => (
      <div>
        <div style={{ fontWeight: 500 }}>{text}</div>
        <div style={{ fontSize: 12, color: '#666' }}>{record.quality}</div>
      </div>
    ),
  },
  {
    title: '歌手',
    dataIndex: 'artist',
    key: 'artist',
    width: 120,
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    width: 300,
    render: (progress, record) => (
      <div style={{ minHeight: 60 }}>
        <Progress
          percent={progress}
          size="small"
          status={record.status === 'error' ? 'exception' : record.status === 'completed' ? 'success' : 'active'}
        />
        <div style={{
          fontSize: 12,
          color: '#666',
          marginTop: 4,
          minHeight: 16,
          display: 'flex',
          alignItems: 'center'
        }}>
          {record.status === 'downloading' && (
            <span>{record.downloadedSize || '0B'}/{record.totalSize || '--'}</span>
          )}
          {record.status === 'completed' && (
            <span>下载完成</span>
          )}
          {record.status === 'error' && (
            <span>下载失败</span>
          )}
          {record.status === 'paused' && (
            <span>已暂停</span>
          )}
        </div>
      </div>
    ),
  },
  {
    title: '大小',
    dataIndex: 'size',
    key: 'size',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    render: (_, record) => (
      <Space size="middle">
        {record.status === 'downloading' && (
          <Button type="link" size="small" onClick={() => record.onPause(record.key)}>
            暂停
          </Button>
        )}
        {record.status === 'paused' && (
          <Button type="link" size="small" onClick={() => record.onResume(record.key)}>
            继续
          </Button>
        )}
        <Button type="link" size="small" danger onClick={() => record.onCancel(record.key)}>
          取消
        </Button>
      </Space>
    ),
  },
]

const items = [
  {
    key: 0,
    label: '本地歌曲'
  },
  {
    key: 1,
    label: '正在下载'
  }
]

function DownLoad() {
  const [tabKey, setTabKey] = useState(0)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [downloadDir, setDownloadDir] = useState(null)
  const [localSongs, setLocalSongs] = useState([])
  const [currentMusicId, setCurrentMusicId] = useState(null)
  const [downloadingData, setDownloadingData] = useState([])
  const { token } = theme.useToken()

  const onChange = key => {
    setTabKey(key)
  }

  const handleOk = () => {
    setIsModalOpen(false)
  }

  const handlePlayLocalSong = item => {
    if (item.MUSICRID === currentMusicId) {
      window.audioAPI.seek(0)
      return
    }
    const songInfo = {
      NAME: item.NAME,
      ARTIST: item.ARTIST,
      ALBUM: item.ALBUM,
      MUSICRID: item.MUSICRID,
      DURATION: item.DURATION,
      cover: item.cover,
      isLocalPlay: true
    }
    // 构建文件URL
    const fileUrl = `file://${item.path}`
    try {
      // 播放歌曲
      window.audioAPI.play(fileUrl, 0, songInfo, item.MUSICRID)
      window.audioAPI.setCurrentMusicId(item.MUSICRID)
      setCurrentMusicId(item.MUSICRID)
    } catch (error) {
      console.error('播放命令发送失败:', error)
    }
  }

  // 获取下载目录
  useEffect(() => {
    const syncDownloadDir = async () => {
      if (window.audioAPI && window.audioAPI.getDownloadDir) {
        const dir = await window.audioAPI.getDownloadDir()
        setDownloadDir(dir)
      }
    }
    syncDownloadDir()
  }, [])

  // 监听下载管理器更新 - 确保在组件挂载时立即注册
  useEffect(() => {
    const handleDownloadsUpdate = (downloads) => {
      setDownloadingData(downloads)
    }
    downloadManager.addListener(handleDownloadsUpdate)
    // 立即获取当前下载状态并更新UI
    const currentDownloads = downloadManager.getDownloads()
    if (currentDownloads.length > 0) {
      handleDownloadsUpdate(currentDownloads)
    }

    return () => {
      downloadManager.removeListener(handleDownloadsUpdate)
    }
  }, [])

  // 监听下载进度和完成事件
  useEffect(() => {
    let unlistenProgress
    let unlistenDone
    if (window.audioAPI) {
      // 监听下载进度
      unlistenProgress = window.audioAPI.onDownloadProgress((progress) => {
        // 更新下载管理器中的进度
        if (progress.key && progress.progress !== undefined) {
          downloadManager.updateProgress(
            progress.key,
            progress.progress,
            progress.downloadedSize || '0B'
          )
        }
      })

      // 监听下载完成
      unlistenDone = window.audioAPI.onDownloadDone((info) => {
        console.log('下载完成事件', info)
        if (info.state === 'completed') {
          // 更新下载状态为完成
          if (info.key) {
            downloadManager.updateStatus(info.key, 'completed')
          }
          // 重新读取本地 download 目录下的所有歌曲
          refreshLocalSongs()
        } else {
          // 下载失败时只更新状态，不显示通知
          if (info.key) {
            downloadManager.updateStatus(info.key, 'error')
          }
        }
      })
    }

    // 在组件销毁时，调用清理函数
    return () => {
      if (unlistenProgress) unlistenProgress()
      if (unlistenDone) unlistenDone()
    }
  }, [])

  const refreshLocalSongs = async () => {
    const songsFromMain = await window.audioAPI.getLocalSongs()
    const formattedSongs = songsFromMain.map(song => {
      return {
        NAME: song.NAME,
        ARTIST: song.ARTIST,
        ALBUM: song.ALBUM,
        MUSICRID: song.MUSICRID,
        path: song.path,
        DURATION: song.DURATION,
        source: song.source,
        cover: song.cover,
        isLocalPlay: true
      }
    })
    setLocalSongs(formattedSongs)
    window.audioAPI.setPlayList(formattedSongs)
  }

  const handleChangeDir = async () => {
    const dir = await window.audioAPI.selectDownloadDir()
    if (dir) {
      setDownloadDir(dir)
    }
  }

  useEffect(() => {
    // 组件加载时获取本地歌曲列表
    refreshLocalSongs()
  }, [])

  // 用于同步播放状态
  useEffect(() => {
    if (window.audioAPI) {
      // 初始同步一次
      window.audioAPI.getState().then(state => {
        setCurrentMusicId(state.currentMusicId)
      })
      // 监听主进程的实时更新
      window.audioAPI.onStateUpdate((event, state) => {
        setCurrentMusicId(state.currentMusicId)
      })
    }
  }, [])

  return (
    <>
      <Tabs items={items} type="card" onChange={onChange}/>
      {
        tabKey === 0 && (
          <div>
            <MusicList
              data={localSongs}
              currentMusicId={currentMusicId}
              onPlay={handlePlayLocalSong}
              isShowToolbar={false}
              isShowEndMessage={false}
              token={token}
            />
          </div>
        )
      }
      {
        tabKey === 1 && (
          <div style={{ padding: '16px 0' }}>
            {downloadingData.filter(item => item.status !== 'completed').length === 0 ? (
              <div style={{
                textAlign: 'center',
                padding: '40px 0',
                color: '#999',
                fontSize: 14
              }}>
                暂无下载任务
              </div>
            ) : (
              <Table
                columns={downloadingColumns}
                dataSource={downloadingData.filter(item => item.status !== 'completed')}
                pagination={false}
                rowKey="key"
                scroll={{ x: 840 }}
                style={{ width: '100%' }}
              />
            )}
          </div>
        )
      }
      <Modal
        closable={{ 'aria-label': 'Custom Close Button' }}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={() => setIsModalOpen(false)}
        okText="确认"
        cancelText="取消"
      >
        <p>下载目录: {downloadDir}</p> <Button onClick={handleChangeDir}>更改</Button>
      </Modal>
    </>
  )
}

export default DownLoad