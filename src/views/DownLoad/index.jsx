import React, { useEffect, useState } from 'react'
import { List, Progress, Button, Tabs, Modal, ConfigProvider } from 'antd'
import { theme } from 'antd'
import MusicList from '../MusicList'
import { downloadManager } from '@/utils/downloadManager'
import './index.less'

// 渲染下载项
const renderDownloadItem = (item, token) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'downloading':
        return token.colorPrimary
      case 'completed':
        return token.colorSuccess
      case 'error':
        return token.colorError
      case 'paused':
        return token.colorWarning
      case 'queued':
        return token.colorTextSecondary
      default:
        return token.colorTextSecondary
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'downloading':
        return '下载中'
      case 'completed':
        return '已完成'
      case 'error':
        return '下载失败'
      case 'paused':
        return '已暂停'
      case 'queued':
        return '排队中'
      default:
        return '未知'
    }
  }

  return (
    <List.Item
      className="download-item"
      style={{
        borderBottom: `1px solid ${token.colorBorderSecondary}`,
        backgroundColor: token.colorBgContainer,
        boxShadow: `0 1px 3px ${token.colorBgMask}`
      }}
    >
      <div className="download-item-content">
        {/* 歌曲信息 */}
        <div className="song-info">
            <span
              className="song-name"
              style={{ color: token.colorText }}
            >
              {item.name} - {item.artist}
            </span>
          <span
            className="status-tag"
            style={{
              color: getStatusColor(item.status),
              backgroundColor: getStatusColor(item.status) + '10'
            }}
          >
              {getStatusText(item.status)}
            </span>
        </div>

        {/* 进度条 */}
        <div className="progress-container">
          <Progress
            percent={item.progress}
            size="small"
            status={item.status === 'error' ? 'exception' :
              item.status === 'completed' ? 'success' :
                item.status === 'queued' ? 'normal' : 'active'}
            showInfo={false}
          />
        </div>

        {/* 下载信息 */}
        <div
          className="download-info"
          style={{ color: token.colorTextSecondary }}
        >
          {item.status === 'downloading' && (
            `${item.downloadedSize || '0B'}/${item.totalSize || '--'}`
          )}
          {item.status === 'completed' && '下载完成'}
          {item.status === 'error' && '下载失败'}
          {item.status === 'paused' && '已暂停'}
          {item.status === 'queued' && '排队中...'}
        </div>

        {/* 文件大小 */}
        <div
          className="file-size"
          style={{ color: token.colorTextSecondary }}
        >
          {item.size}
        </div>
      </div>
    </List.Item>
  )
}

function DownLoad() {
  const [tabKey, setTabKey] = useState(0)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [downloadDir, setDownloadDir] = useState(null)
  const [localSongs, setLocalSongs] = useState([])
  const [currentMusicId, setCurrentMusicId] = useState(null)
  const [downloadingData, setDownloadingData] = useState([])
  const [activeDownloadsCount, setActiveDownloadsCount] = useState(0)
  const [queuedDownloadsCount, setQueuedDownloadsCount] = useState(0)
  const { token } = theme.useToken()

  // 若无封面，使用 hash 通过主进程补齐并写库
  const ensureCoverByHash = async (item) => {
    try {
      // 优先本地
      if (item?.localCover) return
      if (item?.cover) return
      if (!item?.hash || !window.audioAPI.fetchAndUpdateCoverByHash) return
      const coverPath = await window.audioAPI.fetchAndUpdateCoverByHash(item.hash)
      if (coverPath) {
        console.log('receive cover: ', coverPath)
        item.localCover = coverPath
      }
    } catch {
      // 静默失败
    }
  }

  const items = [
    {
      key: 0,
      label: `本地歌曲 (${localSongs.length})`
    },
    {
      key: 1,
      label: '正在下载'
    }
  ]

  // 设置滚动条主题样式
  useEffect(() => {
    const root = document.documentElement
    root.style.setProperty('--scrollbar-track-bg', token.colorBgContainer)
    root.style.setProperty('--scrollbar-thumb-bg', token.colorBorderSecondary)
    root.style.setProperty('--scrollbar-thumb-hover-bg', token.colorTextSecondary)
    root.style.setProperty('--playlist-bg', token.colorBgContainer)

  }, [token])

  const onChange = key => {
    setTabKey(key)
  }

  const handleOk = () => {
    setIsModalOpen(false)
  }

  const handlePlayLocalSong = async item => {
    if (item.MUSICRID === currentMusicId) {
      window.audioAPI.seek(0)
      return
    }

    console.log('handlePlayLocalSong')

    // 播放前补封面
    await ensureCoverByHash(item)

    // 设置本地歌曲播放列表
    const localPlayList = localSongs.map(song => ({
      ...song,
      isLocalPlay: true
    }))

    const songInfo = {
      ...item,
      isLocalPlay: true
    }

    // 构建文件URL
    const fileUrl = `file://${item.path}`
    try {
      // 播放歌曲时同时传递播放列表
      await window.audioAPI.play(fileUrl, 0, songInfo, item.MUSICRID, localPlayList)
      setCurrentMusicId(item.MUSICRID)
    } catch (error) {
      console.error('播放命令发送失败:', error)
    }
  }

  // 多选播放
  const handlePlaySelected = async selectedItems => {
    if (!selectedItems.length) return
    // 仅为首曲补封面，避免对整表逐一请求
    await ensureCoverByHash(selectedItems[0])
    console.log('多选播放', selectedItems)
    const list = selectedItems.map(item => {
      return {
        NAME: item.NAME || item.SONGNAME,
        ARTIST: item.ARTIST,
        ALBUM: item.ALBUM,
        cover: item.cover,
        localCover: item.localCover,
        duration: item.DURATION || 0,
        MUSICRID: item.MUSICRID,
        path: item.path,
        hash: item.hash,
        isLocalPlay: true
      }
    })
    try {
      const fileUrl = `file://${list[0].path}`
      // 从选中第一首开始播放
      const result = await window.audioAPI.play(fileUrl, 0, list[0], list[0].MUSICRID, list)
      if (!result.success) {
        console.error(result.message || '播放失败')
      }
    } catch (err) {
      console.error('获取音乐链接失败，请稍后再试')
    }
  }

  // 获取下载目录
  useEffect(() => {
    const syncDownloadDir = async () => {
      if (window.audioAPI && window.audioAPI.getDownloadDir) {
        const dir = await window.audioAPI.getDownloadDir()
        setDownloadDir(dir)
      }
    }
    syncDownloadDir()
  }, [])

  // 监听下载管理器更新 - 确保在组件挂载时立即注册
  useEffect(() => {
    const handleDownloadsUpdate = (downloads) => {
      setDownloadingData(downloads)
      // 更新活跃下载数和排队数
      setActiveDownloadsCount(downloadManager.getActiveDownloadsCount())
      setQueuedDownloadsCount(downloadManager.getQueuedDownloadsCount())
    }
    downloadManager.addListener(handleDownloadsUpdate)
    // 立即获取当前下载状态并更新UI
    const currentDownloads = downloadManager.getDownloads()
    if (currentDownloads.length > 0) {
      handleDownloadsUpdate(currentDownloads)
    }

    return () => {
      downloadManager.removeListener(handleDownloadsUpdate)
    }
  }, [])

  // 监听下载进度和完成事件
  useEffect(() => {
    let unlistenProgress
    let unlistenDone
    if (window.audioAPI) {
      // 监听下载进度
      unlistenProgress = window.audioAPI.onDownloadProgress((progress) => {
        // 更新下载管理器中的进度
        if (progress.key && progress.progress !== undefined) {
          downloadManager.updateProgress(
            progress.key,
            progress.progress,
            progress.downloadedSize || '0B'
          )
        }
      })

      // 监听下载完成
      unlistenDone = window.audioAPI.onDownloadDone((info) => {
        console.log('下载完成事件', info)
        if (info.state === 'completed') {
          // 更新下载状态为完成
          if (info.key) {
            downloadManager.updateStatus(info.key, 'completed')
          }
          // 重新读取本地 download 目录下的所有歌曲
          refreshLocalSongs()
        } else {
          // 下载失败时只更新状态，不显示通知
          if (info.key) {
            downloadManager.updateStatus(info.key, 'error')
          }
        }
      })
    }

    // 在组件销毁时，调用清理函数
    return () => {
      if (unlistenProgress) unlistenProgress()
      if (unlistenDone) unlistenDone()
    }
  }, [])

  const refreshLocalSongs = async () => {
    const songsFromMain = await window.audioAPI.getLocalSongs()
    const formattedSongs = songsFromMain.map(song => {
      return {
        ...song,
        isLocalPlay: true,
      }
    })
    setLocalSongs(formattedSongs)
  }

  const handleChangeDir = async () => {
    const dir = await window.audioAPI.selectDownloadDir()
    if (dir) {
      setDownloadDir(dir)
    }
  }

  useEffect(() => {
    // 组件加载时获取本地歌曲列表
    refreshLocalSongs()
    // 监听主进程的封面更新，保持本地列表的封面同步（切歌时由主进程下发）
    let unlistenCover = null
    if (window.coverAPI && window.coverAPI.onCoverUpdated) {
      unlistenCover = window.coverAPI.onCoverUpdated((payload) => {
        try {
          const { hash, localCover, MUSICRID } = payload || {}
          if (!hash && !MUSICRID) return
          setLocalSongs(prev => prev.map(s => {
            if ((s.hash && hash && s.hash === hash) || (MUSICRID && s.MUSICRID === MUSICRID)) {
              return { ...s, localCover }
            }
            return s
          }))
        } catch (e) { console.error('[DownLoad] cover-updated handler err', e) }
      })
    }
    return () => { if (unlistenCover) unlistenCover() }
  }, [])

  // 用于同步播放状态
  useEffect(() => {
    if (window.audioAPI) {
      // 初始同步一次
      window.audioAPI.getState().then(state => {
        setCurrentMusicId(state.currentMusicId)
      })
      // 监听主进程的实时更新
      window.audioAPI.onStateUpdate((event, state) => {
        setCurrentMusicId(state.currentMusicId)
      })
    }
  }, [])

  return (
    <div className="download-page">
      <ConfigProvider
        theme={{
          components: {
            Tabs: {
              horizontalMargin: 0
            }
          }
        }}
      >
        <Tabs items={items} type="card" onChange={onChange}/>
      </ConfigProvider>
      {
        tabKey === 0 && (
          <div>
            <MusicList
              data={localSongs}
              currentMusicId={currentMusicId}
              onPlay={handlePlayLocalSong}
              onPlaySelected={handlePlaySelected}
              onRefresh={refreshLocalSongs}
              token={token}
              isLocalPlay={true}
            />
          </div>
        )
      }
      {
        tabKey === 1 && (
          <div className="download-list-container">
            {/* 新增：显示下载状态信息 - 只在有任务时显示 */}
            {(activeDownloadsCount > 0 || queuedDownloadsCount > 0) && (
              <div
                className="download-status-info"
                style={{
                  backgroundColor: token.colorBgContainer,
                  border: `1px solid ${token.colorBorderSecondary}`,
                  color: token.colorTextSecondary
                }}
              >
                正在下载: {activeDownloadsCount}/3 | 排队中: {queuedDownloadsCount}
              </div>
            )}

            <div className="list-content">
              {downloadingData.filter(item => item.status !== 'completed').length === 0 ? (
                <div
                  className="empty-state"
                  style={{ color: token.colorTextSecondary }}
                >
                  暂无下载任务
                </div>
              ) : (
                <List
                  dataSource={downloadingData.filter(item => item.status !== 'completed')}
                  renderItem={(item) => renderDownloadItem(item, token)}
                  className="download-list-scrollbar"
                  style={{
                    height: '100%',
                    overflow: 'auto',
                    padding: '0 8px'
                  }}
                />
              )}
            </div>
          </div>
        )
      }
      <Modal
        closable={{ 'aria-label': 'Custom Close Button' }}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={() => setIsModalOpen(false)}
        okText="确认"
        cancelText="取消"
      >
        <p>下载目录: {downloadDir}</p> <Button onClick={handleChangeDir}>更改</Button>
      </Modal>
    </div>
  )
}

export default DownLoad