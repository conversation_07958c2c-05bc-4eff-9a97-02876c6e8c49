.download-page {
  .download-list-scrollbar {
    &::-webkit-scrollbar {
      width: 8px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 4px;
      background: var(--scrollbar-track-bg, #f5f5f5);
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background: var(--scrollbar-thumb-bg, #d9d9d9);
      &:hover {
        background: var(--scrollbar-thumb-hover-bg, #bfbfbf);
      }
    }
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb-bg, #d9d9d9) var(--scrollbar-track-bg, #f5f5f5);
  }

  .download-status-info {
    padding: 8px 12px;
    margin-bottom: 12px;
    border-radius: 6px;
    font-size: 14px;
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    font-size: 14px;
  }

  .download-item {
    margin-bottom: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    padding: 12px 16px;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .download-item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      gap: 12px;
      height: 32px;
    }

    .song-info {
      display: flex;
      align-items: center;
      min-width: 200px;
      max-width: 250px;
      flex: 0 0 auto;

      .song-name {
        font-weight: 500;
        font-size: 14px;
        margin-right: 12px;
        line-height: 32px;
        max-width: 180px;
        min-width: 180px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;
      }

      .status-tag {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        line-height: 1.2;
        display: flex;
        align-items: center;
        flex-shrink: 0;
      }
    }

    .progress-container {
      display: flex;
      align-items: center;
      flex: 1;
      max-width: 300px;
      height: 100%;

      .ant-progress {
        margin: 0;
        width: 100%;
      }
    }

    .download-info {
      font-size: 12px;
      min-width: 100px;
      text-align: right;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 100%;
    }

    .file-size {
      font-size: 12px;
      min-width: 60px;
      text-align: right;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 100%;
    }
  }

  .download-list-container {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .list-content {
    flex: 1;
    overflow: hidden;
  }
}