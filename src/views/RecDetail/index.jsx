import React, { useState, useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import axios from 'axios'
import { LOCALHOST } from '@/utils/constants'
import { MusicList } from '@/views'

function RecDetail() {
  const location = useLocation()
  const [data, setData] = useState([])
  const [currentMusicId, setCurrentMusicId] = useState('')

  const getRecDetail = async () => {
    if (!location.state) return
    try {
      const cachedData = localStorage.getItem(`recId-${location.state}`)
      if (cachedData) {
        try {
          const parsedData = JSON.parse(cachedData)
          setData(parsedData)
          return
        } catch (parseError) {
          console.error('解析缓存数据失败:', parseError)
        }
      }
      const res = await axios.get(LOCALHOST + '/recProxy', {
        params: {
          id: location.state,
          page: 1
        }
      })
      console.log(res.data)
      if (res.data) {
        const list = res.data?.musicList.map(item => {
          return {
            NAME: item.name,
            ARTIST: item.artist,
            ALBUM: item.album,
            cover: item.pic,
            pic: item.pic,
            DURATION: item.duration,
            MUSICRID: item.musicrid
          }
        })
        setData(list)
        localStorage.setItem(`recId-${location.state}`, JSON.stringify(list))
      }
    } catch (error) {
      console.error('获取推荐详情失败:', error)
    }
  }

  useEffect(() => {
    getRecDetail()
  }, [])

  return (
    <>
      <MusicList
        data={data}
        currentMusicId={currentMusicId}
        onPlay={item => {
          // setCurrentMusicId(item.MUSICRID)
          // playMusic(item)
        }}
        isShowToolbar={false}
      />
    </>
  )
}

export default RecDetail