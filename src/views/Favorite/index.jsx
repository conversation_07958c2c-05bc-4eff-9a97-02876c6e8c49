import React, { useEffect, useState } from 'react'
import axios from 'axios'
import { Tabs } from 'antd'
import MusicList  from '@/views/MusicList'

const items = [
  {
    key: '1',
    label: '单曲'
  },
  {
    key: '2',
    label: '歌单',
  },
  {
    key: '3',
    label: '专辑'
  }
]

const onChange = (key) => {
  console.log(key)
}

function Favorite() {
  const [favoriteData, setFavoriteData] = useState([])
  const [hasMore, setHasMore] = useState(false)
  const [loading, setLoading] = useState(false)

  // 模拟加载收藏数据
  const loadMoreData = () => {
    // 这里可以添加实际的加载逻辑
    console.log('加载更多收藏数据')
  }

  // 处理播放
  const handlePlay = (item) => {
    console.log('播放:', item)
  }

  return (
    <div>
      <Tabs defaultActiveKey="1" items={items} type="card" onChange={onChange}/>
      <MusicList
        data={favoriteData}
        hasMore={hasMore}
        loading={loading}
        loadMoreData={loadMoreData}
        onPlay={handlePlay}
        isShowToolbar={false}
      />
    </div>
  )
}

export default Favorite