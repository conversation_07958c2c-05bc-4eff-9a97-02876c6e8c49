import React, { useEffect, useState } from 'react'
import { Card, Col, Row } from 'antd'

import { data } from './initial.js'

function HoverCard({ item }) {
  const [hovered, setHovered] = useState(false)

  return (
    <Card
      title={item.title}
      variant="borderless"
      hoverable
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      cover={
        <div className="hover-card-wrapper">
          <img alt="img" src={item.img}
               style={{
                 transform: hovered ? 'scale(1.08)' : 'scale(1)',
                 opacity: hovered ? 0.85 : 1,
               }}
          />
          {hovered && (
            <span style={{
              transform: `translate(-50%, -50%) scale(${hovered ? 1.1 : 1})`,
              opacity: hovered ? 1 : 0,
            }}
            >
              <svg width="32" height="32" viewBox="0 0 1024 1024" fill="currentColor">
                <path d="M384 256l384 256-384 256z"/>
              </svg>
            </span>
          )}
        </div>
      }
      styles={{
        body: {
          padding: 6,
          height: 60,
        }
      }}
    >
      {item.content}
    </Card>
  )
}

function RecPlayList() {
  useEffect(() => {
  }, [])
  return (
    <div className="top">
      <Row gutter={[16, 16]} style={{ margin: 0 }}>
        {
          data.map((item, index) => (
            <Col key={index} xs={24} sm={12} md={4} lg={4} xl={4}
                 style={{ flex: '0 0 20%', maxWidth: '20%' }}
                 onClick={() => {
                   console.log('点击了', item.id)
                 }}
            >
              <HoverCard item={item}/>
            </Col>
          ))
        }
      </Row>
    </div>
  )
}

export default RecPlayList