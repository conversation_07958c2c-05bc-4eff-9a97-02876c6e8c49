import news from '@/assets/imgs/new.jpg'
import old from '@/assets/imgs/old.jpg'
import douyin from '@/assets/imgs/douyin.jpg'
import bumianye from '@/assets/imgs/bumianye.jpg'
import laonanren from '@/assets/imgs/laonanren.jpg'
import shangba from '@/assets/imgs/shangba.jpg'
import eng from '@/assets/imgs/eng.jpg'
import guofeng from '@/assets/imgs/guofeng.jpg'
import minyao from '@/assets/imgs/minyao.jpg'
import qingyinyue from '@/assets/imgs/qingyinyue.jpg'

export const data = [
  {
    id: '1082685104',
    img: news,
    content: '每日最新单曲推荐',
  },
  {
    id: '2996314807',
    img: old,
    content: '经典老歌大放送！一人一首代表作！',
  },

  {
    id: '3539687644',
    img: douyin,
    content: '抖音爆款丨2025热门歌曲',
  },

  {
    id: '3001661381',
    img: bumianye,
    content: '伤感情歌｜不眠夜里的孤单心事',
  },

  {
    id: '2802745654',
    img: laonanren,
    content: '【经典国语】老男人的情怀老男人的歌',
  },

  {
    id: '2508547657',
    img: shangba,
    content: '【伤感】对于你，我总是好了伤疤忘了疼',
  },
  {
    id: '2539910244',
    img: eng,
    content: '【燃爆】抖音中俘虏你耳朵的英文歌曲',
  },
  {
    id: '3014900006',
    img: guofeng,
    content: '国风精选篇：赠君一场千年梦',
  },
  {
    id: '2964988930',
    img: minyao,
    content: '民谣男声丨屏蔽纷纷扰扰',
  },
  {
    id: '2907225045',
    img: qingyinyue,
    content: '解压良药: 舒缓惬意的轻音乐',
  }
]