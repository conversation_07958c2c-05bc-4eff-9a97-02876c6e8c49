.bang-container {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  padding: 16px;
}

.cover-container {
  position: relative;
  cursor: pointer;
  overflow: hidden;
  border-radius: 8px;

  img {
    display: block;
    width: 100%;
    transition: transform 0.3s ease;
  }

  .play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    img {
      transform: scale(1.1);
    }

    .play-icon {
      opacity: 1;
    }
  }
}

.ant-card {
  border: none !important;
  background-color: transparent !important;
}

ul {
  list-style: none;
  padding: 8px 0;
  margin: 0;

  li {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    transition: background-color 0.2s;
    border-radius: 4px;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    .index {
      width: 20px;
      text-align: center;
      margin-right: 12px;
      font-weight: bold;
      color: #999;
    }

    .top-img-1,
    .top-img-2,
    .top-img-3 {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      background-size: contain;
      background-repeat: no-repeat;
    }

    .top-img-1 {
      background-image: url('../../assets/imgs/top1.webp');
    }

    .top-img-2 {
      background-image: url('../../assets/imgs/top2.webp');
    }

    .top-img-3 {
      background-image: url('../../assets/imgs/top3.webp');
    }

    .info {
      flex: 1;
      overflow: hidden;

      p {
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .song-name {
        font-size: 14px;
      }

      .artist {
        font-size: 12px;
        color: var(--ant-color-text-secondary, #888);
      }
    }
  }
}