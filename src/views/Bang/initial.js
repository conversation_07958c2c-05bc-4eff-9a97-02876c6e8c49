import reb from '@/assets/imgs/rgb.jpg'
import xgb from '@/assets/imgs/xgb.jpg'
import bsb from '@/assets/imgs/bsb.jpg'
import om from '@/assets/imgs/om.jpg'
import rhb from '@/assets/imgs/rhb.jpg'
import { LOCALHOST } from '@/utils/constants'

const initialData = [
  { img: reb, title: '热歌榜', content: [] },
  { img: xgb, title: '新歌榜', content: [] },
  { img: bsb, title: '飙升榜', content: [] },
  { img: om, title: '欧美榜', content: [] },
  { img: rhb, title: '日韩版', content: [] }
]

// 检查今天是否可以请求数据
function canRequestToday(key = 'lastRequestDate') {
  const today = new Date().toISOString().slice(0, 10)
  const lastDate = localStorage.getItem(key)
  if (lastDate === today) {
    return false // 今天已请求
  }
  localStorage.setItem(key, today)
  return true // 可以请求
}

export { initialData, canRequestToday, LOCALHOST }