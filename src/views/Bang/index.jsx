import React, { useState, useEffect } from 'react'
import { useNavigate, Outlet } from 'react-router-dom'
import { Card, Col, Row, Flex, Spin, message } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'
import axios from 'axios'
import './index.less'
import { initialData, LOCALHOST, canRequestToday } from './initial.js'

function Bang() {
  const navigate = useNavigate()
  const [data, setData] = useState(initialData)
  const [loading, setLoading] = useState(false)

  const bangClick = ({ id }) => {
    navigate('/home/<USER>/' + id)
  }

  const liClick = li => {
    console.log(li)
  }

  useEffect(() => {
    // 检查是否可以今天请求数据
    if (canRequestToday('lastRequestDate')) {
      setLoading(true)
      axios.get(LOCALHOST + '/proxy', { params: { type: 'bang' } })
        .then(res => {
          if (res.status === 200) {
            const doc = new DOMParser().parseFromString(res.data, 'text/html')
            const scripts = doc.querySelectorAll('script')
            let newData = [...initialData]
            for (let script of scripts) {
              if (!script.src) {
                const result = eval(script.innerHTML)
                const bang = result?.data[0]?.bang
                bang.forEach((bangItem, idx) => {
                  newData[idx].id = bangItem.id
                  newData[idx].content = bangItem.musicList.slice(0, 6).map(m => ({
                    artist: m.artist,
                    song_name: m.name,
                    rid: m.rid,
                    pic: m.pic,
                    pic120: m.pic120
                  }))
                })
              }
            }
            localStorage.setItem('arrData', JSON.stringify(newData))
            localStorage.setItem('lastRequestDate', new Date().toISOString().slice(0, 10))
            setData(newData)
          } else {
            message.open({
              type: 'error',
              content: '寄了',
            })
          }
        })
        .finally(() => {
          setLoading(false)
        })
    } else {
      const localArr = localStorage.getItem('arrData')
      if (localArr) {
        setData(JSON.parse(localArr))
      }
    }
  }, [])

  return (
    <div>
      {
        loading
          ?
          <Flex align="center" gap="middle">
            <Spin indicator={<LoadingOutlined spin/>} size="large"
                  style={{ position: 'absolute', top: '50%', left: '50%' }}/>
          </Flex>
          :
          <>
            <Row gutter={16} style={{ margin: 0 }}>
              {data.map((item, index) => (
                <Col span={24 / data.length} key={index} style={{ minWidth: 0, flex: '1 1 0%' }}>
                  <Card
                    hoverable
                    styles={{
                      body: {
                        padding: 0
                      }
                    }}
                    cover={
                      <div className="cover-container">
                        <img onClick={() => bangClick(item)}
                             alt={item.title}
                             src={item.img}
                             style={{ width: '100%' }}
                        />
                        <div className="play-icon">
                          <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                            <circle cx="20" cy="20" r="20" fill="rgba(0,0,0,0.4)"/>
                            <polygon points="16,13 28,20 16,27" fill="#fff"/>
                          </svg>
                        </div>
                      </div>
                    }
                  >
                    <ul>
                      {item.content.map((i, index) => (
                        <li key={index} onClick={() => liClick(i)}>
                          <div className={`${index > 2 && 'index'} top-img-${index + 1}`}>
                            {index > 2 && index + 1}
                          </div>
                          <div className="info">
                            <p className="song-name hover-effect" title={i.song_name}>{i.song_name}</p>
                            <p className="artist" title={i.artist}>{i.artist}</p>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </Card>
                </Col>
              ))}
            </Row>
            <Outlet/>
          </>
      }
    </div>
  )
}

export default Bang