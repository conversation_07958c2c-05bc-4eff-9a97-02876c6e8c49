import React, { useEffect, useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { Layout, Menu, ConfigProvider, theme } from 'antd'
import routes from '@/routers/config.jsx'
import './index.less'
import { downloadManager } from '@/utils/downloadManager'

import Player from '@/views/Player'
import Top from '@/views/Header'

const { Header, Sider, Content } = Layout

function MyLayout(props) {
  const { themeMode, setThemeMode } = props
  const navigate = useNavigate()
  const location = useLocation()
  const [isDownloading, setIsDownloading] = useState(false)

  // 监听下载状态变化
  useEffect(() => {
    const handleDownloadStatusChange = (downloading) => {
      setIsDownloading(downloading)
    }

    // 获取初始状态
    setIsDownloading(downloadManager.getDownloadStatus())
    
    // 添加状态监听器
    downloadManager.addStatusListener(handleDownloadStatusChange)

    return () => {
      downloadManager.removeStatusListener(handleDownloadStatusChange)
    }
  }, [])

  // 过滤出需要显示在菜单中的路由
  const items = routes
    .filter(route => !route.hidden && route.name)
    .map(route => ({
      key: route.path,
      label: route.name,
      icon: route.icon ? React.cloneElement(route.icon, {
        className: route.path === '/download' && isDownloading ? 'download-icon-animation' : ''
      }) : null,
    }))

  const onMenuClick = ({ key }) => {
    // 如果当前路径已经是目标路径的子路径，则不执行任何操作
    if (location.pathname.startsWith(key)) {
      return
    }
    navigate(key)
  }

  useEffect(() => {
    if (location.pathname === '/') {
      navigate('/home', { replace: true })
    }
  }, [location, navigate])

  return (
    <>
      <Layout style={{ minHeight: '100vh' }}>
        <Header style={{ backgroundColor: themeMode === 'light' ? '#fff' : '#141414', padding: 0, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Top themeMode={themeMode} setThemeMode={setThemeMode}/>
        </Header>
        <Layout>
          <Sider>
            <Menu
              theme="light"
              mode="inline"
              selectedKeys={[routes.some(r => r.path === location.pathname) ? location.pathname : '/home']}
              onClick={onMenuClick}
              items={items}
              style={{ height: '100%' }}
            />
          </Sider>
          <Content
            className="site-layout-background"
            style={{ padding: 24 }}
          >
            {props.children}
          </Content>
        </Layout>
      </Layout>
      <Player/>
    </>
  )
}

export default MyLayout
