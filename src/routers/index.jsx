import React, { Suspense } from 'react'
import { HashRouter as Router, Routes, Route } from 'react-router-dom'

import routes from './config.jsx'
import { MyLayout, Loading } from '../components'
import SearchDetail from '@/views/SearchDetail'

export default function App(props) {
  const { themeMode, setThemeMode } = props
  function renderRoute(route, key) {
    if (route.path === '/search') {
      return <Route key={key} path={route.path} element={<SearchDetail themeMode={themeMode} setThemeMode={setThemeMode} />} />
    }
    return route.children ? (
      <Route key={key} path={route.path} element={
        <Suspense fallback={<Loading/>}>
          <route.component/>
        </Suspense>
      }>
        {route.children.map((child, idx) => renderRoute(child, `${key}-${idx}`))}
      </Route>
    ) : (
      <Route key={key} path={route.path} element={
        <Suspense fallback={<Loading/>}>
          <route.component/>
        </Suspense>
      }/>
    )
  }

  return (
    <Router>
      <MyLayout themeMode={themeMode} setThemeMode={setThemeMode}>
        <Routes>
          {
            routes.map((item, i) => renderRoute(item, i))
          }
        </Routes>
      </MyLayout>
    </Router>
  )
}


