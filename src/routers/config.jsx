import React from 'react'
import { lazy } from 'react'
import { HeartOutlined, DownloadOutlined } from '@ant-design/icons'
import lister from '@/assets/imgs/lister.png'

const routes = [
  {
    path: '/home',
    name: '音乐',
    icon: <span style={{
      backgroundImage: `url(${lister})`,
      backgroundSize: 'contain',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center',
      width: 15,
      height: 15
    }}/>,
    component: lazy(() => import('../views/Home')),
    children: [
      {
        path: 'rec',
        name: '推荐',
        component: lazy(() => import('../views/RecPlayList'))
      },
      {
        path: 'rec/:id',
        name: '推荐详情',
        component: lazy(() => import('../views/RecDetail'))
      },
      {
        path: 'bang',
        name: '排行榜',
        component: lazy(() => import('../views/Bang'))
      },
      {
        path: 'bang/:id',
        name: '榜单详情',
        component: lazy(() => import('../views/BangDetail'))
      },
      {
        path: 'artist',
        name: '歌手',
        component: lazy(() => import('../views/Artist'))
      },
      {
        path: 'artist/:id',
        name: '歌手详情',
        component: lazy(() => import('../views/ArtistDetail'))
      }
    ]
  },
  {
    path: '/search',
    name: '搜索',
    hidden: true,
    component: lazy(() => import('../views/SearchDetail'))
  },
  {

    path: '/favorite',
    name: '我的收藏',
    icon: <HeartOutlined/>,
    component: lazy(() => import('../views/Favorite'))
  },
  {
    path: '/download',
    name: '本地与下载',
    icon: <DownloadOutlined/>,
    component: lazy(() => import('../views/DownLoad'))
  },
  {
    path: '/playList',
    name: '播放列表',
    hidden: true,
    component: lazy(() => import('../views/MusicList'))
  },
]

export default routes
