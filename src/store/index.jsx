import React, { createContext, useReducer } from 'react'

export const SEARCHMUSIC = 'SEARCHMUSIC'
export const MusicContext = createContext([])

const reducer = (state = [], action) => {
  switch (action.type) {
    case SEARCHMUSIC:
      return action.payload
    default:
      return state
  }
}

export const MusicStore = props => {
  const [state, dispatch] = useReducer(reducer, [])
  return (
    <MusicContext.Provider value={{state, dispatch}}>
      {props.children}
    </MusicContext.Provider>
  )
}
