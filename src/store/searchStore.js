import { create } from 'zustand'
import { persist } from 'zustand/middleware'

const useSearchStore = create(
  persist(
    (set) => ({
      searchTerm: '',
      setSearchTerm: (term) => set({ searchTerm: term }),
      searchData: [],
      setSearchData: (data) => set({ searchData: data }),
      searchPage: 0,
      setSearchPage: (page) => set({ searchPage: page }),
      lastSearchTerm: '',
      setLastSearchTerm: (term) => set({ lastSearchTerm: term }),
    }),
    {
      name: 'search-storage', // localStorage key
    }
  )
)

export default useSearchStore