import { useState, useEffect, useCallback } from 'react'
import { message } from 'antd'

/**
 * 收藏功能Hook
 * 提供收藏相关的状态管理和操作方法
 */
export const useFavorites = () => {
  const [favorites, setFavorites] = useState([])
  const [favoriteIds, setFavoriteIds] = useState(new Set())
  const [loading, setLoading] = useState(false)

  // 加载收藏列表
  const loadFavorites = useCallback(async () => {
    if (!window.audioAPI?.getFavorites) return
    
    setLoading(true)
    try {
      const result = await window.audioAPI.getFavorites()
      if (result.success) {
        setFavorites(result.data)
        // 创建ID集合用于快速查找
        const ids = new Set(result.data.map(item => item.musicrid))
        setFavoriteIds(ids)
      } else {
        console.error('获取收藏列表失败:', result.error)
      }
    } catch (error) {
      console.error('加载收藏列表失败:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  // 检查是否已收藏
  const isFavorite = useCallback((musicrid) => {
    return favoriteIds.has(musicrid)
  }, [favoriteIds])

  // 添加收藏
  const addFavorite = useCallback(async (songInfo) => {
    if (!window.audioAPI?.addFavorite) {
      message.error('收藏功能不可用')
      return false
    }

    if (!songInfo?.MUSICRID) {
      message.error('歌曲信息不完整')
      return false
    }

    try {
      const result = await window.audioAPI.addFavorite(songInfo)
      if (result.success) {
        // 更新本地状态
        const newFavorite = {
          musicrid: songInfo.MUSICRID,
          name: songInfo.NAME || songInfo.SONGNAME,
          artist: songInfo.ARTIST || '',
          album: songInfo.ALBUM || '',
          duration: songInfo.DURATION || 0,
          cover: songInfo.cover || '',
          source: songInfo.source || 'online',
          created_at: Date.now()
        }
        
        setFavorites(prev => [newFavorite, ...prev])
        setFavoriteIds(prev => new Set([...prev, songInfo.MUSICRID]))
        
        message.success('已添加到收藏')
        return true
      } else {
        message.error('收藏失败: ' + result.error)
        return false
      }
    } catch (error) {
      console.error('添加收藏失败:', error)
      message.error('收藏失败')
      return false
    }
  }, [])

  // 取消收藏
  const removeFavorite = useCallback(async (musicrid) => {
    if (!window.audioAPI?.removeFavorite) {
      message.error('收藏功能不可用')
      return false
    }

    try {
      const result = await window.audioAPI.removeFavorite(musicrid)
      if (result.success) {
        // 更新本地状态
        setFavorites(prev => prev.filter(item => item.musicrid !== musicrid))
        setFavoriteIds(prev => {
          const newSet = new Set(prev)
          newSet.delete(musicrid)
          return newSet
        })
        
        message.success('已取消收藏')
        return true
      } else {
        message.error('取消收藏失败: ' + result.error)
        return false
      }
    } catch (error) {
      console.error('取消收藏失败:', error)
      message.error('取消收藏失败')
      return false
    }
  }, [])

  // 切换收藏状态
  const toggleFavorite = useCallback(async (songInfo) => {
    if (!songInfo?.MUSICRID) return false
    
    const isCurrentlyFavorite = isFavorite(songInfo.MUSICRID)
    
    if (isCurrentlyFavorite) {
      return await removeFavorite(songInfo.MUSICRID)
    } else {
      return await addFavorite(songInfo)
    }
  }, [isFavorite, addFavorite, removeFavorite])

  // 批量检查收藏状态
  const checkMultipleFavorites = useCallback(async (musicrids) => {
    if (!window.audioAPI?.checkFavorite) return {}
    
    const results = {}
    try {
      await Promise.all(
        musicrids.map(async (musicrid) => {
          const result = await window.audioAPI.checkFavorite(musicrid)
          if (result.success) {
            results[musicrid] = result.isFavorite
          }
        })
      )
    } catch (error) {
      console.error('批量检查收藏状态失败:', error)
    }
    
    return results
  }, [])

  // 获取收藏统计
  const getFavoriteStats = useCallback(() => {
    return {
      total: favorites.length,
      online: favorites.filter(item => item.source === 'online').length,
      local: favorites.filter(item => item.source === 'local').length
    }
  }, [favorites])

  // 搜索收藏
  const searchFavorites = useCallback((keyword) => {
    if (!keyword) return favorites
    
    const lowerKeyword = keyword.toLowerCase()
    return favorites.filter(item => 
      item.name?.toLowerCase().includes(lowerKeyword) ||
      item.artist?.toLowerCase().includes(lowerKeyword) ||
      item.album?.toLowerCase().includes(lowerKeyword)
    )
  }, [favorites])

  // 初始化时加载收藏列表
  useEffect(() => {
    loadFavorites()
  }, [loadFavorites])

  return {
    // 状态
    favorites,
    favoriteIds,
    loading,
    
    // 操作方法
    loadFavorites,
    addFavorite,
    removeFavorite,
    toggleFavorite,
    isFavorite,
    checkMultipleFavorites,
    
    // 工具方法
    getFavoriteStats,
    searchFavorites
  }
}

export default useFavorites
