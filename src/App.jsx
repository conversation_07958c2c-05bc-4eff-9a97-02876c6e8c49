import React, { useEffect, useState } from 'react'
import { ConfigProvider, theme, App as AntdApp, Empty } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import './App.css'
import App from './routers'
import DownLoad from '@/views/DownLoad'
import { MusicStore } from '@/store'

function ViewManager() {
  const [themeMode, setThemeMode] = useState('dark') // 恢复 themeMode 的 state

  const view = () => {
    let type = new URLSearchParams(window.location.search).get('type')

    if (type === 'sub') {
      return (
        <ConfigProvider
          theme={{
            algorithm: theme.defaultAlgorithm,
            token: {
              colorPrimary: '#1890ff',
            },
          }}
        >
          {/* 这里可以保留 AntdApp 包装器以备后用 */}
          <AntdApp>
            <DownLoad/>
          </AntdApp>
        </ConfigProvider>
      )
    } else {
      return (
        <ConfigProvider
          theme={{
            algorithm: themeMode === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
            token: {
              colorPrimary: themeMode === 'dark' ? '#ff4d4f' : '#1890ff',
            },
          }}
          renderEmpty={() => {
            return (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE}/>
            )
          }}
          locale={zhCN}
        >
          <AntdApp>
            <MusicStore>
              {/* 移除 PlayerStoreProvider */}
              <App themeMode={themeMode} setThemeMode={setThemeMode}/>
            </MusicStore>
          </AntdApp>
        </ConfigProvider>
      )
    }
  }
  return <div className="app">{view()}</div>
}

export default ViewManager
