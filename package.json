{"name": "leigod", "private": true, "version": "1.0.0", "type": "commonjs", "main": "./out/main/index.js", "scripts": {"start": "electron-vite preview", "dev": "electron-vite dev", "prebuild": "electron-vite build", "preview": "vite preview"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "electron": "^36.5.0", "electron-vite": "^3.1.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "less": "^4.3.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "vite": "^6.3.5"}, "dependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "audio-metadata": "^0.3.0", "axios": "^1.10.0", "axios-cookiejar-support": "^6.0.2", "electron-store": "^10.1.0", "express": "^5.1.0", "fluent-ffmpeg": "^2.1.3", "https-proxy-agent": "^7.0.6", "music-metadata": "^11.7.1", "react-infinite-scroll-component": "^6.1.0", "react-window": "^1.8.10", "react-window-infinite-loader": "^1.0.9", "tough-cookie": "^5.1.2", "zustand": "^5.0.6"}}